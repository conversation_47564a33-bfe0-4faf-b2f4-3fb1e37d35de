# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock_renting
# 
# Translators:
# Wil Odoo, 2024
# Abe Manyo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s item)"
msgstr "%(product)s (%(qty)s item)"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s items)"
msgstr "%(product)s (%(qty)s items)"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Some products don't have the requested qty available for pickup\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Beberapa produk tidak memiliki kuantitas yang diminta tersedia untuk pickup\n"
"                        </span>"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Activate to use stock deliveries and receipts for rental orders"
msgstr ""
"Aktifkan untuk menggunakan pengiriman stok dan resi untuk pesanan rental"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered unavailable "
"prior to renting (preparation time)."
msgstr ""
"Jumlah waktu (dalam jam) di mana produk akan dianggap tidak tersedia sebelum"
" dirental (waktu persiapan)."

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_config_settings__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered "
"unavailableprior to renting (preparation time)."
msgstr ""
"Jumlah waktu (dalam jam) di mana produk akan dianggap tidak tersedia sebelum"
" dirental (waktu persiapan)."

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "Ketersediaan"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__qty_available
msgid "Available"
msgstr "Tersedia"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__available_reserved_lots
msgid "Available Reserved Lots"
msgstr "Lot-Lot Direservasi Tersedia"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available for Rent"
msgstr "Tersedia untuk Dirental"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Default Padding Time"
msgstr "Padding Time Default"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_sale_order_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Pastikan produk storable yang disimpan di gudang Anda dapat dilacak."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_lines_missing_stock
msgid "Has lines whose products have insufficient stock"
msgstr "Memiliki baris produk yang stoknya tidak mencukupi"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_tracked_lines
msgid "Has lines with tracked products"
msgstr "Memiliki baris dengan produk yang dilacak"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__rental_loc_id
msgid "In rent"
msgstr "Sedang dirental"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__is_available
msgid "Is Available"
msgstr "Apakah Tersedia"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__is_product_storable
msgid "Is Product Storable"
msgstr "Apakah Produk Storable"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_lot
msgid "Lot/Serial"
msgstr "Seri/Lot"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Aturan Stok Persediaan Minimum"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Minimum amount of time between two rentals"
msgstr "Jumlah waktu minimal di antara dua rental"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid ""
"No valid quant has been found in location %(location)s for serial number "
"%(serial_number)s!"
msgstr ""
"Tidak ada kuantitas valid yang ditemukan di lokasi %(location)s untuk nomor "
"seri %(serial_number)s!"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__padding_time
msgid "Padding"
msgstr "Padding"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__padding_time
msgid "Padding Time"
msgstr "Padding Time"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Pick-up/Kembalikkan produk"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickeable_lot_ids
msgid "Pickeable Lot"
msgstr "Pickeable Lot"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickedup_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__pickedup_lot_ids
msgid "Pickedup Lot"
msgstr "Pickedup Lot"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers picked up for the tracked products."
msgstr "Silakan tentukan nomor seri yang dipick up untuk produk yang dilacak."

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers returned for the tracked products."
msgstr ""
"Silakan tentukan nomor seri yang dikembalikkan untuk produk yang dilacak."

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_template
msgid "Product"
msgstr "Produk"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_product
msgid "Product Variant"
msgstr "Varian Produk"

#. module: sale_stock_renting
#: model:stock.route,name:sale_stock_renting.route_rental
msgid "Rental"
msgstr "Rental"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Laporan Analisis Rental"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "Jadwal Rental"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Rental Transfers"
msgstr "Transfer Rental"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "Rental move: %(order)s"
msgstr "Pergerakkan rental: %(order)s"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__group_rental_stock_picking
msgid "Rental pickings"
msgstr "Picking rental"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "RentalOrderLine transient representation"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__reserved_lot_ids
msgid "Reserved Lot"
msgstr "Lot Direservasi"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved lots unavailable"
msgstr "Lot yang direservasi tidak tersedia"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_return_picking
msgid "Return Picking"
msgstr "Return Picking"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returnable_lot_ids
msgid "Returnable Lot"
msgstr "Returnable Lot"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returned_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__returned_lot_ids
msgid "Returned Lot"
msgstr "Returned Lot"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Baris Pesanan Penjualan"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_template__preparation_time
msgid "Security Time"
msgstr "Security Time"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_report__lot_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__lot_id
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_report_search_view_inherit_lots
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_search_inherit_lots
msgid "Serial Number"
msgstr "Nomor Seri"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.report_rental_order_document
msgid "Serial Numbers"
msgstr "Nomor Seri"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_move
msgid "Stock Move"
msgstr "Pergerakan Stok"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,help:sale_stock_renting.field_product_template__preparation_time
msgid "Temporarily make this product unavailable before pickup."
msgstr "Untuk sementara buat produk ini tidak tersedia sebelum pickup."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_gantt_inherit_stock
msgid "The product is not available during this period."
msgstr "Produk tidak tersedia selama periode ini."

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__rental_loc_id
msgid ""
"This technical location serves as stock for products currently in rentalThis"
" location is internal because products in rentalare still considered as "
"company assets."
msgstr ""
"Lokasi teknis ini merupakan stok untuk produk yang saat ini dirental. Lokasi"
" ini bersifat internal karena produk dirental masih dianggap sebagai aset "
"perusahaan."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__tracking
msgid "Tracking"
msgstr "Pelacakan"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_template.py:0
msgid ""
"Tracking by lots isn't supported for rental products.\n"
"You should rather change the tracking mode to unique serial numbers."
msgstr ""
"Melacak berdasarkan lot tidak didukung untuk produk rental.\n"
"Anda harus mengganti mode pelacakan menjadi nomor seri unik."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__unavailable_lot_ids
msgid "Unavailable Lot"
msgstr "Lot Tidak Tersedia"

#. module: sale_stock_renting
#: model:res.groups,name:sale_stock_renting.group_rental_stock_picking
msgid "Use pickings for rental orders"
msgstr "Gunakan picking untuk pesanan rental"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Rentals"
msgstr "Lihat Rental"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__warehouse_id
msgid "Warehouse"
msgstr "Gudang"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "You cannot change the product of lines linked to stock moves."
msgstr ""
"Anda tidak dapat mengganti produk untuk baris yang di-link ke pergerakan "
"stok."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "hours"
msgstr "jam"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.product_template_inherit_view_form_stock_rental
msgid "hours before orders"
msgstr "jam sebelum pesanan"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "to"
msgstr "kepada"
