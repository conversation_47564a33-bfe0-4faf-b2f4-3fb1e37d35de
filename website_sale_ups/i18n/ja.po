# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_ups
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "(Bill My Account)"
msgstr "（マイアカウントに請求する）"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "<i class=\"fa fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/>"
msgstr "<i class=\"fa fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/>"

#. module: website_sale_ups
#: model_terms:payment.provider,pending_msg:website_sale_ups.payment_provider_ups_cod
msgid ""
"<i>Pending</i>, Thanks for choosing COD(Collect on Delivery/Cash on "
"Delivery) option. Delivery boy will collect the payment on delivery."
msgstr "<i>保留中</i>代金引換をお選び頂きありがとうございます。配達時に配達員が代金を回収いたします。"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "<span>(UPS Billing will remain to the customer)</span>"
msgstr "<span>（UPS請求が顧客に残ります）</span>"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Apply"
msgstr "適用"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Cancel"
msgstr "キャンセル"

#. module: website_sale_ups
#: model:ir.model.fields.selection,name:website_sale_ups.selection__payment_provider__custom_mode__cash_on_delivery
msgid "Cash On Delivery"
msgstr "代金引換"

#. module: website_sale_ups
#: model:payment.method,name:website_sale_ups.payment_method_cash_on_delivery
#: model:payment.provider,name:website_sale_ups.payment_provider_ups_cod
msgid "Cash on Delivery"
msgstr "代金引換"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Close"
msgstr "閉じる"

#. module: website_sale_ups
#: model:ir.model.fields,field_description:website_sale_ups.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "カスタムモード"

#. module: website_sale_ups
#: model:ir.model,name:website_sale_ups.model_payment_provider
msgid "Payment Provider"
msgstr "決済プロバイダー"

#. module: website_sale_ups
#: model:ir.model,name:website_sale_ups.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "UPS Bill My Account"
msgstr "UPS請求マイアカウント"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.property_ups_carrier_account_inherit_portal_details
msgid "UPS Number Account"
msgstr "UPSナンバーアカウント"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.res_config_settings_view_form
msgid "UPS Shipping Methods"
msgstr "UPS配送方法"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "Using Account"
msgstr "アカウントを利用する"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Your UPS Account Number"
msgstr "お客様のUPSアカウント番号"

#. module: website_sale_ups
#: model_terms:payment.provider,auth_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been authorized."
msgstr "お支払いは承認されました。"

#. module: website_sale_ups
#: model_terms:payment.provider,cancel_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been cancelled."
msgstr "支払が取消されました。"

#. module: website_sale_ups
#: model_terms:payment.provider,done_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been successfully processed."
msgstr "お支払いは正常に処理されました。"

#. module: website_sale_ups
#. odoo-python
#: code:addons/website_sale_ups/models/payment_provider.py:0
msgid "no UPS carriers available"
msgstr "利用可能なUPS運送会社がありません"
