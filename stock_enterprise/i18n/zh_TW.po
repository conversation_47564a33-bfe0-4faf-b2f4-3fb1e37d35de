# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_enterprise
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__assigned
msgid "Available"
msgstr "可用"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Average Cycle Time (Days)"
msgstr "平均周期時間（天）"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Average Delay (Days)"
msgstr "平均延誤時間（天）"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__company_id
msgid "Company"
msgstr "公司"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__creation_date
msgid "Creation Date"
msgstr "建立日期"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__outgoing
msgid "Customers"
msgstr "客戶"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Cycle Time"
msgstr "周期時間"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__cycle_time
msgid "Cycle Time (Days)"
msgstr "周期時間（天）"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Days"
msgstr "天內"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Delay"
msgstr "延遲"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__delay
msgid "Delay (Days)"
msgstr "延遲（天）"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__done
msgid "Done"
msgstr "完成"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Deliveries"
msgstr "已完成的交貨"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Receipts"
msgstr "已完成的收貨"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Transfers"
msgstr "已完成的調撥"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__scheduled_date
msgid "Expected Date"
msgstr "預計交貨日期"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__id
msgid "ID"
msgstr "識別號"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__internal
msgid "Internal"
msgstr "內部"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__is_late
msgid "Is Late"
msgstr "是延誤"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__is_backorder
msgid "Is a Backorder"
msgstr "是缺貨訂單"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__draft
msgid "New"
msgstr "新增"

#. module: stock_enterprise
#: model_terms:ir.actions.act_window,help:stock_enterprise.stock_report_action_performance
msgid "No data yet!"
msgstr "暫無資料！"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__operation_type_id
msgid "Operation Type"
msgstr "作業類型"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__partially_available
msgid "Partially Available"
msgstr "部分可用"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: stock_enterprise
#: model:ir.ui.menu,name:stock_enterprise.stock_dashboard_menuitem
msgid "Performance"
msgstr "效能"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_name
msgid "Picking Name"
msgstr "揀貨名稱"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__product_id
msgid "Product"
msgstr "商品"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__categ_id
msgid "Product Category"
msgstr "產品分類"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__product_qty
msgid "Product Quantity"
msgstr "數量"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_enterprise_move_tree_view
msgid "Quantity"
msgstr "數量"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__reference
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_map_view
msgid "Reference"
msgstr "編號"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_cohort_view
msgid "Report"
msgstr "報表"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_map_view
msgid "Scheduled Date"
msgstr "預定日期"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__state
msgid "Status"
msgstr "狀態"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Stock Overview"
msgstr "庫存概覽"

#. module: stock_enterprise
#: model:ir.model,name:stock_enterprise.model_stock_report
msgid "Stock Report"
msgstr "庫存報告"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__date_done
msgid "Transfer Date"
msgstr "轉帳日期"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_id
msgid "Transfer Reference"
msgstr "調撥參照"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_type_code
msgid "Type"
msgstr "類型"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__incoming
msgid "Vendors"
msgstr "供應商"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__waiting
msgid "Waiting Another Move"
msgstr "等待其它移動"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__confirmed
msgid "Waiting Availability"
msgstr "等待可用"

#. module: stock_enterprise
#: model:ir.actions.act_window,name:stock_enterprise.stock_report_action_performance
msgid "Warehouse Analysis"
msgstr "倉庫分析"
