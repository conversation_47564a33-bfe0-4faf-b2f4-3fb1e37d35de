<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_mx.diot_report" model="account.report">
        <field name="custom_handler_model_id" ref="model_l10n_mx_report_handler"/>
    </record>

    <!-- EXTRA COLUMNS  -->
    <record id="diot_third_party" model="account.report.column">
        <field name="name">Type of Third</field>
        <field name="expression_label">third_party_code</field>
        <field name="figure_type">string</field>
        <field name="sequence">1</field>
        <field name="report_id" ref="l10n_mx.diot_report"/>
    </record>
    <record id="diot_operation_type" model="account.report.column">
        <field name="name">Type of Operation</field>
        <field name="expression_label">operation_type_code</field>
        <field name="figure_type">string</field>
        <field name="sequence">2</field>
        <field name="report_id" ref="l10n_mx.diot_report"/>
    </record>
    <record id="diot_report_partner_vat_number" model="account.report.column">
        <field name="name">VAT</field>
        <field name="expression_label">partner_vat_number</field>
        <field name="figure_type">string</field>
        <field name="sequence">3</field>
        <field name="report_id" ref="l10n_mx.diot_report"/>
    </record>
    <record id="diot_report_country" model="account.report.column">
        <field name="name">Country</field>
        <field name="expression_label">country_code</field>
        <field name="figure_type">string</field>
        <field name="sequence">4</field>
        <field name="report_id" ref="l10n_mx.diot_report"/>
    </record>
    <record id="diot_report_partner_nationality" model="account.report.column">
        <field name="name">Nationality</field>
        <field name="expression_label">partner_nationality</field>
        <field name="figure_type">string</field>
        <field name="sequence">5</field>
        <field name="report_id" ref="l10n_mx.diot_report"/>
    </record>

    <!-- EXTRA EXPRESSIONS -->
    <record id="tax_report_mx_diot_third_party_code" model="account.report.expression">
        <field name="label">third_party_code</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">third_party_code</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
    <record id="tax_report_mx_diot_operation_type_code" model="account.report.expression">
        <field name="label">operation_type_code</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">operation_type_code</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
    <record id="tax_report_mx_diot_partner_vat_number" model="account.report.expression">
        <field name="label">partner_vat_number</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">partner_vat_number</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
    <record id="tax_report_mx_diot_country_code" model="account.report.expression">
        <field name="label">country_code</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">country_code</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
    <record id="tax_report_mx_diot_partner_nationality" model="account.report.expression">
        <field name="label">partner_nationality</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">partner_nationality</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
    <record id="tax_report_mx_diot_counter" model="account.report.expression">
        <field name="label">counter</field>
        <field name="engine">custom</field>
        <field name="formula">_report_custom_engine_diot_report</field>
        <field name="subformula">counter</field>
        <field name="report_line_id" ref="l10n_mx.diot_report_line"/>
    </record>
</odoo>
