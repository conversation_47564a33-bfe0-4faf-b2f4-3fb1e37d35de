# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid ""
" If checked, any change on this information will trigger a new computation "
"of the gross-->net salary."
msgstr "如果勾选此项，则该信息的任何更改都会触发重新计算工资总额->净额。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__signatures_count
msgid "# Signatures"
msgstr "# 签字"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "%(company)s: Job Offer - %(job_title)s"
msgstr "%(company)s：工作邀约 - %(job_title)s"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "%s manually set the Offer to Refused"
msgstr "%s手动将合同设置为拒绝"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,helper:hr_contract_salary.hr_contract_salary_personal_info_name
msgid "(Lastname Firstname)"
msgstr "(姓 名）"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "00.00.00-000.00"
msgstr "00.00.00-000.00"

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.<br/>\n"
"    The link will expire on the <t t-out=\"ctx.get('validity_end')\"/>.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/salary_package/simulation/offer/{{ctx.get('offer_id')}}\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.<br/>\n"
"    The link will expire on the <t t-out=\"ctx.get('validity_end')\"/>.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/salary_package/simulation/offer/{{ctx.get('offer_id')}}?token={{ctx.get('access_token')}}\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "<i class=\"fa fa-check\"/> Your message was successfully sent!"
msgstr "<i class=\"fa fa-check\"/>您的信息已成功发送！"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr "<i class=\"fa fa-check-circle-o mr8\"/>恭喜"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/>反馈"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/月</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/年</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text ml4\">Previous Contract</span>"
msgstr "<span class=\"o_stat_text ml4\">以前的合同</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr "<span class=\"o_stat_text\">合同</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Offers</span>"
msgstr "<span class=\"o_stat_text\">工作录用</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "<span class=\"o_stat_text\">Requested Signature</span>"
msgstr "<span class=\"o_stat_text\">要求签名</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Reviews</span>"
msgstr "<span class=\"o_stat_text\">回顾</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "<span class=\"o_stat_text\">Signed Contract</span>"
msgstr "<span class=\"o_stat_text\">签署的文档</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<span class=\"text-muted mr4 ml4\">|</span>"
msgstr "<span class=\"text-muted mr4 ml4\">|</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid ""
"<span>\n"
"                                    This field can only be modified by a server administrator, contact them if these field requires modifications.\n"
"                                </span>\n"
"                                <span invisible=\"requested_documents_fields_string\">\n"
"                                    There are currently no requested fields.\n"
"                                </span>"
msgstr ""
"<span>\n"
"                                    此字段只能由服务器管理员修改，如果需要修改这些字段，请联系他们。\n"
"                                </span>\n"
"                                <span invisible=\"requested_documents_fields_string\">\n"
"                                    目前没有要求修改的字段。\n"
"                                </span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "<span> days</span>"
msgstr "<span> 天</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ 月</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>days / year</span>"
msgstr "<span>天数/年</span>"

#. module: hr_contract_salary
#: model:ir.model.constraint,message:hr_contract_salary.constraint_hr_contract_salary_benefit_required_fold_res_field_id
msgid "A folded field is required"
msgstr "需要有一个折叠的字段"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_signatory.py:0
msgid ""
"A signatory role is unassigned. Please ensure there is a valid group or "
"person assigned to each role."
msgstr "签署人角色未指定。请确保为每个角色指定了一个有效的组或个人。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_needaction
msgid "Action Needed"
msgstr "所需操作"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Actions"
msgstr "操作"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__active
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__active
msgid "Active"
msgstr "有效"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_ids
msgid "Activities"
msgstr "活动"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Activity"
msgstr "活动"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation
msgid "Activity Creation"
msgstr "活动创建"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation_type
msgid "Activity Creation Type"
msgstr "活动创建类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_type_id
msgid "Activity Type"
msgstr "活动类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Add a line"
msgstr "添加行"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Add a section"
msgstr "添加小节"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__benefit_type_id
msgid "Allow to define the periodicity and output type of the advantage"
msgstr "允许定义优势的周期和输出类型"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__always
msgid "Always Selected"
msgstr "始终选择"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__always_show_description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__always_show_description
msgid "Always Show Description"
msgstr "始终显示说明"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"An %(offer)s has been sent by %(user)s to the applicant (mail: %(email)s)"
msgstr "%(user)s已将%(offer)s发送申请人（邮件：%(email)s)"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"An %(offer)s has been sent by %(user)s to the employee (mail: %(email)s)"
msgstr "%(user)s已将%(offer)s发送员工（邮件：%(email)s)"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_employee_report__final_yearly_costs
msgid "Annual Employee Budget"
msgstr "年度员工预算"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Annual Employer Cost"
msgstr "员工年度成本"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__applicant_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__applicant_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Applicant"
msgstr "申请人"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Applicants"
msgstr "申请人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__applies_on
msgid "Applies On"
msgstr "适用于"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Apply Now"
msgstr "现在申请"

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_clean_redundant_salary_data_ir_actions_server
msgid "Archive/Delete redundant generated salary data"
msgstr "存档/删除 多余工资数据"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Archived"
msgstr "已归档"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/binary_field_contract.js:0
msgid "Are you sure you want to delete this file permanently ?"
msgstr "您确定要永久删除该文件吗？"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_responsible_id
msgid "Assigned to"
msgstr "分派给"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_bachelor
msgid "Bachelor"
msgstr "本科"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__bank_account
msgid "Bank Account"
msgstr "银行账户"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_acc_number
msgid "Bank account"
msgstr "银行账户"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__benefit_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__benefit_ids
msgid "Benefit"
msgstr "利益"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__benefit_display_type
msgid "Benefit Display Type"
msgstr "福利显示类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__res_field_public
msgid "Benefit Field"
msgstr "福利字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__benefit_type_id
msgid "Benefit Type"
msgstr "福利类型"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_benefit_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_benefit
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Benefits"
msgstr "优点"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_benefit.py:0
msgid "Benefits that are not linked to a field should always be displayed."
msgstr "应始终显示与字段无关的效益。"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_birthdate
msgid "Birthdate"
msgstr "生日"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_candidate
msgid "Candidate"
msgstr "应聘人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__category_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
msgid "Category"
msgstr "类别"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_certificate
msgid "Certificate"
msgstr "证书"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "Changes summary since previous contract:"
msgstr "自上一份合约以来的变更汇总："

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__checkbox
msgid "Checkbox"
msgstr "复选框"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__child_ids
msgid "Child"
msgstr "子级"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation
msgid ""
"Choose when the activity is created:\n"
"- Employee signs his contract: Activity is created as soon as the employee signed the contract\n"
"- Contract is countersigned: HR responsible have signed the contract and conclude the process."
msgstr ""
"选择创建活动的时间：\n"
"- 员工签署合同：员工签署合同后立即创建活动\n"
"- 合约会签： 人力资源负责人签署合约并关闭流程。"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_city
msgid "City"
msgstr "城市"

#. module: hr_contract_salary
#: model:sign.template,redirect_url_text:hr_contract_salary.sign_template_cdi_developer
msgid "Close"
msgstr "关闭"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "代码"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Company"
msgstr "公司"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
msgid "Confirm"
msgstr "确认"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contact"
msgstr "联系"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contact Email"
msgstr "联系EMail"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contract"
msgstr "合同"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit_type
msgid "Contract Benefit Type"
msgstr "合同福利类型"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit_value
msgid "Contract Benefit Value"
msgstr "合同效益值"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Contract Information:"
msgstr "合同信息："

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__res_field_id
msgid "Contract Related Field"
msgstr "合约相关字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__sign_role_id
msgid "Contract Role"
msgstr "合同角色"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_signatory
msgid "Contract Signatories"
msgstr "合同签署"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__contract_start_date
msgid "Contract Start Date"
msgstr "合同开始日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__contract_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__contract_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job__default_contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Contract Template"
msgstr "合同模板"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
#: model:ir.ui.menu,name:hr_contract_salary.hr_recruitment_menu_contract_templates
msgid "Contract Templates"
msgstr "合同模板"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Contract Type"
msgstr "合同类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contract Update"
msgstr "合同更新"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid "Contract Update Document Template"
msgstr "合同更新文档模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_signatories_ids
msgid "Contract Update Signatories"
msgstr "合同更新签署"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__contract
msgid "Contract Value"
msgstr "合同价值"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "合同和员工分析报表"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__manual_res_field_id
msgid "Contract field used to manually encode an benefit value."
msgstr "合同字段，用于人工编码福利值。"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_history
msgid "Contract history"
msgstr "合同历史"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation__countersigned
msgid "Contract is countersigned"
msgstr "合同已加签"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_recruitment_config_contract_templates
msgid "Contracts"
msgstr "合同"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contracts Reviews"
msgstr "合同评审"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_id
msgid "Cost Field"
msgstr "成本字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_public
msgid "Cost Field (Public)"
msgstr "成本字段（公开）"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_field
msgid "Cost Field Name"
msgstr "成本字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__country
msgid "Countries"
msgstr "国家/地区"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__country_id
msgid "Country"
msgstr "国家/地区"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Country of Birth"
msgstr "国籍"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid "Create new offer"
msgstr "创建新邀约"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__hash_token
msgid "Created From Token"
msgstr "从令牌创建"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__create_uid
msgid "Created by"
msgstr "创建人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__create_date
msgid "Created on"
msgstr "创建日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__currency_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__currency
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__currency
msgid "Currency"
msgstr "币别"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr "定制您的工资"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__date
msgid "Date"
msgstr "日期"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__days
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__days
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_benefits
msgid "Days"
msgstr "天"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__access_token_validity
msgid "Default Access Token Validity Duration"
msgstr "默认访问令牌的有效期限"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__employee_salary_simulator_link_validity
msgid "Default Salary Configurator Link Validity Duration For Employees"
msgstr "员工的默认薪资配置链接有效期"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job__default_contract_id
msgid ""
"Default contract used to generate an offer. If empty, benefits will be taken"
" from current contract of the employee/nothing for an applicant."
msgstr "用于生成要约的默认合同。如果为空，则将从员工的当前合同中提取福利；如果为申请人，则不提取任何福利。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr "向申请人发出聘用通知时使用的默认合同。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr "默认文档，即申请人接受合约时必须签署的文档。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr "默认文档，即员工为更新合约应当签署的文档。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation_type
msgid ""
"Define when the system creates a new activity:\n"
"- When the benefit is set: Unique creation the first time the employee will take the benefit\n"
"- When the benefit is modified: Activity will be created for each change regarding the benefit."
msgstr ""
"定义系统何时创建新活动：\n"
"- 设定福利时：员工首次享受福利时的唯一创建时间\n"
"- 修改福利时：每次更改福利时都会创建活动。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_frenquency
msgid ""
"Define when the system creates a new sign request:\n"
"- When the benefit is set: Unique signature request the first time the employee will take the benefit\n"
"- When the benefit is modified: Signature request will be created for each change regarding the benefit."
msgstr ""
"定义系统创建新标识申请的时间：\n"
"- 设定福利时：员工首次领取福利时的唯一签名请求\n"
"- 更改福利时：每次更改福利时都会创建签名请求。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Department"
msgstr "部门"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__name
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Description"
msgstr "描述"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
msgid "Details"
msgstr "详情"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid ""
"Did you know that you can create an offer for any applicant?<br>\n"
"                Why don't you try? They're listed"
msgstr ""
"您知道您可以为任何申请者创建录用邀约吗？<br>\n"
"                不妨试试？它们已列出"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Discard"
msgstr "丢弃"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_type
msgid "Display Type"
msgstr "显示类型"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_doctor
msgid "Doctor"
msgstr "博士"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__document
msgid "Document"
msgstr "单据"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_template_id
msgid ""
"Documents selected here will be requested to the employee for additional "
"signatures related to the benefit. eg: A company car policy to approve if "
"you choose a company car."
msgstr "在此选择的文件将要求员工签署与福利相关的附加文件：如果您选择公司用车，则需要批准公司用车政策。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "没有要签署的文档"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"要签署的文件。只有有1个或2个不同负责人的文件可以选择。\n"
"        只有一个负责人的文件必须由员工签字，而有两个不同负责人的文件必须由员工和负责人签字。\n"
"        "

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Doesn't impact net salary"
msgstr "不影响净薪"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__dropdown
msgid "Dropdown"
msgstr "下拉"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__dropdown-group
msgid "Dropdown Group"
msgstr "下拉组"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_email
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__email
msgid "Email"
msgstr "电子邮件"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_copy_partner_id
msgid "Email address to which to transfer the signature."
msgstr "转移签字的电子邮件地址。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__employee
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__employee
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Employee"
msgstr "员工"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_contract_id
msgid "Employee Contract"
msgstr "员工合同"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_job_id
msgid "Employee Job"
msgstr "员工工作"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Employee Name"
msgstr "员工姓名"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_image_1920
msgid "Employee Photo"
msgstr "员工照片"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation__running
msgid "Employee signs his contract"
msgstr "员工签署合同"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer
msgid "Employee: Contract And Salary Package"
msgstr "员工：合同以及工资包"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Employees"
msgstr "员工"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__final_yearly_costs
msgid "Employer Budget"
msgstr "雇主预算"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"Equals to the sum of the following values:\n"
"\n"
"%s"
msgstr ""
"等于以下数值之和: \n"
"\n"
"%s"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information_input
msgid "Existing file:"
msgstr "现有文件："

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__expired
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Expired"
msgstr "过期"

#. module: hr_contract_salary
#: model:hr.contract.salary.benefit,name:hr_contract_salary.benefit_extra_time_off
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__holidays
msgid "Extra Time Off"
msgstr "额外的休息时间"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_female
msgid "Female"
msgstr "女性"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__field
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__field
msgid "Field Name"
msgstr "字段名称"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_field
msgid "Field of Study"
msgstr "研究领域"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__fixed_value
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__fixed
msgid "Fixed Value"
msgstr "固定值"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_res_field_id
msgid "Fold Condition"
msgstr "折叠条件"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_field
msgid "Fold Field Name"
msgstr "折叠字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_label
msgid "Fold Label"
msgstr "折叠标签"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__folded
msgid "Folded"
msgstr "已折叠"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__full_signed
msgid "Fully Signed"
msgstr "完全签署"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_gender
msgid "Gender"
msgstr "性别"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Generate Offer"
msgstr "生成录取书"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Group By"
msgstr "分组方式"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__hr
msgid "HR Responsible"
msgstr "HR 负责人"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"HR Responsible %s should be a user of Sign when New Contract Document "
"Template is specified"
msgstr "指定新合同文件模板时，人力资源负责人%s应是签署用户"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"HR Responsible %s should have a valid email address when New Contract "
"Document Template is specified"
msgstr "指定新合同文件模板时，人力资源负责人%s应有有效的电子邮件地址"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__has_admin_access
msgid "Has Admin Access"
msgstr "有管理员访问权限"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__has_message
msgid "Has Message"
msgstr "有消息"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__helper
msgid "Helper"
msgstr "助手"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Hide <i class=\"oi oi-chevron-up\"/>"
msgstr "隐藏<i class=\"oi oi-chevron-up\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide Children"
msgstr "隐藏子级"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide children personal info when checked."
msgstr "检查时隐藏儿童的个人信息。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__icon
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_error
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid ""
"If checked, the value of this information will be computed in all "
"information set as Monthly Total"
msgstr "如果选中，该信息的值将在所有信息集中计算为 \"月度总计\""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__always_show_description
msgid "If unchecked, Description will only be shown when Benefit is selected"
msgstr "如果未选中，则只有在选择 “福利” 时才会显示“说明”"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920
msgid "Image"
msgstr "图像"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920_filename
msgid "Image 1920 Filename"
msgstr "图像 1920 文件名"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid "Impacts Monthly Total"
msgstr "每月影响总数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid "Impacts Net Salary"
msgstr "影响净薪"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__open
msgid "In Progress"
msgstr "进行中"

#. module: hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:hr_contract_salary.benefit_extra_time_off
msgid ""
"In addition to your legal leaves, you can chose up to 30 extra days off.\n"
"            The amount of annual time off (legal leaves) you get depends on your work schedule in the previous year. A full-time work schedule through the 12 months of the last year, under your contract, will grant you 20 annual time off (legal leaves)."
msgstr ""
"除法定假期外，您还可以选择多达 30 天的额外假期。\n"
"            您可获得的年假（法定假期）天数取决于您上一年的工作安排。根据合约，如果上一年全职工作满 12 个月，您可获得 20 天年假（法定假期）。"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid ""
"In order to choose %s, first you need to choose:\n"
" %s"
msgstr "为了选择%s，首先您需要选择：%s"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__info_type_id
msgid "Info Type"
msgstr "信息类型"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "Information"
msgstr "信息"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__integer
msgid "Integer"
msgstr "整数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__is_required
msgid "Is Required"
msgstr "必填项"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__is_origin_contract_template
msgid "Is origin contract a contract template?"
msgstr "原始合约是合约模板吗？"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model,name:hr_contract_salary.model_hr_job
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Job Position"
msgstr "工作岗位"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__job_title
msgid "Job Title"
msgstr "工作头衔"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__lang
msgid "Languages"
msgstr "语言"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Let's create one"
msgstr "让我们创建一个"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__display_type__line
msgid "Line"
msgstr "行"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__url
msgid "Link"
msgstr "链接"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__left
msgid "Main Panel"
msgstr "主面板"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_male
msgid "Male"
msgstr "男性"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__benefit_ids
msgid "Mandatory Benefits"
msgstr "强制性福利"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__manual_field
msgid "Manual Field Name"
msgstr "手动字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__manual
msgid "Manual Input"
msgstr "手工输入"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__manual_res_field_id
msgid "Manual Res Field"
msgstr "手动资源字段"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_master
msgid "Master"
msgstr "主版本"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_ids
msgid "Messages"
msgstr "消息"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_personal_info.py:0
msgid ""
"Mismatch between res_field_id %(field)s and model %(model)s for info "
"%(personal_info)s"
msgstr "信息%(personal_info)s的res_field_id %(field)s和模型%(model)s不匹配"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_type__periodicity__monthly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__monthly
msgid "Monthly"
msgstr "每月"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Cost"
msgstr "每月费用"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Monthly Cost (Real)"
msgstr "每月费用(实际)"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Monthly Gross Salary"
msgstr "月工资总额"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__monthly_total
msgid "Monthly Total"
msgstr "每月总数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "月工资"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__name
msgid "Name"
msgstr "名称"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Name of the field related to this personal info."
msgstr "与此个人信息相关的字段名称。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Name or email..."
msgstr "姓名或电子邮箱地址⋯"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "National Identification Number"
msgstr "公民身份证号码"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_id
msgid "Nationality"
msgstr "国籍"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr "净计算"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "New Contract"
msgstr "新合约"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid "New Contract Document Template"
msgstr "新增的合同文档模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_id
msgid "New Contract Template"
msgstr "新合同模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一活动日历事件"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "No"
msgstr "否"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr "在工作岗位中没有定义人力资源主管。请与管理员联系。"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "No Template found"
msgstr "未找到模板"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/wizard/refuse_offer_wizard.py:0
msgid "No offer selected"
msgstr "未选择聘用邀约"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"No signature template defined on the contract. Please contact the HR "
"responsible."
msgstr "该合同没有签名模板。请与人力资源主管联系。"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__selector_highlight__none
msgid "None"
msgstr "无"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Not a valid e-mail address"
msgstr "无效的电子邮件地址"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Not a valid input in integer field"
msgstr "在整数字段不是一个有效的输入"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__holidays
msgid "Number of days of paid leaves the employee gets per year."
msgstr "员工每年获得的带薪休假天数。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的信息数量"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__offer_create_date
msgid "Offer Create Date"
msgstr "聘用邀约建立日期"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_refusal_reasons
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_contract_salary_offer_refusal_reasons
msgid "Offer Refusal Reasons"
msgstr "聘用邀约拒绝原因"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__offer_end_date
msgid "Offer Validity Date"
msgstr "录用通知有效期"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Offer for %(recipient)s"
msgstr " %(recipient)s的优惠信息"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"Offer link can not be send. The applicant needs to have a name and email."
msgstr "无法发送工作邀约链接。申请人需要有姓名和电子邮件。"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_action
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_recruitment_action
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_contract_salary_job_offer
#: model:ir.ui.menu,name:hr_contract_salary.menu_salary_package_offer
msgid "Offers"
msgstr "定价"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Oops"
msgstr "哎呀"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "Origin Contract"
msgstr "原始合同"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Original Link"
msgstr "原链接"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__originated_offer_id
msgid "Originated Offer"
msgstr "原始优惠"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_other
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_other
msgid "Other"
msgstr "其他"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "PDF Template"
msgstr "PDF 模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__parent_id
msgid "Parent"
msgstr "上级"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__half_signed
msgid "Partially Signed"
msgstr "部分签署"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__partner_id
msgid "Partner"
msgstr "合作伙伴"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__applicant_name
msgid "Partner Name"
msgstr "合作伙伴名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__percent
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__percent
msgid "Percent"
msgstr "百分比"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__periodicity
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__periodicity
msgid "Periodicity"
msgstr "周期"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_contact
msgid "Person to call"
msgstr "联络人"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Personal Documents"
msgstr "个人文件"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_personal_info_action
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__personal_info_id
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_personal_info
msgid "Personal Info"
msgstr "个人信息"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Personal Information"
msgstr "个人信息"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_phone
msgid "Phone Number"
msgstr "电话号码"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_phone
msgid "Phone number"
msgstr "电话号码"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__category_id
msgid "Pick a category to display this information"
msgstr "选择一个类别来显示该信息"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__value_type
msgid ""
"Pick how the value of the information is computed:\n"
"Fixed value: Set a determined value static for all links\n"
"Contract value: Get the value from a field on the contract record\n"
"Payslip value: Get the value from a field on the payslip record\n"
"Sum of Benefits value: You can pick in all benefits and compute a sum of them\n"
"Monthly Total: The information will be a total of all the informations in the category Monthly Benefits"
msgstr ""
"选择计算信息值的方式：\n"
"固定值：为所有链接设置一个固定值\n"
"合同值：从合同记录的字段中获取值\n"
"工资单值：从工资单记录的字段中获取值\n"
"津贴总和值：您可以选择所有福利并计算其总和\n"
"月度总计：该信息将是月度津贴类别中所有信息的总计"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_place_of_birth
msgid "Place of Birth"
msgstr "出生地"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__placeholder
msgid "Placeholder"
msgstr "占位符"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__position
msgid "Position"
msgstr "位置"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Previous Contract"
msgstr "前合同"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_private_license_plate
msgid "Private License Plate"
msgstr "私人牌照"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr "草拟合同"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_reviews_count
msgid "Proposed Contracts Count"
msgstr "草拟的合同数"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__radio
msgid "Radio"
msgstr "单选"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__radio
msgid "Radio Buttons"
msgstr "单选按钮"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__rating_ids
msgid "Ratings"
msgstr "点评"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
msgid "Recompute"
msgstr "重新计算"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer_applicant
msgid "Recruitment: Your Salary Package"
msgstr "招聘：工资包"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__selector_highlight__red
msgid "Red"
msgstr "红色"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__refusal_date
msgid "Refusal Date"
msgstr "拒绝日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__refusal_reason
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__refusal_reason
msgid "Refusal Reason"
msgstr "拒绝原因"

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.action_refuse_salary_offer
msgid "Refuse"
msgstr "拒绝"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.open_refuse_wizard
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
msgid "Refuse Offer"
msgstr "拒绝聘用邀约"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_refusal_reasons_view_tree
msgid "Refuse Reason"
msgstr "拒绝理由"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_refuse_offer_wizard
msgid "Refuse an Offer"
msgstr "拒绝聘用邀约"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__refused
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Refused"
msgstr "已拒绝"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Related Field"
msgstr "关联字段"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
msgid "Related Model"
msgstr "关联模型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents_fields_string
msgid "Requested Documents"
msgstr "要求的文档"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents_field_ids
msgid "Requested Documents (IDs)"
msgstr "要求的文件 ( 身份证明 )"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents
msgid "Requested Documents Fields"
msgstr "要求的文档字段"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Requested Signature"
msgstr "要求签名"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__sign_request_ids
msgid "Requested Signatures"
msgstr "请签字"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_model
msgid "Res Model"
msgstr "Res 模型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_resume_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_resume
msgid "Resume"
msgstr "恢复"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Review Contract &amp; Sign"
msgstr "审查合同和签署"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_payroll_structure_type__salary_benefits_ids
msgid "Salary Benefits"
msgstr "工资福利"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Salary Configurator"
msgstr "工资配置器"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Salary Configurator Display"
msgstr "薪资配置器显示"

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_update_offer_state_ir_actions_server
msgid "Salary Configurator: Update Offer State"
msgstr "薪资配置工具：更新聘用通知状态"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__salary_offer_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__salary_offer_ids
msgid "Salary Offer"
msgstr "薪资待遇"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_offer_refusal_reason
msgid "Salary Offer Refusal Reasons"
msgstr "薪酬聘用邀约拒绝原因"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__salary_offers_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_candidate__salary_offers_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__salary_offers_count
msgid "Salary Offers Count"
msgstr "薪资待遇计算"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_tree
msgid "Salary Package Benefit"
msgstr "薪酬福利"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_menu
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Salary Package Configurator"
msgstr "工资包配置"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_offer
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Salary Package Offer"
msgstr "薪资待遇"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_tree
msgid "Salary Package Offers"
msgstr "薪资待遇"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_tree
msgid "Salary Package Personal Info"
msgstr "工资包 个人信息"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_type
msgid "Salary Package Personal Info Type"
msgstr "工资包 个人信息类型"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_value
msgid "Salary Package Personal Info Value"
msgstr "工资包 个人信息价值"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr "工资包"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume_category
msgid "Salary Package Resume Category"
msgstr "工资包 简历类别"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Salary Package Summary"
msgstr "工资包摘要"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__structure_type_id
msgid "Salary Structure Type"
msgstr "工资结构类型"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_school
msgid "School"
msgstr "毕业院校"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Search Offers"
msgstr "搜索录用通知"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__display_type__section
msgid "Section"
msgstr "小节"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country_id
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Select a Country"
msgstr "选择一个国家"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_state_id
msgid "Select a State"
msgstr "选择一个省/州"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__benefit_ids
msgid ""
"Select other Benefits that need to be selected to make this Benefit "
"available"
msgstr "选择需要选择的其他福利，使该福利可用"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_id
msgid "Select the contract's field to consider in salary computation"
msgstr "选择计算工资时要考虑的合同字段"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__res_field_id
msgid ""
"Select the contract's field where the value of the benefit will be written"
msgstr "选择将写入津贴值的合同字段"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__dropdown
msgid "Selection"
msgstr "选择"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__dropdown_selection
msgid "Selection Nature"
msgstr "选择性质"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__selector_highlight
msgid "Selector Highlight"
msgstr "选择器高亮"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Send"
msgstr "发送"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Send By Email"
msgstr "通过电子邮件发送"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_copy_partner_id
msgid "Send a copy to"
msgstr "抄送"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Sent Offers"
msgstr "已发送录用通知"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer_applicant
msgid "Sent automatically when you generate an offer for an application"
msgstr "当您为一个申请生成要约时将自动发送"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer
msgid ""
"Sent manually when you generate a simulation link on the employee contract"
msgstr "在员工合同上生成模拟链接时手动发送"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__sequence
msgid "Sequence"
msgstr "序列"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Show <i class=\"oi oi-chevron-down\"/>"
msgstr "显示<i class=\"oi oi-chevron-down\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__show_name
msgid "Show Name"
msgstr "显示名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__right
msgid "Side Panel"
msgstr "侧边面板"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Sign"
msgstr "签署"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_frenquency
msgid "Sign Creation Type"
msgstr "标识创建类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__order
msgid "Sign Order"
msgstr "签署订单"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_signatories_ids
msgid "Sign Template Signatories"
msgstr "签名模板签署人"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "在合同文件上签字"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Signatories"
msgstr "签署人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__signatory
msgid "Signatory"
msgstr "签署人"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Signature Request - %s"
msgstr "签名请求 - %s"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__slider
msgid "Slider"
msgstr "滑块"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_max
msgid "Slider Max"
msgstr "滑动最大值  Slider Max"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_min
msgid "Slider Min"
msgstr "滑动最小值  Slider Min"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_step
msgid "Slider Step"
msgstr "滑块步骤值"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Some required fields are not filled"
msgstr "一些必要的字段没有填写"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__partner
msgid "Specific Partner"
msgstr "指定合作伙伴"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__specific
msgid "Specific Values"
msgstr "具体值"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_state_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__state
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "State"
msgstr "省/州"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__state
msgid "States"
msgstr "状态"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street
msgid "Street"
msgstr "街道"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street2
msgid "Street 2"
msgstr "街道2"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Structure Type"
msgstr "结构类型"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__sum
msgid "Sum of Benefits Values"
msgstr "效益值总和"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_template_id
msgid "Template to Sign"
msgstr "签署模板"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Templates"
msgstr "模板"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__text
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__text
msgid "Text"
msgstr "文本"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr "本合同是复制的合同。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "The current requested documents are the followings:"
msgstr "目前所需的文件如下："

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"The employee is not linked to an existing user, please contact the "
"administrator.."
msgstr "该员工没有与现有的用户联系，请联系管理员。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__fold_res_field_id
msgid ""
"The field here needs to be set for this benefit to be folded by default."
msgstr "需要设置此处的字段才能默认折叠此福利。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_benefit.py:0
msgid ""
"The minimum value for the slider should be inferior to the maximum value."
msgstr "滑块的最小值应低于最大值。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__signatures_count
msgid "The number of signatures on the pdf contract with the most signatures."
msgstr "PDF合同中最大签字数目。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"The offer has been marked as refused when the linked applicant was declined."
msgstr "关联的申请人被拒绝时，聘用邀约被标记为“已拒绝”。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__originated_offer_id
msgid "The original offer"
msgstr "原本聘用邀约"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_type_id
msgid ""
"The type of activity that will be created automatically on the contract if "
"this benefit is chosen by the employee."
msgstr "如果员工选择了这项福利，合同上将自动创建的活动类型。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_benefits
msgid "There is no available option to customize your salary"
msgstr "没有可用的选项来定制你的工资"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "There were no changes since previous contract."
msgstr "自上一份合同签订以来，没有发生任何变化。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"They will review your contract.<br/> Feel free to contact them if you have "
"any questions."
msgstr "他们会审查您的合同。<br/> 如有任何疑问，请随时联系。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"This link is invalid. Please contact the HR Responsible to get a new one..."
msgstr "这个链接是无效的。请联系人力资源部门负责人，以获得一个新的链接..."

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "This offer has been updated, please request an updated link.."
msgstr "此录用通知已更新，请申请提供最新链接。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "This offer is outdated, please request an updated link..."
msgstr "此录用通知已过期，请申请提供最新链接..."

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__display_name
msgid "Title"
msgstr "标题"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Token"
msgstr "令牌"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Total real monthly cost of the employee for the employer."
msgstr "雇主的员工每月实际费用总额。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Total real yearly cost of the employee for the employer."
msgstr "员工对雇主的实际年度总成本。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
msgid "Total yearly cost of the employee for the employer."
msgstr "雇主每年为员工支付的总费用。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__uom
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__uom
msgid "Unit of Measure"
msgstr "计量单位"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__update_contract_template_id
msgid "Update Contract Template"
msgstr "更新合同模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__validity_days_count
msgid "Validity Days Count"
msgstr "有效天数"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Validity duration for salary package requests for employees"
msgstr "员工工资包请求的有效期限"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr "新申请人工资包请求的有效期限"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__value
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__value
msgid "Value"
msgstr "值"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__value_type
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Value Type"
msgstr "值类型"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Wage"
msgstr "工资"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on Payroll"
msgstr "工资册上的工资"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on contract signature"
msgstr "签字合同时的工资"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
msgid "Wage update with holidays retenues"
msgstr "工资更新与假期收入"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_with_holidays
msgid "Wage with Holidays"
msgstr "带假期的工资"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation_type__onchange
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__sign_frenquency__always
msgid "When the benefit is modified"
msgstr "修改福利时"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation_type__always
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__sign_frenquency__onchange
msgid "When the benefit is set"
msgstr "设定福利时"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__show_name
msgid "Whether the name should be displayed in the Salary Configurator"
msgstr "是否应在薪资设置器中显示姓名"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Write your message here and we will come back to you."
msgstr "请在此留下信息，我们将很快与您联络。"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_type__periodicity__yearly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__yearly
msgid "Yearly"
msgstr "每年"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Yearly Cost"
msgstr "年度成本"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Yearly Cost (Real)"
msgstr "电镀成本（实际）。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Yes"
msgstr "是"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid "You cannot have multiple person responsible for the same role"
msgstr "不能由多人负责同一角色"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"You have to define contract templates to be used for offers. Go to "
"Configuration / Contract Templates to define a contract template"
msgstr "您必须定义用于报价的合同模板。转到 “配置”/“合同模板” 定义合同模板"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Your Personal Information"
msgstr "您的个人信息"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr "您的合同已发送至："

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_zip
msgid "Zip Code"
msgstr "邮编"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "close"
msgstr "关闭"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "e.g. Birthdate"
msgstr "例如：出生日期"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "e.g. Meal Vouchers"
msgstr "例如：餐券"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid "here"
msgstr "此处"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"{{ object.company_id.name }}: Job Offer - {{ "
"object.applicant_id.partner_name }}"
msgstr ""
"{{ object.company_id.name }}: 录用通知 - {{ object.applicant_id.partner_name }}"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
msgid ""
"{{ object.company_id.name }}: Job Offer - {{ "
"object.employee_contract_id.name }}"
msgstr ""
"{{ object.company_id.name }}: 录用通知 - {{ object.employee_contract_id.name }}"
