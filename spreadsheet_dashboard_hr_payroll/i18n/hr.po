# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_payroll
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "#Payslips"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Absenteeism Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Attendance Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Basic Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Hours per Days of Work"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Net Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Avg Hours/Days"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Companies"
msgstr "Tvrtke"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Current"
msgstr "Trenutno"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Days"
msgstr "Dana"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees"
msgstr "Djelatnici"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Job position"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Paid Time Off"
msgstr "Plaćeno odsustvo"

#. module: spreadsheet_dashboard_hr_payroll
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_payroll.spreadsheet_dashboard_payroll
msgid "Payroll"
msgstr "Obračun plaće"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Payroll Analysis"
msgstr "Analiza obračuna plaće"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Period"
msgstr "Period"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Previous"
msgstr "Prethodni"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Basic Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Net Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Unpaid TIme Off"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Days"
msgstr "Radni dani"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Entries Analysis"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Hours"
msgstr "Radni sati"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work type"
msgstr "Vrsta radnih sati"
