# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_crm
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span class=\"me-1\">posted on</span>"
msgstr "<span class=\"me-1\">publicado en</span>"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span class=\"ms-1\">at</span>"
msgstr "<span class=\"ms-1\">a las</span>"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Leads</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Leads</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Oportunidades</span>"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__conversion_source
msgid "Conversion Source"
msgstr "Fuente de conversión"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert"
msgstr "Convertir"

#. module: social_crm
#: model:ir.actions.act_window,name:social_crm.social_post_to_lead_action
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert Post to Lead"
msgstr "Convertir publicación a lead"

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post_to_lead
msgid "Convert Social Post to Lead"
msgstr "Convertir publicación en red social a lead"

#. module: social_crm
#. odoo-javascript
#: code:addons/social_crm/static/src/xml/social_crm_button.xml:0
msgid "Create Lead"
msgstr "Crear lead"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__create
msgid "Create a new customer"
msgstr "Crear un nuevo cliente"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_date
msgid "Created on"
msgstr "Creado el"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__partner_id
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Customer"
msgstr "Cliente"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__nothing
msgid "Do not link to a customer"
msgstr "No vincular a un cliente"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__comment
msgid "From a comment"
msgstr "Desde un comentario"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__stream_post
msgid "From a stream post"
msgstr "De una publicación en el flujo"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__id
msgid "ID"
msgstr "ID"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__leads_opportunities_count
msgid "Leads / Opportunities count"
msgstr "Número de leads/oportunidades"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Leads:"
msgstr "Leads:"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__exist
msgid "Link to an existing customer"
msgstr "Vincular a un cliente existente"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Opportunities:"
msgstr "Oportunidades:"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__author_name
msgid "Post Author Name"
msgstr "Nombre del autor de la publicación"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_content
msgid "Post Content"
msgstr "Contenido de la publicación"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_datetime
msgid "Post Datetime"
msgstr "Fecha de la publicación"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_image_urls
msgid "Post Images URLs"
msgstr "URL de las imágenes de la publicación"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_link
msgid "Post Link"
msgstr "Enlace de la publicación"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__action
msgid "Related Customer"
msgstr "Cliente relacionado"

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
msgid "Request from %s"
msgstr "Solcitud de %s"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_account_id
msgid "Social Account"
msgstr "Cuenta de red social"

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post
msgid "Social Post"
msgstr "Publicación en redes sociales"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "Social Post Image {{image_url_index}}"
msgstr "Imagen de la publicación en redes sociales {{image_url_index}}"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_stream_post_id
msgid "Social Stream Post"
msgstr "Publicación en el flujo social"

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
msgid "The lead has been created successfully."
msgstr "Este lead se creó con éxito."

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__use_leads
msgid "Use Leads"
msgstr "Usar leads"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_campaign_id
msgid "Utm Campaign"
msgstr "Campaña UTM"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_medium_id
msgid "Utm Medium"
msgstr "Medio UTM"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_source_id
msgid "Utm Source"
msgstr "Fuente UTM"
