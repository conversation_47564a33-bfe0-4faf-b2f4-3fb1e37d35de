# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_studio
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <kari.<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Tuomo Aura <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "<span class=\"bg-300 align-self-baseline\">/model/</span>"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_form_field_name
msgid ""
"<span class=\"s_website_form_label_content\">Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Nimi</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "<span invisible=\"name_slugified\" class=\"bg-300\">...</span>"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Advanced"
msgstr "Edistyneet"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Basic parameters"
msgstr "Perusparametrit"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "Create %s"
msgstr "Luo %s"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__auto_single_page
msgid "Create Single Page"
msgstr "Luo yksittäinen sivu"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__use_menu
msgid "Create Website Menu"
msgstr "Luo verkkosivun valikko"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_record_page
msgid "Drag building blocks to customize the footer of single record pages."
msgstr ""
"Vedä lohkoja yksittäisten tietueiden sivujen alatunnisteen mukauttamiseksi."

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_record_page
msgid "Drag building blocks to customize the header of single record pages."
msgstr "Voit muokata yksittäisten tietueiden otsikkoa vetämällä lohkoja."

#. module: website_studio
#. odoo-python
#: code:addons/website_studio/models/website_controller_page.py:0
msgid "Drag building blocks to edit the website description of this record."
msgstr ""
"Muokkaa tämän tietueen verkkosivun kuvausta vetämällä rakennuspalikoita."

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Exposed model"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.s_website_form_options
msgid "Form Access"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.listing_layout_switcher
msgid "Grid"
msgstr "Taulukko"

#. module: website_studio
#: model:ir.model.fields,help:website_studio.field_website_controller_page__auto_single_page
msgid "If checked, a single page will be created along with your listing page"
msgstr "Jos valintaruutu on valittuna, yksi sivu luodaan listasivun rinnalle"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.listing_layout_switcher
msgid "List"
msgstr "Lista"

#. module: website_studio
#: model:ir.model,name:website_studio.model_website_controller_page
msgid "Model Page"
msgstr "Mallisivu"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/editor_tabs.js:0
msgid "Model Pages"
msgstr "Mallin sivut"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "More models"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "Search"
msgstr "Hae"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "Search..."
msgstr "Hae..."

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "Select model"
msgstr ""

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_form_field_name
msgid "Submit"
msgstr "Tallenna"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "URL"
msgstr "URL"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__name
msgid "View Name"
msgstr "Näkymän nimi"

#. module: website_studio
#. odoo-python
#: code:addons/website_studio/models/website_controller_page.py:0
msgid "Website Description"
msgstr "Verkkosivun kuvaus"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "found)"
msgstr "löytyi)"
