# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_commission
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "%s (copy)"
msgstr "%s (surət)"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "<span class=\"o_stat_text\">Commissions</span>"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__achieved
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved
msgid "Achieved"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved_rate
msgid "Achieved Rate"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__achievement_ids
msgid "Achievement"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_config_settings__group_commission_forecast
msgid "Achievement Forecast"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Achievement Report"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_achievement_action_report
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__achieve
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_report_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Achievements"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__active
msgid "Active"
msgstr "Aktiv"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Active Plans"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add Multiple Salespersons"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_subscription_change_customer_wizard_action
msgid "Add Salespersons"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new Sales Person"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new achievement"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new commission level"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment %s"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment: %s"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_achievement
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_menu
msgid "Adjustments"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All categories"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All products"
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid ""
"Allows you to manually adjust a salesperson's achievements\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
msgid ""
"Allows you to manually set a salesperson's forecast\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid ""
"Allows you to manually set your sales' forecast\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__amount
msgid "Amount"
msgstr "Məbləğ"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_invoiced
msgid "Amount Invoiced"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_sold
msgid "Amount Sold"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Approve"
msgstr "Təsdiq et"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__approved
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Approved"
msgstr "Təsdiq olundu"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Archived"
msgstr "Arxivləndi"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Based on"
msgstr "Əsasən"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "By Quarter"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__cancel
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_categ_id
msgid "Category"
msgstr "Kateqoriya"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__commission
msgid "Commission"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_achievement_view_search
msgid "Commission Achievement"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/report/commission_report.py:0
msgid "Commission Detail: %(name)s"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan
#: model:ir.model.fields,field_description:sale_commission.field_crm_team__commission_plan_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Plan"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_achievement
msgid "Commission Plan Achievement"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target
msgid "Commission Plan Target"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_commission
msgid "Commission Plan Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_forecast
msgid "Commission Plan Target Forecast"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user
msgid "Commission Plan User"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_plan
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_menu
msgid "Commission Plans"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Report"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__commission_plan_users_ids
msgid "Commission plans"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report_sale
#: model:ir.ui.menu,name:sale_commission.menu_sale_commission
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu_report
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Commissions"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Company"
msgstr "Şirkət"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Completed"
msgstr "Tamamlandı"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid ""
"Compute commissions of your sales people based on their achievements and "
"targets"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid "Create a new commission plan"
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid "Create an adjustment"
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid "Create an forecast"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_rate
msgid "Currency Rate"
msgstr "Valyuta Məzənnəsi"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Current Period"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__date
msgid "Date"
msgstr "Tarix"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_crm_team__commission_plan_ids
msgid "Default commission plan for team members."
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Details"
msgstr "Ətraflı Məlumat"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__done
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Done"
msgstr "Hazırdır"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__draft
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Draft"
msgstr "Qaralama"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Effective Period"
msgstr ""

#. module: sale_commission
#: model:res.groups,name:sale_commission.group_commission_forecast
msgid "Enable Commission Forecast"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "End Date"
msgstr "Bitmə Tarixi"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid ""
"Ensure you are assigned to a commission plan and have made sales that "
"qualify for commissions"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__filtered_commission_plan_users_ids
msgid "Filtered Commission Plan Users"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_forecast
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_graph
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_list
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "Forecast"
msgstr "Proqnoz"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_from
msgid "From"
msgstr "Başlama Tarixi"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "From must be before To"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Group By"
msgstr "Görə Qrupla"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__id
msgid "ID"
msgstr "ID"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement
msgid "Manual Commission Achievement"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Mark as done"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__month
msgid "Monthly"
msgstr "Aylıq"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "My Achievements"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_report
#: model:ir.ui.menu,name:sale_commission.sale_commission_my_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "My Commissions"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_forecast
msgid "My Forecast"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__name
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Name"
msgstr "Ad"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__note
msgid "Note"
msgstr "Qeyd"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__commission_amount
msgid "OTC"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "OTC %"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__commission_amount
msgid "On Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "On Target Commission rate"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Ongoing"
msgstr "Davamlı"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__other_plans
msgid "Other plans"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__payment_date
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Payment Date"
msgstr "Ödəniş Tarixi"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Period"
msgstr "Müddət"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__periodicity
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Periodicity"
msgstr "Dövrilik"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Periods"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__plan_id
msgid "Plan"
msgstr "Plan"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_id
msgid "Product"
msgstr "Məhsul"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_invoiced
msgid "Quantity Invoiced"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_sold
msgid "Quantity Sold"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__quarter
msgid "Quarterly"
msgstr "Rüblük"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__rate
msgid "Rate"
msgstr "Qiymətləndirmə"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_id
msgid "Related"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_model
msgid "Related Res Model"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "Related commissions"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Reset to draft"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__team_id
msgid "Sale team"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement_report
msgid "Sales Achievement Report"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_report
msgid "Sales Commission Report"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Sales People"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__user_id
msgid "Sales Person"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_crm_team
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__team_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__team
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Sales Team"
msgstr "Satış Komandası"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__user_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__person
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Salesperson"
msgstr "Satıcı"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "See associated achievements"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_list
msgid "Source"
msgstr "Mənbə"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "Start Date"
msgstr "Başlanğıc Tarixi"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__state
msgid "State"
msgstr "Dövlət"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
msgid "Submit"
msgstr "Təqdim edin"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__amount
msgid "Target"
msgstr "Hədəf "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_amount
msgid "Target Amount"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_ids
msgid "Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_graph
msgid "Target Commission Graph"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Target Frequency"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__target_rate
msgid "Target completion (%)"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__target
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Targets"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid ""
"The plan should have at least one target with an achievement rate of 0%"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The start date must be before the end date."
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The team is required in team plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.constraint,message:sale_commission.constraint_sale_commission_plan_user_user_uniq
msgid "The user is already present in the plan"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Month"
msgstr "Bu Ay"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Year"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__date_to
msgid "To"
msgstr "Bitmə Tarixi"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__type
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Type"
msgstr "Tip"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid "Unfortunately, there are no commissions for you"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Upcoming"
msgstr "Qarşıdan gələn"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_users
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__user_ids
msgid "User"
msgstr "İstifadəçi"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid "User Sales Team"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_type
msgid "User Type"
msgstr "İstifadəçi tipi"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot end after the plan."
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot start before the plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user_wizard
msgid "Wizard for selecting multiple users"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__year
msgid "Yearly"
msgstr "İllik"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_target_forecast.py:0
msgid "You cannot create a forecast for an user that is not in the plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast_id
msgid "fc"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "i.e. Commissions plan 2025"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "per"
msgstr ""
