# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_intrastat
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__company_country_id
msgid "Fiscal Country"
msgstr "Държава на данъчно облагане"

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_stock_intrastat_report_handler
msgid "Intrastat Report Custom Handler (Stock)"
msgstr ""

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__intrastat_region_id
msgid "Intrastat region"
msgstr ""

#. module: stock_intrastat
#: model:ir.model.fields,help:stock_intrastat.field_stock_warehouse__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_stock_warehouse
msgid "Warehouse"
msgstr "Склад"
