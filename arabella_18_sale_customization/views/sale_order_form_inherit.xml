<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Override the form view to add new state buttons -->
    <record id="view_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.form.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
             <!-- Modify the header buttons -->
            <!-- modify Confirm buttons -->
            <xpath expr="//header/button[@name='action_confirm'][1]" position="attributes">
                <!-- Make the confirm button visible only in GM approval state -->
                <attribute name="invisible">state != 'gm_approval' </attribute>
                <attribute name="groups" >arabella_18_sale_customization.general_manager_group</attribute>
            </xpath>
            <!-- remove the second Confirm button -->
            <xpath expr="//header/button[@name='action_confirm'][2]" position="replace" />


            <!-- modify send by email forth button -->
            <xpath expr="//header/button[@name='action_quotation_send'][last()]"  position="attributes">
                <!-- Make the send by email button invisible in Quotation state -->
                <attribute name="invisible">state not in ('gm_approval', 'manager_approval', 'sent', 'sale') </attribute>
            </xpath>

            <!-- modify cancel button -->
            <xpath expr="//header/button[@name='action_cancel']" position="attributes">
                <!-- Make the cancel button invisible in Quotation state -->
                <attribute name="invisible">state not in ['gm_approval', 'manager_approval', 'draft', 'sent', 'sale'] or not id or locked </attribute>
            </xpath>


            <xpath expr="//header/button[@name='action_confirm'][1]" position="after">
                <!-- Add Manager Approval  button -->
                <button name="action_set_manager_approval"
                        string="manager approve"
                        type="object"
                        class="btn-primary"
                        invisible="state not in ('draft', 'sent')"
                        groups="arabella_18_sale_customization.manager_group"
                        />


                <!-- Add General Manager Approval button -->
                <button name="action_set_gm_approval"
                        string="GM approve"
                        type="object"
                        class="btn-primary"
                        invisible="state != 'manager_approval'"
                        groups="arabella_18_sale_customization.general_manager_group"
                        />



            </xpath>

            <!-- Update the statusbar to show all states -->
            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="statusbar_visible">draft,sent,manager_approval,gm_approval,sale</attribute>
            </xpath>
        </field>
    </record>
</odoo>