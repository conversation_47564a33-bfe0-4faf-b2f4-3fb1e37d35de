<odoo>
    <data>
<!--        manager group-->
        <record id="manager_group" model="res.groups">
            <field name="name">Sale manager</field>
            <field name="category_id" ref="base.module_category_sales_sales"/>
            <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
          </record>

<!--        General manager group-->
         <record id="general_manager_group" model="res.groups">
            <field name="name">Sale general manager</field>
            <field name="category_id" ref="base.module_category_sales_sales"/>
            <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
          </record>
    </data>
</odoo>