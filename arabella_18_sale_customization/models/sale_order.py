from odoo import models, fields

SALE_ORDER_STATE = [
    ('draft', "Quotation"),
    ('sent', "Quotation Sent"),
    ("manager_approval", "Manager Approval"),
    ("gm_approval","General Manager Approval"),
    ('sale', "Sales Order"),
    ('cancel', "Cancelled"),
]

class SaleOrderModified(models.Model):
    _inherit = 'sale.order'

    # Override the state field to add custom states
    state = fields.Selection(
        selection=SALE_ORDER_STATE,
        string="Status",
        readonly=True, copy=False, index=True,
        tracking=3,
        default='draft'
    )

    def action_set_manager_approval(self):
        for order in self:
            order.state = 'manager_approval'

    def action_set_gm_approval(self):
        for order in self:
            order.state = 'gm_approval'

    # overriding the base method of approval
    def _confirmation_error_message(self):
        """ Return whether order can be confirmed or not if not then returm error message. """
        self.ensure_one()
        if self.state not in {'gm_approval'}:
            return _("Some orders are not in a state requiring confirmation.")
        if any(
            not line.display_type
            and not line.is_downpayment
            and not line.product_id
            for line in self.order_line
        ):
            return _("A line on these orders missing a product, you cannot confirm it.")

        return False