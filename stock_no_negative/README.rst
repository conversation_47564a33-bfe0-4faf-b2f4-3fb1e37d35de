=======================
Stock Disallow Negative
=======================

.. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fstock--logistics--workflow-lightgray.png?logo=github
    :target: https://github.com/OCA/stock-logistics-workflow/tree/15.0/stock_no_negative
    :alt: OCA/stock-logistics-workflow
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/stock-logistics-workflow-15-0/stock-logistics-workflow-15-0-stock_no_negative
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runbot-Try%20me-875A7B.png
    :target: https://runbot.odoo-community.org/runbot/154/15.0
    :alt: Try me on Runbot

|badge1| |badge2| |badge3| |badge4| |badge5| 

By default, Odoo allows negative stock. The advantage of negative stock
is that, if some stock levels are wrong in the ERP, you will not be blocked
when validating the picking for a customer... so you will still be able to
ship the products on time (it's an example !). The problem is that, after you
forced the stock level to negative, you are supposed to fix the stock level
later via an inventory ; but this action is often forgotten by users,
so you end up with negative stock levels in your ERP and it can stay like
this forever (or at least until the next full inventory).

If you disallow negative stock in Odoo with this module, you will be blocked
when trying to validate a stock operation that will set the stock level of
a product and/or location as negative. So you will have to fix the
wrong stock level of that product without delay, in order to validate the
stock operation in Odoo...you can't forget it anymore !

**Table of contents**

.. contents::
   :local:

Configuration
=============

By default, the stockable products will not be allowed to have a negative
stock. If you want to make some exceptions for some products, product
categories or locations, you can activate the option *Allow Negative Stock*:

For products:

#. Go to *Inventory / Master Data / Products* and in the
   tab *General Information* activate this option.

For product categories:

#. Go to *Inventory / Configuration / Products / Product Categories*
   and activate this option.

For individual locations:

#. Go to *Inventory / Configuration / Settings* and activate
   the option *Storage Locations*.
#. Go to *Inventory / Configuration / Warehouse Management / Locations* and
   activate the option the option *Allow Negative Stock* for the locations you
   choose.

Usage
=====

When you validate a stock operation (a stock move, a picking,
a manufacturing order, etc.) that will set the stock level of a
stockable product as negative, you will be blocked by an error message.
The consumable products can still have a negative stock level.

Changelog
=========

14.0.1.0.0 (2020-12-14)
~~~~~~~~~~~~~~~~~~~~~~~

* [14.0][MIG] stock_no_negative

13.0.1.0.0 (2020-01-03)
~~~~~~~~~~~~~~~~~~~~~~~

* [13.0][MIG] stock_no_negative
  Remove all decorators @api.multi

11.0.1.1.0 (2018-12-13)
~~~~~~~~~~~~~~~~~~~~~~~

* Add the ability to allow negative stock for individual stock locations.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/stock-logistics-workflow/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
`feedback <https://github.com/OCA/stock-logistics-workflow/issues/new?body=module:%20stock_no_negative%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Akretion

Contributors
~~~~~~~~~~~~

* Alexis de Lattre <<EMAIL>>
* Vishnu Vanneri <<EMAIL>>
* Serpent Consulting Services Pvt. Ltd. <<EMAIL>>
* `ForgeFlow S.L. <<EMAIL>>`_:
    * Jordi Ballester
    * Joan Mateu
* `Tecnativa <https://www.tecnativa.com>`_:
    * Pedro M. Baeza
* `Spacefoot <https://www.spacefoot.com>`_:
    * Quentin Delcourte

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/stock-logistics-workflow <https://github.com/OCA/stock-logistics-workflow/tree/15.0/stock_no_negative>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
