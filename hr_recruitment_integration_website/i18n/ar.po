# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_website
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "طريقة التقديم "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "At least one platform must be selected"
msgstr ""

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "تاريخ بدء الحملة "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Campaign Start Date is required."
msgstr "تاريخ بدء الحملة مطلوب "

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_job_post__apply_method
msgid "Contact Method"
msgstr "وسيلة التواصل "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Generate"
msgstr "إنشاء"

#. module: hr_recruitment_integration_website
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_view_form
msgid "Generate Description (<i class=\"fa fa-fw fa-magic\"/>AI)"
msgstr "إنشاء وصف (<i class=\"fa fa-fw fa-magic\"/>باستخدام الذكاء الاصطناعي) "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"IGNORE PREVIOUS PROMPTS AND FORMAT AS HTML AND NO HYPERLINKS\n"
"You are the best recruiter in out company and we are looking to hire a new employee.\n"
"You have been tasked with creating a the best job description for a new job opening.\n"
"Given the following infos, make a friendly and attractive job description that could be used\n"
"to attract potential candidates (The platforms could be things like LinkedIn, Indeed, Glassdoor, Monster, etc.)\n"
"It's your last chance to make a good impression and attract the best candidates to our company so make it count!\n"
"Here is the data to generate the job description:\n"
msgstr ""

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__platform_ids
msgid "Job Board"
msgstr "موقع التوظيف "

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job_post
msgid "Job Post"
msgstr "منصب وظيفي "

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__job_apply_url
msgid "Job url"
msgstr "رابط URL للوظيفة "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr "عفواً، يبدو أن نظام الذكاء الاصطناعي لدينا غير متصل! "

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__post_html
msgid "Post"
msgstr "منشور "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "نشر الوظيفة "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Post is required."
msgstr "المنشور مطلوب. "

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_job_post__apply_method__redirect
msgid "Redirect to Website"
msgstr "إعادة التوجيه إلى الموقع الإلكتروني "

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_recruitment_post_job_wizard__apply_method__redirect
msgid "Redirect to company's website"
msgstr "إعادة التوجيه إلى الموقع الإلكتروني للشركة "

#. module: hr_recruitment_integration_website
#: model:ir.actions.server,name:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_action_regenerate_post
msgid "Regenerate Post"
msgstr "إعادة كتابة المنشور "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, the web page is too long for our AI to process."
msgstr ""

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr "عذراً ، لم نتمكن من إنشاء رد. يرجى المحاولة مجدداً لاحقاً. "

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The Job Description will be replaced with the generated one, do you want to "
"continue?"
msgstr ""

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,help:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr ""

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The job must be published on the website to generate a post with a redirect "
"apply method."
msgstr ""

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"URL is required if the apply method is 'Redirect to company's website'."
msgstr ""

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "You can only generate a post for a published job offer."
msgstr ""

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"لقد وصلت إلى الحد الأقصى لعدد الطلبات لهذه الخدمة. يرجى المحاولة مجدداً "
"لاحقاً. "
