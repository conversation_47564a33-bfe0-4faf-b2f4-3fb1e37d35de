# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_inter_company_rules
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_generated
msgid "Auto Generated Purchase Order"
msgstr "Comandă de Achiziție generată automat"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_generated
msgid "Auto Generated Sales Order"
msgstr "Comandă de vânzare generată automat"

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr "Generat automat din %(origin)s ale companiei %(company)s."

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__intercompany_generate_purchase_orders
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__intercompany_generate_purchase_orders
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.res_config_settings_view_form
msgid "Generate Purchase Orders"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.view_company_inter_change_inherit_form
msgid "Generate Purchases Orders"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.view_company_inter_change_inherit_form
msgid "Generate Sales Orders"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__intercompany_generate_sales_orders
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__intercompany_generate_sales_orders
msgid "Generate Sales order"
msgstr ""

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
msgid ""
"Inter company user of company %(name)s doesn't have enough access rights"
msgstr ""
"Utilizatorul inter-companie al companiei %(name)s nu are suficiente drepturi"
" de acces"

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""
"Utilizatorul inter-companie al companiei %s nu are suficiente drepturi de "
"acces"

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
msgid "Provide at least one user for inter company relation for %(name)s"
msgstr ""
"Furnizați cel puțin un utilizator pentru relația între companii pentru "
"%(name)s"

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
msgid "Provide one user for intercompany relation for %(name)s "
msgstr "Furnizați un utilizator pentru relația intercompany pentru %(name)s"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Comandă de achiziție"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Comandă de vânzare"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_purchase_order_id
msgid "Source Purchase Order"
msgstr "Sursă comandă de achiziție"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_sale_order_id
msgid "Source Sales Order"
msgstr "Sursă comandă de vânzare"

#. module: sale_purchase_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
msgid ""
"You cannot create SO from PO because sale price list currency is different than purchase price list currency.\n"
"The currency of the SO is obtained from the pricelist of the company partner.\n"
"\n"
"(SO currency: %(so_currency)s, Pricelist: %(pricelist)s, Partner: %(partner)s (ID: %(id)s))"
msgstr ""
"Nu puteți crea o comandă de vânzare dintr-o comandă de achiziție deoarece moneda listei de prețuri de vânzare este diferită de moneda listei de prețuri de achiziție.\n"
"Moneda comenzii de vânzare este obținută din lista de prețuri a companiei partenere.\n"
"\n"
"(Monedă comandă de vânzare: %(so_currency)s, Listă de prețuri: %(pricelist)s, Partener: %(partner)s (ID: %(id)s))"
