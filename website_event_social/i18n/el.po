# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_social
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Ko<PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_social
#. odoo-javascript
#: code:addons/website_event_social/static/src/js/push_notification_widget.js:0
msgid ""
"Allow notifications to be able to add talks into your favorite list or "
"connect to other attendees."
msgstr ""

#. module: website_event_social
#: model:ir.model,name:website_event_social.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: website_event_social
#: model:ir.model.fields,field_description:website_event_social.field_event_event__firebase_enable_push_notifications
msgid "Enable Web Push Notifications"
msgstr ""

#. module: website_event_social
#: model:ir.model,name:website_event_social.model_event_event
msgid "Event"
msgstr "Συμβάν"

#. module: website_event_social
#: model:ir.model.fields,field_description:website_event_social.field_res_partner__registration_ids
#: model:ir.model.fields,field_description:website_event_social.field_res_users__registration_ids
msgid "Event Registrations"
msgstr ""

#. module: website_event_social
#. odoo-javascript
#: code:addons/website_event_social/static/src/js/push_notification_widget.js:0
msgid "Get the best experience!"
msgstr ""

#. module: website_event_social
#: model_terms:ir.ui.view,arch_db:website_event_social.event_event_view_form
msgid "Send Push"
msgstr ""

#. module: website_event_social
#. odoo-python
#: code:addons/website_event_social/models/event.py:0
msgid "You do not have access to this action."
msgstr ""
