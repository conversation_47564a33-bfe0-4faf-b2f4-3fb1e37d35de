# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_stock_inter_company_rules
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# krnkris, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: krnkris, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások"

#. module: sale_purchase_stock_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_stock_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_stock_inter_company_rules/models/sale_order.py:0
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""
"Állítson be megfelelő raktárépületet a (%s) vállalathoz a következő Menüből:"
" Beállítások/Felhasználók/Vállalatok"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid ""
"Default Operation type to set on Receipts that will be created for inter-"
"company transfers"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""
"A Beszerzési(Vásárlói/Értékesítési) Megrendeléseken beállított "
"alapértelmezett érték, melyet a vállalat a Vásárlói(Beszerzési) megrendelés "
"alapján hozza létre"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Beszerzési megrendelés"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid "Receipt Operation Type"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Vevői rendelés"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "Vevői rendelés sor"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_sync_delivery_receipt
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_sync_delivery_receipt
msgid "Synchronize Deliveries to your Receipts"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_stock_picking
msgid "Transfer"
msgstr "Átmozgatás"

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Operation"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
msgid "Warehouse"
msgstr "Raktár"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "Raktár a beszerzési megrendelésekhez"
