# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_gantt
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s is on time off %(leaves)s. \n"
msgstr "%(name)sは休暇中 %(leaves)sです。 \n"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s requested time off %(leaves)s. \n"
msgstr "%(name)sは休暇 %(leaves)sを申請しました。 \n"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Approve"
msgstr "承認"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr "日"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Refuse"
msgstr "不採用"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr "休暇"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "休暇カレンダー"

#. module: hr_holidays_gantt
#. odoo-javascript
#: code:addons/hr_holidays_gantt/static/src/views/gantt/hr_holidays_gantt_view.js:0
msgid "Time Off Request"
msgstr "休暇申請"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Validate"
msgstr "検証"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "from %(start_date)s to %(end_date)s"
msgstr " %(start_date)s から %(end_date)sへ"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s"
msgstr "%(start_date)sに"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s from %(start_time)s to %(end_time)s"
msgstr "%(start_date)sに、%(start_time)s から %(end_time)sまで"
