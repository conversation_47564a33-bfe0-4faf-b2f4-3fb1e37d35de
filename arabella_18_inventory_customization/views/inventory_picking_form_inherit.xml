<odoo>
    <data>
        <record id="view_picking_form_inherit_arabella" model="ir.ui.view">
            <field name="name">stock.picking.form.inherit.arabella</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">

                <!-- modify validate buttons -->
                <xpath expr="//header/button[@name='button_validate'][1]" position="attributes">
                    <!-- Make the confirm button visible only in GM approval state -->
                    <attribute name="invisible">state != 'gm_approval' </attribute>
                    <attribute name="groups">arabella_18_inventory_customization.general_manager_group</attribute>
                </xpath>
                <!-- remove the second Confirm button -->
                <xpath expr="//header/button[@name='button_validate'][2]" position="replace"/>

                <!-- modify first print button -->
                <xpath expr="//header/button[@name='do_print_picking'][1]" position="attributes">
                    <attribute name="invisible">state not in ('gm_approval', 'manager_approval','assigned') </attribute>
                </xpath>


                <!-- modify send by CANCEL button -->
                <xpath expr="//header/button[@name='action_cancel']" position="attributes">
                    <attribute name="invisible">state not in ('gm_approval', 'manager_approval','assigned', 'confirmed', 'draft', 'waiting') </attribute>
                </xpath>

                <xpath expr="//header/button[@name='action_assign']" position="after">
                    <!-- Add Manager Approval  button -->
                    <button name="action_set_manager_approval"
                            string="manager approve"
                            type="object"
                            class="btn-primary"
                            invisible="state not in ('draft','confirmed', 'assigned')"
                            groups="arabella_18_inventory_customization.manager_group"
                            />


                    <!-- Add General Manager Approval button -->
                    <button name="action_set_gm_approval"
                            string="GM approve"
                            type="object"
                            class="btn-primary"
                            invisible="state != 'manager_approval'"
                            groups="arabella_18_inventory_customization.general_manager_group"
                            />



                </xpath>
                <!-- Update the statusbar to show all states -->
                <!-- Receipts -->
                <xpath expr="//header/field[@name='state'][1]" position="attributes">
                    <attribute name="statusbar_visible">draft,assigned,manager_approval,gm_approval,done</attribute>
                </xpath>
                <!-- Deliveries -->
                <xpath expr="//header/field[@name='state'][2]" position="attributes">
                    <attribute name="statusbar_visible">draft,confirmed,assigned,manager_approval,gm_approval,done</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>