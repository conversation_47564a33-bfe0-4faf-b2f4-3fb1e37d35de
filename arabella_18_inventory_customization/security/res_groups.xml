<odoo>
    <data>
        <!--  manager group  -->
        <record id="manager_group" model="res.groups">
            <field name="name">Inventory manager</field>
            <field name="category_id" ref="base.module_category_inventory_inventory"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_manager'))]"/>
          </record>

        <!--  General manager group  -->
         <record id="general_manager_group" model="res.groups">
            <field name="name">Inventory general manager</field>
            <field name="category_id" ref="base.module_category_inventory_inventory"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_manager'))]"/>
          </record>

    </data>
</odoo>