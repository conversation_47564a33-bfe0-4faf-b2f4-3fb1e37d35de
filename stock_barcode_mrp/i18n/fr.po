# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_mrp
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Man<PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""
"<i class=\"fa fa-print\"/> Imprimer les codes-barres des commandes et des "
"types d'opération"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> Imprimer les commandes de codes-barres"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Add Component"
msgstr "Ajouter composant"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow full order validation"
msgstr "Autoriser la validation complète de la commande"

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr ""
"Autoriser la création de nouveaux lots/numéros de série pour les composants"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow user to produce all even if no components were scanned"
msgstr ""
"Autoriser l'utilisateur à produire tout même si aucun composant n'a été "
"scanné"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr "Êtes-vous sûr de vouloir annuler cet ordre de fabrication ?"

#. module: stock_barcode_mrp
#: model:ir.actions.client,name:stock_barcode_mrp.stock_barcode_mo_client_action
msgid "Barcode MO Client Action"
msgstr "Action client code-barres OF"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclature des codes-barres"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Cancel Manufacturing Order"
msgstr "Annuler l'ordre de fabrication"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Cancel manufacturing order?"
msgstr "Annuler l'ordre de fabrication ?"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Configurer les codes-barres des produits"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Confirm"
msgstr "Confirmer"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_picking_type__count_mo_confirmed
msgid "Count Mo Confirmed"
msgstr "Nombre d'OF confirmés"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "Créer de nouveaux lots/numéros série pour les composants"

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Create a new Manufacturing Order"
msgstr "Créer un nouvel ordre de fabrication"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Destination Location"
msgstr "Emplacement de destination"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Discard"
msgstr "Ignorer"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__is_completed
msgid "Is Completed"
msgstr "Est complété"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid "Manual Consumption"
msgstr "Consommation manuelle"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Ordre de fabrication"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_byproduct_line_ids
msgid "Move Byproduct Line"
msgstr "Ligne de sous-produit du mouvement"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_raw_line_ids
msgid "Move Raw Line"
msgstr "Ligne de mouvement brut"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__backorder_ids
msgid "Mrp Production"
msgstr "Production Mrp"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "New"
msgstr "Nouveau"

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "No Manufacturing Order ready for this %(barcode_type)s"
msgstr "Aucun ordre de fabrication pour ce %(barcode_type)s"

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "No product or order found for barcode %s"
msgstr "Aucun produit ou commande trouvé pour le code-barres %s"

#. module: stock_barcode_mrp
#: model:ir.actions.act_window,name:stock_barcode_mrp.mrp_action_kanban
msgid "Operations"
msgstr "Opérations"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Type de transfert"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Finished Product Label (PDF)"
msgstr "Imprimer l'étiquette du produit fini (PDF)"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Finished Product Label (ZPL)"
msgstr "Imprimer l'étiquette du produit fini (ZPL)"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Production Order"
msgstr "Imprimer l'ordre de production"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Produce"
msgstr "Produire"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Produce All"
msgstr "Tout produire"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/header.xml:0
msgid "Producing"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Product"
msgstr "Produit"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Product not Allowed"
msgstr "Produit non autorisé"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Quantity"
msgstr "Quantité"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Scan a component"
msgstr "Scanner un composant"

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "Scan a product to filter the orders."
msgstr "Scanner un produit pour filtrer les commandes."

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "Scan an"
msgstr "Scanner un(e)"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Scan your final product or more components"
msgstr "Scanner votre produit final ou plus de composants"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.stock_move_line_product_selector
msgid "Serial/Lot Number"
msgstr "Numéro de série / lot"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "The Manufacturing Order has been cancelled"
msgstr "L'ordre de fabrication a été annulé"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_picking_model.js:0
msgid ""
"The lines with a kit have been replaced with their components. Please check "
"the picking before the final validation."
msgstr ""
"Les lignes avec un kit ont été remplacées par leurs composants. Veuillez "
"vérifier le transfert avant la validation finale."

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "The manufacturing order has been validated"
msgstr "L'ordre de fabrication a été validé"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "This order is already done"
msgstr "Cet commande est déjà terminée"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "This order is cancelled"
msgstr "Cette commande est annulée"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "To produce more products create a new MO."
msgstr "Créez un nouvel OF pour produire plus de produits."

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_picking
msgid "Transfer"
msgstr "Transfert"

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Les transferts vous permettent de déplacer des produits d'un emplacement à "
"un autre."

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""
"Lorsqu'il est activé, l'enregistrement de la consommation pour ce composant est enregistré manuellement exclusivement.\n"
"S'il n'est pas activé et que la consommation de l'un des composants est modifiée manuellement sur l'ordre de fabrication, Odoo suppose qu'il s'agit également d'une consommation manuelle."

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "Assistant pour marquer comme terminé ou créer un reliquat"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "You can't add the final product of a MO as a byproduct."
msgstr ""
"Vous ne pouvez pas ajouter le produit final d'un ordre de fabrication en "
"tant que sous-produit."

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "or a"
msgstr "ou une"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "product"
msgstr "produit"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr ""
