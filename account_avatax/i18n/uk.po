# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_avatax
# 
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "- %(partner_name)s (ID: %(partner_id)s) on %(record_list)s"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-fw oi-arrow-right\"/>\n"
"                                How to Get Credentials"
msgstr ""
"<i class=\"oi oi-fw oi-arrow-right\"/>\n"
"                                Як отримати облікові дані"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Avatax portal"
msgstr ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Портал Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                Show logs"
msgstr ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                Показати логи"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                Start logging for 30 minutes"
msgstr ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                Почніть реєстрацію протягом 30 хвилин"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                Sync Parameters"
msgstr ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                Синхронізувати параметри"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Test connection"
msgstr ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Тестове з'єднання"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API KEY"
msgstr "КЛЮЧ API "

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_chart_template
msgid "Account Chart Template"
msgstr "Шаблон плану рахунків"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Account that will be used by Avatax taxes for invoices."
msgstr ""
"Рахунок, який буде використовуватися податками Avatax для рахунків-фактур."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Account that will be used by Avatax taxes for refunds."
msgstr ""
"Рахунок, який буде використовуватися Avatax для відшкодування податків."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Address Validation"
msgstr "Валідація адреси"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
msgid "Address validation is only supported for North American addresses."
msgstr "Перевірка адрес підтримується лише для північноамериканських адрес."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Authentication failed."
msgstr "Аутентифікація не вдалася."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Authentication success."
msgstr "Аутентифікація успішна."

#. module: account_avatax
#: model:account.fiscal.position,name:account_avatax.account_fiscal_position_avatax_us
msgid "Automatic Tax Mapping (AvaTax)"
msgstr "Автомтаичне співставлення податків (AvaTax)"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Automatically compute tax rates in the US and Canada."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "AvaTax"
msgstr "AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_id
msgid "Avalara API ID"
msgstr "Avalara API ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_key
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_key
msgid "Avalara API KEY"
msgstr "Avalara API KEY"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_address_validation
msgid "Avalara Address Validation"
msgstr "Валідація адреси Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avatax_unique_code
msgid "Avalara Code"
msgstr "Код Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_partner_code
msgid "Avalara Company Code"
msgstr "Код компанії Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_environment
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_environment
msgid "Avalara Environment"
msgstr "Середовище Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_exemption_id
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_exemption_id
msgid "Avalara Exemption"
msgstr "Звільнення від Avalara"

#. module: account_avatax
#: model:ir.actions.act_window,name:account_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr "Вхід в Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_partner_code
msgid "Avalara Partner Code"
msgstr "Код партнера Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_show_address_validation
msgid "Avalara Show Address Validation"
msgstr "Показати перевірку адреси Avalara"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.account_fiscal_position_form_inherit
msgid "Avatax"
msgstr "Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__avatax_category_id
msgid "Avatax Category"
msgstr "Категорія Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_tax_date
msgid "Avatax Date"
msgstr "Дата Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Avatax Invoice Account"
msgstr "Рахунок Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_exemption
msgid "Avatax Partner Exemption Codes"
msgstr "Коди винятків для партнерів Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_avatax_category
msgid "Avatax Product Category"
msgstr "Категорія товару Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Avatax Refund Account"
msgstr "Рахунок повернення Avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_tax_date
msgid ""
"Avatax will use this date to calculate the tax on this invoice. If not "
"specified it will use the Invoice Date."
msgstr ""
"Avatax буде використовувати цю дату для обчислення податків на цьому "
"рахунку. Якщо не вказано, використовуватиметься дата рахунка-фактури."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Cancel"
msgstr "Скасувати"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__city
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "City"
msgstr "Місто"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_connection_test_result_view_form
msgid "Close"
msgstr "Закрити"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__code
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__code
msgid "Code"
msgstr "Код"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Commit Transactions"
msgstr "Здійснення транзакцій"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_commit
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_commit
msgid "Commit in Avatax"
msgstr "Здійснити в Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__company_id
msgid "Company"
msgstr "Компанія"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Company Code"
msgstr "Код компанії"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__country_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Country"
msgstr "Країна"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_uid
msgid "Created by"
msgstr "Створив"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_date
msgid "Created on"
msgstr "Створено"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_partner_code
msgid "Customer Code set in Avalara for this partner."
msgstr "Код клієнта встановлений в Avalara для цього партнера."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__description
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__description
msgid "Description"
msgstr "Опис"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Environment"
msgstr "Середовище"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Схема оподаткування"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_latitude
msgid "Geo Latitude"
msgstr "Широта"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_longitude
msgid "Geo Longitude"
msgstr "Довгота"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "Go to the configuration panel"
msgstr "Перейти до панелі налаштувань"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__id
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__id
msgid "ID"
msgstr "ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__is_already_valid
msgid "Is Already Valid"
msgstr "Вже дійсний"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_external_tax_mixin__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_move__is_avatax
msgid "Is Avatax"
msgstr "Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Latitude"
msgstr "Широта"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Longitude"
msgstr "Довгота"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax_unique_code
msgid "Mixin to generate unique ids for Avatax"
msgstr "Mixin для створення унікальних ідентифікаторів для Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_external_tax_mixin
msgid "Mixin to manage common parts of external tax calculation"
msgstr ""
"Mixin для керування загальними частинами зовнішнього розрахунку податків"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__name
msgid "Name"
msgstr "Назва"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not change the state of the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo не змогла змінити стан транзакції, пов’язаної з %(document)s в AvaTax\n"
"Перевірте статус `%(technical)s` в порталі AvaTax."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Odoo could not fetch the exemption codes of %(company)s"
msgstr "Odoo не вдалося отримати коди звільнення %(company)s"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not fetch the taxes related to %(document)s.\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo не змогла отримати відповідні податки %(document)s.\n"
"Перевірте статус `%(technical)s` в порталі AvaTax."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
msgid "Odoo could not validate the address of %(partner)s with Avalara."
msgstr "Odoo не може підтвердити адресу %(partner)sз  Avalara."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not void the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo не могла скасувати транзакцію, пов’язану з %(document)s в AvaTax\n"
"Перевірте статус `%(technical)s` в порталі AvaTax."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Original Address"
msgstr "Оригінальна адреса"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__partner_id
msgid "Partner"
msgstr "Партнер"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "Please add your AvaTax credentials"
msgstr "Додайте ваші облікові дані AvaTax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_template
msgid "Product"
msgstr "Товар"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_category
msgid "Product Category"
msgstr "Категорія товару"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_product
msgid "Product Variant"
msgstr "Варіант товару"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__production
msgid "Production"
msgstr "Виробництво"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__sandbox
msgid "Sandbox"
msgstr "Sandbox"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Save Validated"
msgstr "Зберегти підтвердження"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
msgid "Search operation not supported"
msgstr "Операція пошуку не підтримується"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__server_response
msgid "Server Response"
msgstr "Відповідь сервера"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__state_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "State"
msgstr "Область"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Street"
msgstr "Вулиця"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street2
msgid "Street2"
msgstr "Вулиця 2"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_validate_address
msgid "Suggests validated addresses from Avatax"
msgstr "Пропонує перевірені адреси від Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Synchronize the exemption codes from Avatax"
msgstr "Синхронізуйте коди звільнення від Avatax"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Test Result"
msgstr "Результат тесту"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_connection_test_result
msgid "Test connection with avatax"
msgstr "Тестове зʼєднання з avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_partner_code
msgid ""
"The Avalara Company Code for this company. Avalara will interpret as DEFAULT"
" if it is not set."
msgstr ""
"Код компанії Avalara для цієї компанії. Avalara інтерпретуватиме як DEFAULT,"
" якщо вона не встановлена."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The Avalara Tax Code is required for %(name)s (#%(id)s)\n"
"See https://taxcode.avatax.avalara.com/"
msgstr ""
"Потрібен код податку Avalara для %(name)s (#%(id)s)\n"
"Перегляньте https://taxcode.avatax.avalara.com/"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The following customer(s) need to have a zip, state and country when using "
"Avatax:"
msgstr ""
"Наступні клієнти повинні мати поштовий індекс, область і країну для "
"використання Avatax:"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_commit
msgid "The transactions will be committed for reporting in Avatax."
msgstr "Транзакції будуть зафіксовані для звітності в Avatax."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "This is already a valid address."
msgstr "Це вже дійсна адреса."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__setting_account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__setting_account_avatax
msgid "Use AvaTax"
msgstr "Викоритовувати AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__is_avatax
msgid "Use AvaTax API"
msgstr "Використати AvaTax API"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_use_upc
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_use_upc
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Use UPC"
msgstr "Використати UPC"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_use_upc
msgid "Use Universal Product Code instead of custom defined codes in Avalara."
msgstr ""
"Використовуйте універсальний код товару замість спеціально визначених кодів "
"в Avalara."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avatax_unique_code
msgid "Use this code to cross-reference in the Avalara portal."
msgstr "Використовуйте цей код для перехресних посилань на порталі Avalara."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__valid_country_ids
msgid "Valid Country"
msgstr "Дійсна країна"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_partner_form_inherit
msgid "Validate"
msgstr "Підтвердити"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_partner.py:0
msgid "Validate address of %s"
msgstr "Перевірити адресу %s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_address_validation
msgid ""
"Validate and correct the addresses of partners in North America with "
"Avalara."
msgstr "Перевірте та виправте адреси партнерів у Північній Америці з Avalara."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Validated Address"
msgstr "Підтверджена адреса"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_city
msgid "Validated City"
msgstr "Підтверджене місто"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_country_id
msgid "Validated Country"
msgstr "Підтверджена країна"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_state_id
msgid "Validated State"
msgstr "Підтверджена область"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street
msgid "Validated Street"
msgstr "Підтверджена вулиця"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street2
msgid "Validated Street2"
msgstr "Підвтерджена Вулиця2"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_zip
msgid "Validated Zip Code"
msgstr "Підтверджений індекс"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__zip
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Zip Code"
msgstr "Zip код"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/product.py:0
msgid "[%(code)s] %(description)s"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_template__avatax_category_id
msgid "https://taxcode.avatax.avalara.com/"
msgstr "https://taxcode.avatax.avalara.com/"
