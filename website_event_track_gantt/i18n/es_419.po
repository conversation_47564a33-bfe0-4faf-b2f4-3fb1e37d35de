# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_gantt
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Proposed By — </strong>"
msgstr "<strong>Propuesto por — </strong>"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Start — </strong>"
msgstr "<strong>Inicio — </strong>"

#. module: website_event_track_gantt
#. odoo-javascript
#: code:addons/website_event_track_gantt/static/src/event_track_form_view.js:0
msgid "Are you sure you want to delete this track?"
msgstr "¿Está seguro de que desea eliminar esta sesión?"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Delete"
msgstr "Eliminar"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Discard"
msgstr "Descartar"

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_event
msgid "Event"
msgstr "Evento"

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_track
msgid "Event Track"
msgstr "Sesión del evento"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Save"
msgstr "Guardar"

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_initial_date
msgid "Track Gantt Initial Date"
msgstr "Fecha de inicio de la sesión en vista de Gantt"

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_scale
msgid "Track Gantt Scale"
msgstr "Vista de Gantt de la sesión"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "Tracks"
msgstr "Sesiones"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Unschedule"
msgstr "Anular programación"
