# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_csv
#
# Translators:
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-26 09:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosa<PERSON><PERSON><PERSON><PERSON>@gmail.com>, "
"2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "Base Import"
msgstr "Osnovni uvoz"

#. module: account_bank_statement_import_csv
#. odoo-javascript
#: code:addons/account_bank_statement_import_csv/static/src/bank_statement_csv_import_action.js:0
msgid "Import Bank Statement"
msgstr "Uvezi bankovni izvod"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_account_journal
msgid "Journal"
msgstr "Izveštaj"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
msgid "Mixing CSV files with other file types is not allowed."
msgstr "Mešanje CSV datoteka sa drugim vrstama datoteka nije dozvoljeno."

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
msgid "Only one CSV file can be selected."
msgstr "Samo CSV datoteka može biti izabrana."

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
msgid "Rows must be sorted by date."
msgstr ""
