# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "%(company)s Payment Reminder - %(partner)s"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup.py:0
msgid "%(delay)s days (copy of %(name)s)"
msgstr ""

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Follow-up ' + object.display_name"
msgstr "'Follow-up ' + object.display_name"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "15 Days"
msgstr "15 дней"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "2023-09-06"
msgstr "2023-09-06"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "30 Days"
msgstr "30 дней"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "40 Days"
msgstr "40 дней"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "50 Days"
msgstr "50 дней"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "60 Days"
msgstr "60 дней"

#. module: account_followup
#: model:mail.template,body_html:account_followup.email_template_followup_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        It has come to our attention that you have&nbsp;an outstanding balance of <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"/>\n"
"                        <br/>\n"
"                        We kindly request that you take necessary action to settle this amount within the next 8 days.\n"
"                        <br/>\n"
"                        </p><div t-if=\"object._show_pay_now_button()\" class=\"d-flex\">\n"
"                            <a t-att-href=\"'/my/invoices/overdue'\" class=\"btn btn-primary\">Pay now</a>\n"
"                        </div>\n"
"                        If you have already made the payment after receiving this message, please disregard it.\n"
"                        Our accounting department is available if you require any assistance or have any questions.\n"
"                        <br/>\n"
"                        Thank you for your cooperation.\n"
"                        <br/>\n"
"                        Sincerely,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_4
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        Despite several reminders, your account is still not settled.\n"
"                        Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"                        I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"                        In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <br/>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_2
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"                        <br/>\n"
"                        It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services). Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br/>\n"
"                        If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"                        <br/>\n"
"                        Details of due payments is printed below.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Customer Statement</span>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid ""
"<span>Preferred address for follow-up reports. Selected by default when you "
"send reminders about overdue invoices.</span>"
msgstr ""
"<span>Предпочтительный адрес для последующих отчетов. Выбирается по "
"умолчанию при отправке напоминаний о просроченных счетах.</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>\n"
"                                Pending Invoices\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                Отложенные счета\n"
"                            </strong>"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"Имя последующего действия должно быть уникальным. Это имя уже задано для "
"другого действия."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__account_manager
msgid "Account Manager"
msgstr "Менеджер Аккаунта"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
msgid "Account Report Followup; Execute followup"
msgstr "Последующий отчет по счету; выполнение последующих действий"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "Действия"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Activity"
msgstr "Активность"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Activity Notes"
msgstr "Заметки о деятельности"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_type_id
msgid "Activity Type"
msgstr "Тип активности"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "Добавить заметку"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Add contacts to notify..."
msgstr "Добавьте контакты для уведомления..."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid "Add followers"
msgstr "Добавить последователей"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Address"
msgstr "Адрес"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__type
#: model:ir.model.fields,field_description:account_followup.field_res_users__type
msgid "Address Type"
msgstr "Тип адреса"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__join_invoices
msgid "Attach Invoices"
msgstr "Прикрепите счета-фактуры"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__attachment_ids
msgid "Attachment"
msgstr "Вложение"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__automatic
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Automatic"
msgstr "Автоматически"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Содержание тела соответствует шаблону"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__can_edit_body
msgid "Can Edit Body"
msgstr "Можно редактировать тело"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Cancel"
msgstr "Отменить"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "Close"
msgstr "Закрыть"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Communication"
msgstr "Связь"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "Компания"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Content Template"
msgstr "Шаблон содержимого"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body
msgid "Contents"
msgstr "Содержание"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "Клиентская ссылка:"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "Заявление клиентов"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Date"
msgstr "Дата"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "Дата:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up lines must be different per company"
msgstr "Дни последующих линий должны быть разными для каждой компании"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear %s,\n"
"\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"\n"
msgstr ""
"Уважаемые %s,\n"
"\n"
"\n"
"Исключение сделано, если это была наша ошибка, кажется, что следующая сумма остается невыплаченной. Пожалуйста, примите соответствующие меры, чтобы осуществить этот платеж в течение следующих 8 дней.\n"
"\n"
"Если ваш платеж был осуществлен после отправки этого письма, пожалуйста, проигнорируйте это сообщение. Не стесняйтесь обращаться в нашу бухгалтерию.\n"
"\n"
"С наилучшими пожеланиями,\n"
"\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear client, we kindly remind you that you still have unpaid invoices. "
"Please check them and take appropriate action. %s"
msgstr ""
"Уважаемый клиент, напоминаем вам, что у вас остались неоплаченные счета. "
"Пожалуйста, проверьте их и примите соответствующие меры. %s"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "Определите уровни последующих действий и связанные с ними мероприятия"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Demo Ref"
msgstr "Демо-реф"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Description"
msgstr "Описание"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__activity_default_responsible_type
msgid ""
"Determine who will be assigned to the activity:\n"
"- Follow-up Responsible (default)\n"
"- Salesperson: Sales Person defined on the invoice\n"
"- Account Manager: Sales Person defined on the customer"
msgstr ""
"Определите, кто будет назначен на это мероприятие:\n"
"- Ответственный за последующие действия (по умолчанию)\n"
"- Продавец: Лицо отдела продаж, определенное в счете-фактуре\n"
"- Менеджер по работе с клиентами: Сотрудник отдела продаж, определенный для клиента"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Due Date"
msgstr "Срок платежа"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "Дни исполнения"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email
msgid "Email"
msgstr "Email"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Recipients"
msgstr "Получатели E-mail"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Subject"
msgstr "Тема письма"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_recipient_ids
msgid "Extra Recipients"
msgstr "Дополнительные получатели"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up %(partner)s - %(date)s"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__type__followup
msgid "Follow-up Address"
msgstr "Адрес для последующих действий"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Критерии Последовательности"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_line_id
msgid "Follow-up Level"
msgstr "Уровень последующего наблюдения"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "Последующие уровни"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Последующий отчет"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__followup
msgid "Follow-up Responsible"
msgstr "Последующий Ответственный"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "Статус последующих действий"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "Последующие шаги"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.report_followup_print_all
msgid "Follow-up details"
msgstr "Подробности последующих действий"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up letter generated"
msgstr "Составлено последующее письмо"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_missing_information_wizard
msgid "Followup missing information wizard"
msgstr "Мастер последующей обработки недостающей информации"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"            possible to use print and e-mail templates to send specific messages to\n"
"            the customer."
msgstr ""
"Для каждого шага укажите действия, которые необходимо предпринять, и задержку в днях. Можно\n"
"            можно использовать шаблоны печати и электронной почты для отправки определенных сообщений клиенту\n"
"            клиенту."

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_4
msgid "Fourth reminder followup"
msgstr "Четвертое повторное напоминание"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid ""
"If set, those users will be added as followers on the partner and receive "
"notifications about any email reply made by the partner on the reminder "
"email."
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
msgid "In need of action"
msgstr "Нуждающиеся в действии"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Invoice Follow-ups"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Invoices Analysis"
msgstr "Анализ счетов"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__is_mail_template_editor
msgid "Is Editor"
msgstr "Редактор"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Kind reminder!"
msgstr "Доброе напоминание!"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__lang
msgid "Language"
msgstr "Язык"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Letter/Email Content"
msgstr "Содержание письма/электронной почты"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__mail_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__template_id
msgid "Mail Template"
msgstr "Шаблон сообщения"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__manual
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Manual"
msgstr "Руководство"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: code:addons/account_followup/wizard/followup_missing_information.py:0
msgid "Missing information"
msgstr "Недостающая информация"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Name"
msgstr "Имя"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
msgid "Next Reminder Date set to %s"
msgstr "Дата следующего напоминания установлена на %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_next_action_date
msgid "Next reminder"
msgstr "Следующее напоминание"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "Не требуются действия"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_note
msgid "Note"
msgstr "Заметка"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Notification"
msgstr "Уведомление"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_manual_reminder__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Необязательный язык перевода (код ISO) для выбора при отправке сообщения "
"электронной почты. Если он не задан, будет использоваться английская версия."
" Как правило, это должно быть выражение-заполнитель, обеспечивающее "
"соответствующий язык, например {{ object.partner_id.lang }}."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "Опции"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_origin
msgid "Origin"
msgstr "Источник"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Overdue Invoices"
msgstr "Просроченные счета"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__partner_id
msgid "Partner"
msgstr "Партнер"

#. module: account_followup
#: model:mail.template,name:account_followup.email_template_followup_1
msgid "Payment Reminder"
msgstr "Напоминание о платеже"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__print
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Print"
msgstr "Печать"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "Печать последующего письма"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process Follow-ups"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Reference"
msgstr "Справка"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Remind"
msgstr "Напомнить"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_reminder_type
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_reminder_type
msgid "Reminders"
msgstr "Напоминания"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__render_model
msgid "Rendering Model"
msgstr "Модель рендеринга"

#. module: account_followup
#: model:ir.model,name:account_followup.model_ir_actions_report
msgid "Report Action"
msgstr "Отчет о действиях"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Requires Follow-up"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_default_responsible_type
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_responsible_id
msgid "Responsible"
msgstr "Ответственный"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "SMS"
msgstr "СМС"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__salesperson
msgid "Salesperson"
msgstr "Продавец"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_activity
msgid "Schedule Activity"
msgstr "Деятельность по расписанию"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "Последующий поиск"

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_2
msgid "Second reminder followup"
msgstr "Второе последующее напоминание"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Send"
msgstr "Отправить"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send & Print"
msgstr "Отправить и распечатать"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send Email"
msgstr "Отправить Email"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send SMS Message"
msgstr "Отправить SMS-сообщение"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.manual_reminder_action
msgid "Send and Print"
msgstr "Отправить и распечатать"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms
msgid "Sms"
msgstr "Sms"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_body
msgid "Sms Body"
msgstr "Sms Body"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Sms Content"
msgstr "Sms-контент"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_template_id
msgid "Sms Template"
msgstr "Шаблон смс"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__subject
msgid "Subject"
msgstr "Тема"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_summary
msgid "Summary"
msgstr "Резюме"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.table_header_template_followup_report
msgid "Table Header"
msgstr "Заголовок таблицы"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Table Value"
msgstr "Стоимость таблицы"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__followup_next_action_date
msgid ""
"The date before which no follow-up action should be taken.\n"
"                You can set it manually if desired but it is automatically set when follow-ups are processed.\n"
"                The date is computed according to the following rules (depending on the follow-up levels):\n"
"                - default -> next date set in {next level delay - current level delay} days\n"
"                - if no next level -> next date set in {current level delay - previous level delay} days\n"
"                - if no next level AND no previous level -> next date set in {current level delay} days\n"
"                - if no level defined at all -> next date never automatically set"
msgstr ""
"Дата, до которой не следует предпринимать никаких последующих действий.\n"
"                При желании ее можно задать вручную, но при обработке последующих действий она устанавливается автоматически.\n"
"                Дата вычисляется в соответствии со следующими правилами (в зависимости от уровней последующих действий):\n"
"                - по умолчанию -> следующая дата устанавливается через {задержка следующего уровня - задержка текущего уровня} дней\n"
"                - если нет следующего уровня -> следующая дата устанавливается через {задержка текущего уровня - задержка предыдущего уровня} дней\n"
"                - если нет следующего уровня И нет предыдущего уровня -> следующая дата устанавливается через {задержка текущего уровня} дней\n"
"                - если уровень не определен вообще -> следующая дата никогда не устанавливается автоматически"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr "Документ(ы), который(ые) породил(и) счет-фактуру."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Can be negative if you want to send the reminder before the "
"invoice due date."
msgstr ""
"Количество дней после даты оплаты счета, которое необходимо выждать перед "
"отправкой напоминания. Может быть отрицательным, если вы хотите отправить "
"напоминание до наступления даты оплаты счета."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__followup_responsible_id
msgid ""
"The responsible assigned to manual followup activities, if defined in the "
"level."
msgstr ""
"Ответственный, назначенный на ручные последующие действия, если они "
"определены на уровне."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "Всего"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
msgid "Total Due"
msgstr "Всего взносов"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
msgid "Total Overdue"
msgstr "Общая просрочка"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoice_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoice_ids
msgid "Unpaid Invoice"
msgstr "Неоплаченный счет"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices_count
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices_count
msgid "Unpaid Invoices Count"
msgstr "Подсчет неоплаченных счетов"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "Амл"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "View partners"
msgstr "Посмотреть партнеров"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid ""
"We were not able to process some of the automated follow-up actions due to "
"missing information on the partners."
msgstr ""
"Мы не смогли обработать некоторые автоматизированные последующие действия "
"из-за отсутствия информации о партнерах."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "С просроченными счетами"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Мастер для отправки ручных напоминаний клиентам"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Write your message here..."
msgstr "Напишите ваше сообщение здесь ..."

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an Email, but no follow-up contact has any email "
"address set for customer '%s'"
msgstr ""
"Вы пытаетесь отправить электронное письмо, но ни один из последующих "
"контактов не имеет адреса электронной почты, установленного для клиента '%s'"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an SMS, but no follow-up contact has any mobile/phone"
" number set for customer '%s'"
msgstr ""
"Вы пытаетесь отправить SMS, но ни один из последующих контактов не имеет "
"номера мобильного телефона для клиента '%s'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days after due date"
msgstr "дней после наступления срока платежа"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "например, Первое письмо с напоминанием"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "payment reminder"
msgstr "напоминание о платеже"

#. module: account_followup
#: model:mail.template,subject:account_followup.demo_followup_email_template_2
#: model:mail.template,subject:account_followup.demo_followup_email_template_4
#: model:mail.template,subject:account_followup.email_template_followup_1
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Payment Reminder - {{ object.commercial_company_name }}"
msgstr ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Напоминание о платеже - {{ object.commercial_company_name }}"
