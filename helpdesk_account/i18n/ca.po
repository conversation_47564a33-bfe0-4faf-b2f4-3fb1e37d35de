# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_account
# 
# Translators:
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2024
# Jonatan Gk, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Revocació de moviment en compte"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/controllers/portal.py:0
msgid "Credit Note"
msgstr "Factura rectificativa"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/controllers/portal.py:0
#: code:addons/helpdesk_account/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoice_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
msgid "Credit Notes"
msgstr "Factures rectificatives"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoices_count
msgid "Credit Notes Count"
msgstr "Nombre de factures rectificatives"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Tiquet de Helpdesk"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/wizard/account_move_reversal.py:0
msgid "Helpdesk Ticket #%s"
msgstr "Tiquet helpdesk #%s"

#. module: helpdesk_account
#: model_terms:ir.ui.view,arch_db:helpdesk_account.view_account_move_reversal_inherit_helpdesk_account
msgid "Invoices to Refund"
msgstr "Factures de reemborsament"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__move_ids
msgid "Move"
msgstr "Assent."

#. module: helpdesk_account
#: model:ir.actions.act_window,name:helpdesk_account.helpdesk_ticket_action_refund
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
msgid "Refund"
msgstr "Abonament"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/models/account_move.py:0
msgid "Refund %(status)s"
msgstr ""

#. module: helpdesk_account
#: model:mail.message.subtype,name:helpdesk_account.mt_ticket_refund_created
msgid "Refund Created"
msgstr "Creat el refusat"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/wizard/account_move_reversal.py:0
msgid "Refund created"
msgstr "S'ha creat l'abonament"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__helpdesk_sale_order_id
msgid "Sales Order"
msgstr "Comanda"
