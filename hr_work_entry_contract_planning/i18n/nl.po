# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_planning
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,help:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        Bepaalt de bron voor de werkboekinggeneratie\n"
"\n"
"        Werkschema: Werkboekingen worden gegenereerd voor onderstaande werkuren.\n"
"        Aanwezigheden: Werkboekingen worden gegenereerd op basis van de werknemer's aanwezigheden (Aanwezigheden app vereist)\n"
"        Planning: Werkboekingen worden gegenereerd op basis van de werknemer's planning (Planning app vereist)\n"
"    "

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_contract
msgid "Employee Contract"
msgstr "Arbeidsovereenkomst"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR-werkboeking"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields.selection,name:hr_work_entry_contract_planning.selection__hr_contract__work_entry_source__planning
msgid "Planning"
msgstr "Planning"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_planning_slot
msgid "Planning Shift"
msgstr "Planning dienst"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_work_entry__planning_slot_id
msgid "Planning Slot"
msgstr "Planningsslot"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Hergenereren werknemers werkboekingen"

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't delete "
"it."
msgstr ""
"Deze dienstgegevens zijn gekoppeld aan een gevalideerde werkboeking. Je kunt"
" ze niet verwijderen."

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't modify "
"it."
msgstr ""
"Deze dienstgegevens zijn gekoppeld aan een gevalideerde werkboeking. Je kunt"
" ze niet wijzigen."

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "Bron werkboekingen"
