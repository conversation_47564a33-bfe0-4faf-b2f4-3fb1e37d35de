<odoo>
    <template id="isky_18_realestate_broker.portal_create_unit" name="Create Unit">
        <t t-call="portal.portal_layout">
            <div class="container">
                <ul class="nav nav-tabs mb-4" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#general" role="tab">
                            General
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#basic_info" role="tab">Basic Info</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#unit_price" role="tab">Unit Price</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#utilities" role="tab">Utilities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#financials" role="tab">Financials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#counters" role="tab">Counters</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#warnings" role="tab">Warnings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#location" role="tab">Location</a>
                    </li>
                </ul>

                <h3 class="mb-4">Create New Unit</h3>

                <!-- Form to submit unit data -->
                <form action="/my/unit/create" method="post" class="needs-validation">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="general" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        General Information
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="unit_name">Unit Name</label>
                                                    <input type="text" name="unit_name" id="unit_name"
                                                           class="form-control"/>
                                                </div>
<!--                                                <div class="mb-3">-->
<!--                                                    <label class="fw-bold" for="abbreviation">Abbreviation</label>-->
<!--                                                    <input type="text" name="abbreviation" id="abbreviation"-->
<!--                                                           class="form-control"/>-->
<!--                                                </div>-->
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="unit_code">Unit Code</label>
                                                    <input type="text" name="unit_code" id="unit_code"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="unit_number">Unit Number</label>
                                                    <input type="number" name="unit_number" id="unit_number"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="unit_type">Unit Type</label>
                                                    <select name="unit_type" id="unit_type" class="form-select">
                                                        <option value="">Select Unit Type</option>
                                                        <t t-foreach="unit_types" t-as="type">
                                                            <option t-att-value="type[0]" t-esc="type[1]"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="retail-section" style="display:none;">
                                                    <label class="fw-bold" for="unit_type">Retail Type</label>
                                                    <select name="retail_type" id="retail_type" class="form-select">
                                                        <option value="">Select Retail Type</option>
                                                        <t t-foreach="retail_type" t-as="type">
                                                            <option t-att-value="type[0]" t-esc="type[1]"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="office-section" style="display:none;">
                                                    <label class="fw-bold" for="office_type">Office Type</label>
                                                    <select name="retail_type" id="office_type" class="form-select">
                                                        <option value="">Select Office Type</option>
                                                        <t t-foreach="office_type" t-as="type">
                                                            <option t-att-value="type[0]" t-esc="type[1]"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="resid-section-div" style="display:none;">
                                                    <label class="fw-bold" for="residential_type">Residential Type</label>
                                                    <select name="residential_type" id="residential_type" class="form-select">
                                                        <option value="">Select Office Type</option>
                                                        <t t-foreach="residential_type" t-as="type">
                                                            <option t-att-value="type[0]" t-esc="type[1]"/>
                                                        </t>
                                                    </select>
                                                    <br/>
                                                    <label class="fw-bold" for="multi_floors">Multiple floors</label>
                                                    <input type="checkbox" id='multi_floors_boolean' name="multi_floors_boolean"/>
                                                </div>
                                                <div class="mb-3" id="floors-numbers-section" style="display:none;">
                                                    <label class="fw-bold" for="floors_number">Floors Count</label>
                                                    <input type="number" id='floors_number' name='floors_number' class="form-control" min="1"
                                                           max="50"
                                                           step="1" value="1"/>

                                                </div>
                                                <div class="mb-3" id="unit_purpose_div">
                                                    <label class="form-label">
                                                        <strong>Unit Purpose</strong>
                                                    </label>
                                                    <select class="form-select" id="unit_purpose" name="unit_purpose">
                                                        <option value="to_sale"></option>
                                                        <option value="to_sale">To Sale</option>
                                                        <option value="to_rent">To Rent</option>
                                                        <option value="to_sale_or_rent">To Sale Or Rent</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="sale_price_div" style="display:none;">
                                                    <label class="form-label">
                                                        <strong>Sale Price</strong>
                                                    </label>
                                                    <input type="number" id='sale_price_value' class="form-control"
                                                           step="1000000" value="1000000"/>
                                                </div>

                                                <div class="mb-3" id="rent_price_div" style="display:none;">
                                                    <label class="form-label">
                                                        <strong>Rent Price</strong>
                                                    </label>
                                                    <input type="number" id='rent_price_value' name='rent_price_value' class="form-control"
                                                           step="10000"
                                                           value="1000000"/>
                                                </div>

                                                <div class="mb-3" id="rent_and_sale_price_div" style="display:none;">
                                                    <label class="form-label">
                                                        <strong>Sale Price</strong>
                                                    </label>
                                                    <input type="number" id='sale_price_value' name='sale_price_value' class="form-control"
                                                           step="1000000" value="1000000"/>
                                                    <br/>
                                                    <label class="form-label">
                                                        <strong>Rent Price</strong>
                                                    </label>
                                                    <input type="number" id='rent_price_value' name='rent_price_value' class="form-control"
                                                           step="10000"
                                                           value="1000000"/>
                                                </div>
                                                <div class="mb-3" id="currencies_div">
                                                    <label class="fw-bold" for="currencies-section">Currencies</label>
                                                    <select name="currency_id" id="currency_id"
                                                            class="form-select">
                                                        <option value="">Select Currency</option>
                                                        <t t-foreach="currencies" t-as="curr">
                                                            <option t-att-value="curr.id" t-esc="curr.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="projects_div">
                                                    <label class="fw-bold" for="projects-section">Project</label>
                                                    <select name="project_id" id="project_id"
                                                            class="form-select">
                                                        <option value="">Select Project Type</option>
                                                        <t t-foreach="projects" t-as="project">
                                                            <option t-att-value="project.id" t-esc="project.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="developer_div">
                                                    <label class="fw-bold" for="developer-section">Developer</label>
                                                    <select name="developer_id" id="developer_id"
                                                            class="form-select">
                                                        <option value="">select developer</option>
                                                        <t t-foreach="developers" t-as="developer">
                                                            <option t-att-value="developer.id" t-esc="developer.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="unit_status_div">
                                                    <label class="form-label">
                                                        <strong>Unit Status</strong>
                                                    </label>
                                                    <select class="form-select" id="unit_status" name="unit_status">
                                                        <option value="select unit status"></option>
                                                        <option value="available">Available</option>
                                                        <option value="eoi">Eoi</option>
                                                        <option value="reserved">Reserved</option>
                                                        <option value="contracted">Contracted</option>
                                                        <option value="unavailable">UnAvailable</option>
                                                        <option value="sold">Sold</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">

                                                <div class="mb-3" id="sale_phase_div">
                                                    <label class="fw-bold" for="sale-phase-section">Sale Phase</label>
                                                    <select name="sale_phase_id" id="sale_phase_id"
                                                            class="form-select">
                                                        <option value="">select sale phase</option>
                                                        <t t-foreach="phases" t-as="phase">
                                                            <option t-att-value="phase.id" t-esc="phase.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="customer_div">
                                                    <label class="fw-bold" for="customer-section">Customer</label>
                                                    <select name="customer_id" id="customer_id"
                                                            class="form-select">
                                                        <option value="">select customer</option>
                                                        <t t-foreach="developers" t-as="developers">
                                                            <option t-att-value="developers.id"
                                                                    t-esc="developers.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="bua_div">
                                                    <label class="form-label">
                                                        <strong>BUA</strong>
                                                    </label>
                                                    <input type="number" id="bua_value" name="bua_value" class="form-control"/>
                                                </div>
                                                <div class="mb-3" id="checkbox_div">
                                                    <label class="fw-bold" for="yard_boolean">Yard</label>
                                                    <input type="checkbox" id='yard_boolean' name="yard_boolean"/>
                                                </div>
                                                <div class="mb-3" id="unit_sale_type_div">
                                                    <label class="form-label">
                                                        <strong>Unit Sale Type</strong>
                                                    </label>
                                                    <select class="form-select" id="unit_sale_type" name="unit_sale_type">
                                                        <option value="">select unit sale type</option>
                                                        <option value="primary">Primary</option>
                                                        <option value="resale">Resale</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="compound_div">
                                                    <label class="fw-bold" for="compound_boolean">Compound</label>
                                                    <input type="checkbox" id='compound_boolean' name="compound_boolean"/>
                                                </div>
                                                <div class="mb-3" id="finished_type_div">
                                                    <label class="form-label">
                                                        <strong>Finished</strong>
                                                    </label>
                                                    <select class="form-select" id="unit_finished_sale_type" name="unit_finished_sale_type">
                                                        <option value="">select unit sale type</option>
                                                        <option value="finished">Finished</option>
                                                        <option value="semi_finished">Semi-Finished</option>
                                                        <option value="core_shell">Core and Shell</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3" id="num_rooms_div">
                                                    <label class="form-label">
                                                        <strong>Number Of Rooms</strong>
                                                    </label>
                                                    <input type="number" id='num_rooms_value' name="num_rooms_value"
                                                           class="form-control"/>
                                                </div>

                                                <div class="mb-3" id="num_bathrooms_div">
                                                    <label class="form-label">
                                                        <strong>Number Of Bathrooms</strong>
                                                    </label>
                                                    <input type="number" id='num_bathrooms_value'
                                                           name="num_bathrooms_value"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3" id="ready_div">
                                                    <label class="fw-bold" for="ready_move_boolean">Ready To Move
                                                    </label>
                                                    <input type="checkbox" id='ready_move' name="ready_to_move"/>
                                                </div>
                                                <div class="mb-3" id="published_div">
                                                    <label class="fw-bold" for="published_boolean">Is Published</label>
                                                    <input type="checkbox" id='published' name="published"/>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="basic_info" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Basic Information
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5 class="fw-bold mb-4" style="text-decoration: underline;">Area Info
                                                </h5>
                                                <div class="mb-3" id="in-gross-area-section">
                                                    <label class="fw-bold" for="in-gross-area">Indoor Gross Area</label>
                                                    <input type="number" id='in-gross-area' name="in-gross-area"
                                                           class="form-control" min="1"/>
                                                </div>
                                                <div class="mb-3" id="in-net-area-section">
                                                    <label class="fw-bold" for="in-net-area">Indoor Net Area</label>
                                                    <input type="number" id='in-net-area' name="in-net-area"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3" id="out-gross-area-section">
                                                    <label class="fw-bold" for="out-gross-area">Outdoor Gross Area
                                                    </label>
                                                    <input type="number" id='out-gross-area' name="out-gross-area"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3" id="net-total-area-section">
                                                    <label class="fw-bold" for="net-total-area">Net Total Area</label>
                                                    <input type="number" id='net-total-area' name="net-total-area"
                                                           class="form-control"/>
                                                </div>
                                                <div class="mb-3" id="gross-total-area-section">
                                                    <label class="fw-bold" for="gross-total-area">Gross Total Area
                                                    </label>
                                                    <input type="number" id='gross-total-area' name="gross-total-area"
                                                           class="form-control"/>
                                                </div>
                                                <h6 class="fw-bold mb-4" style="text-decoration: underline;">View</h6>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Garden View</label>
                                                    <input type="checkbox" id='garden_view' name="garden_view"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Pool View</label>
                                                    <input type="checkbox" id='pool_view' name="pool_view"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">City View</label>
                                                    <input type="checkbox" id='city_view' name="city_view"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Sea View</label>
                                                    <input type="checkbox" id='sea_view' name="sea_view"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Lagoon View</label>
                                                    <input type="checkbox" id='lagoon_view' name="lagoon_view"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Internal View</label>
                                                    <input type="checkbox" id='internal_view' name="internal_view"/>
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <h5 class="fw-bold mb-4" style="text-decoration: underline;">Has</h5>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Garden</label>
                                                    <input type="checkbox" id='has_garden' name="has_garden"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Roof</label>
                                                    <input type="checkbox" id='has_roof' name="has_roof"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Parking</label>
                                                    <input type="checkbox" id='has_parking' name="has_parking"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Balcony</label>
                                                    <input type="checkbox" id='has_balcony' name="has_balcony"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Pool</label>
                                                    <input type="checkbox" id='has_pool' name="has_pool"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Has Club Membership</label>
                                                    <input type="checkbox" id='has_club' name="has_club"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="unit_price" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Unit Price
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="mb-3" id="unit_price_sale_div" style="display:none;">
                                                <label class="form-label">
                                                    <strong>Sale Maintenance Price</strong>
                                                </label>
                                                <input type="number" id='sale_maintenance_price'
                                                       name="sale_maintenance_price"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Down Payment Percentage</strong>
                                                </label>
                                                <input type="number" id='down_payment_percentage'
                                                       name="down_payment_percentage" min="1" max="100" step="1"
                                                       value="1"
                                                       class="form-control"/>
                                            </div>
                                            <div class="mb-3" id="unit_price_rent_div" style="display:none;">
                                                <label class="form-label">
                                                    <strong>Rent Maintenance Price</strong>
                                                </label>
                                                <input type="number" id='rent_maintenance_price'
                                                       name="rent_maintenance_price"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Rent Period</strong>
                                                </label>
                                                <select class="form-select" id="rent_period" name="rent_period">
                                                    <option value=""></option>
                                                    <option value="monthly">Monthly</option>
                                                    <option value="quarterly">Quarterly</option>
                                                    <option value="yearly">Yearly</option>
                                                </select>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Maintenance Period</strong>
                                                </label>
                                                <select class="form-select" id="maintenance_period"
                                                        name="maintenance_period">
                                                    <option value=""></option>
                                                    <option value="monthly">Monthly</option>
                                                    <option value="quarterly">Quarterly</option>
                                                    <option value="yearly">Yearly</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="utilities" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Utilities Information
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="fw-bold">Heating Source</label>
                                                    <input type="checkbox" id='heating_source' name="heating_source"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Solar System</label>
                                                    <input type="checkbox" id='solar_system' name="solar_system"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Electricity Meter</label>
                                                    <input type="checkbox" id='electricity_meter'
                                                           name="electricity_meter"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Gas Meter</label>
                                                    <input type="checkbox" id='gas_meter' name="gas_meter"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Water Meter</label>
                                                    <input type="checkbox" id='water_meter' name="water_meter"/>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="fw-bold">Landline</label>
                                                    <input type="checkbox" id='landline' name="landline"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Internet</label>
                                                    <input type="checkbox" id='internet' name="internet"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Air Condition</label>
                                                    <input type="checkbox" id='air_condition' name="air_condition"/>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Cooling Water</label>
                                                    <input type="checkbox" id='cooling_water' name="cooling_water"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="financials" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Financials Information
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="mb-3" id="financials_sale_div" style="display:none;">
                                                <label class="form-label">
                                                    <strong>Indoor Price/SQM</strong>
                                                </label>
                                                <input type="number" id='indoor_price_sqm'
                                                       name="indoor_price_sqm"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Outdoor Price/SQM</strong>
                                                </label>
                                                <input type="number" id='outdoor_price_sqm'
                                                       name="outdoor_price_sqm"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Indoor+Outdoor Price/SQM</strong>
                                                </label>
                                                <input type="number" id='in_out_price_sqm'
                                                       name="in_out_price_sqm"
                                                       class="form-control"/>
                                            </div>
                                            <div class="mb-3" id="financials_rent_div" style="display:none;">
                                                <label class="form-label">
                                                    <strong>Indoor Price/Month/SQM</strong>
                                                </label>
                                                <input type="number" id='indoor_price_month_sqm'
                                                       name="indoor_price_month_sqm"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Outdoor Price/Month/SQM</strong>
                                                </label>
                                                <input type="number" id='outdoor_price_sqm_month'
                                                       name="outdoor_price_sqm_month"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Indoor+Outdoor Price/Month/SQM</strong>
                                                </label>
                                                <input type="number" id='in_out_price_sqm_month'
                                                       name="in_out_price_sqm_month"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Service Charge /SQM</strong>
                                                </label>
                                                <input type="number" id='service_charge_sqm'
                                                       name="service_charge_sqm"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Marketing Fees Annually Percent</strong>
                                                </label>
                                                <input type="number" id='marketing_fees'
                                                       name="marketing_fees"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Payment Terms Duration</strong>
                                                </label>
                                                <input type="number" id='payment_terms_duration'
                                                       name="payment_terms_duration"
                                                       class="form-control"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade" id="warnings" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Warnings
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="mb-3" id="warning_div">
                                                <label class="form-label">
                                                    <strong>Warning</strong>
                                                </label>
                                                <input type='text' name="warning" id="warning"
                                                       class="form-control"/>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade" id="location" role="tabpanel">
                            <div class="row">
                                <div class="card shadow-lg border-0">
                                    <div class="card-header bg-primary text-white">
                                        Location Informations
                                    </div>

                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5 class="fw-bold mb-4" style="text-decoration: underline;">Google Maps
                                                    Location
                                                </h5>
                                                <label class="form-label">
                                                    <strong>Location Url</strong>
                                                </label>
                                                <input type="text" id='location_url'
                                                       name="location_url"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Latitude</strong>
                                                </label>
                                                <input type="number" id='latitude_value'
                                                       name="latitude_value"
                                                       class="form-control"/>
                                                <br/>
                                                <label class="form-label">
                                                    <strong>Longitude</strong>
                                                </label>
                                                <input type="text" id='longitude_value'
                                                       name="longitude_value"
                                                       class="form-control"/>

                                            </div>
                                            <div class="col-md-6">
                                                <h5 class="fw-bold mb-4" style="text-decoration: underline;">Location
                                                </h5>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="countries">Country</label>
                                                    <select name="country_id" id="country_id" class="form-select">
                                                        <option value="">select country</option>
                                                        <t t-foreach="countries" t-as="country">
                                                            <option t-att-value="country.id" t-esc="country.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="countries">Governorate</label>
                                                    <select name="governorate_id" id="governorate_id"
                                                            class="form-select">
                                                        <option value="">select governorate</option>
                                                        <t t-foreach="governorate" t-as="gover">
                                                            <option t-att-value="gover.id" t-esc="gover.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="countries">City</label>
                                                    <select name="city_id" id="city_id" class="form-select">
                                                        <option value="">select city</option>
                                                        <t t-foreach="cities" t-as="city">
                                                            <option t-att-value="city.id" t-esc="city.name"/>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold" for="countries">Neighborhood</label>
                                                    <select name="neighborhood_id" id="neighborhood_id"
                                                            class="form-select">
                                                        <option value="">select neighborhood</option>
                                                        <t t-foreach="neighborhoods" t-as="neigh">
                                                            <option t-att-value="neigh.id" t-esc="neigh.name"/>
                                                        </t>
                                                    </select>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="submit" class="btn btn-primary">Create Unit</button>
                        </div>
                    </div>
                </form>
            </div>
        </t>
        <script>
            document.getElementById('unit_type').addEventListener('change', function() {
            var unit_type = this.value;
            console.log(unit_type);
            retail_section = document.getElementById('retail-section');
            office_section = document.getElementById('office-section');
            res_section = document.getElementById('resid-section-div');

            retail_section.style.display = 'none';
            office_section.style.display = 'none';
            res_section.style.display = 'none';

            if (unit_type == 'retail'){
            console.log('reeeeeeeeeeeeeeeee')
            retail_section.style.display = 'block';
            } else if (unit_type == 'office'){
            office_section.style.display = 'block';
            } else if (unit_type == 'residential'){
            res_section.style.display = 'block';
            }
            })

            document.getElementById('multi_floors_boolean').addEventListener('change', function() {
            var res = this.value;
            var numbers_section = document.getElementById('floors-numbers-section');
            numbers_section.style.display = 'display';
            if (res == 'on'){
            numbers_section.style.display = 'block';
            }
            })

            document.getElementById('unit_purpose').addEventListener('change', function() {
            var res = this.value;
            sale_div = document.getElementById('sale_price_div');
            rent_div = document.getElementById('rent_price_div');
            rent_sale_div = document.getElementById('rent_and_sale_price_div');
            unit_price_sale_div = document.getElementById('unit_price_sale_div');
            unit_price_rent_div = document.getElementById('unit_price_rent_div');
            financials_sale_div = document.getElementById('financials_sale_div');
            financials_rent_div = document.getElementById('financials_rent_div');

            sale_div.style.display = 'none';
            rent_div.style.display = 'none';
            rent_sale_div.style.display = 'none';
            unit_price_sale_div.style.display = 'none';
            unit_price_rent_div.style.display = 'none';
            financials_sale_div.style.display = 'none';
            financials_rent_div.style.display = 'none';

            if (res == 'to_sale'){
            sale_div.style.display = 'block';
            unit_price_sale_div.style.display = 'block';
            financials_sale_div.style.display = 'block';
            } else if (res == 'to_rent') {
            rent_div.style.display = 'block';
            unit_price_rent_div.style.display = 'block';
            financials_rent_div.style.display = 'block';
            }else if (res == 'to_sale_or_rent') {
            rent_sale_div.style.display = 'block';
            }
            console.log('valueee', res)
            })
            <!--            document.getElementById('projects-section').addEventListener('change', function() {-->
            <!--            var value = this.value;-->
            <!--            console.log(this);-->
            <!--            var data_dict = document.querySelector('#data');;-->
            <!--            var dataDict = JSON.parse(data_dict.innerText || {})-->
            <!--            console.log(dataDict)-->
            <!--            console.log(dataDict[value])-->
            <!--            var testValue = function(id){-->
            <!--            return dataDict[value];-->
            <!--            }-->
            <!--            console.log('dddddd', testValue);-->
            <!--            })-->
        </script>
    </template>
</odoo>
