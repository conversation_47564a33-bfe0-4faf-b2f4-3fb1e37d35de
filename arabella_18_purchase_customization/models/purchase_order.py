from odoo import models, fields



class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    state = fields.Selection([
        ('draft', 'RFQ'),
        ('sent', 'RFQ Sent'),
        ('to approve', 'To Approve'),
        ("manager_approval", "Manager Approval"),
        ("gm_approval", "General Manager Approval"),
        ('purchase', 'Purchase Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled')
    ], string='Status', readonly=True, index=True, copy=False, default='draft', tracking=True)


    def action_set_manager_approval(self):
        for order in self:
            order.state = 'manager_approval'

    def action_set_gm_approval(self):
        for order in self:
            order.state = 'gm_approval'


    # overriding the base method of approval
    def button_confirm(self):
        for order in self:
            if order.state not in ['gm_approval']:
                continue
            order.order_line._validate_analytic_distribution()
            order._add_supplier_to_product()
            # Deal with double validation process
            if order._approval_allowed():
                order.button_approve()
            else:
                order.write({'state': 'to approve'})
            if order.partner_id not in order.message_partner_ids:
                order.message_subscribe([order.partner_id.id])
        return True