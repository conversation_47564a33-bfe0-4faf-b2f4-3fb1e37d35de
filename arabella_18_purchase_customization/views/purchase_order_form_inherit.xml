<odoo>
    <data>
        <record id="inherited_model_view_form" model="ir.ui.view">
            <field name="name">purchase.order.form.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                 <!-- Modify the header buttons -->
                <!-- modify Confirm buttons -->
                <xpath expr="//header/button[@name='button_confirm'][1]" position="attributes">
                    <!-- Make the confirm button visible only in GM approval state -->
                    <attribute name="invisible">state != 'gm_approval' </attribute>
                    <attribute name="groups">arabella_18_purchase_customization.general_manager_group</attribute>
                </xpath>
                <!-- remove the second Confirm button -->
                <xpath expr="//header/button[@name='button_confirm'][2]" position="replace"/>


                <!-- modify send by CANCEL button -->
                <xpath expr="//header/button[@name='button_cancel']" position="attributes">
                    <!-- Make the cancel button invisible in Quotation state -->
                    <attribute name="invisible">state not in ('gm_approval', 'manager_approval', 'draft', 'to approve', 'sent', 'purchase') </attribute>
                </xpath>
                <xpath expr="//header/button[@name='print_quotation'][1]" position="after">
                    <!-- Add Manager Approval  button -->
                    <button name="action_set_manager_approval"
                            string="manager approve"
                            type="object"
                            class="btn-primary"
                            invisible="state not in ('draft', 'sent')"
                            groups="arabella_18_purchase_customization.manager_group"
                            />


                    <!-- Add General Manager Approval button -->
                    <button name="action_set_gm_approval"
                            string="GM approve"
                            type="object"
                            class="btn-primary"
                            invisible="state != 'manager_approval'"
                            groups="arabella_18_purchase_customization.general_manager_group"
                            />



                </xpath>
                <!-- Update the statusbar to show all states -->
            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="statusbar_visible">draft,sent,manager_approval,gm_approval,purchase</attribute>
            </xpath>
            </field>
        </record>
    </data>
</odoo>