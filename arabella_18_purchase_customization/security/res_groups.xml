<odoo>
    <data>
        <!--  manager group  -->
        <record id="manager_group" model="res.groups">
            <field name="name">Purchase manager</field>
            <field name="category_id" ref="base.module_category_inventory_purchase"/>
            <field name="implied_ids" eval="[(4, ref('purchase.group_purchase_manager'))]"/>
          </record>

        <!--  General manager group  -->
         <record id="general_manager_group" model="res.groups">
            <field name="name">Purchase general manager</field>
            <field name="category_id" ref="base.module_category_inventory_purchase"/>
            <field name="implied_ids" eval="[(4, ref('purchase.group_purchase_manager'))]"/>
          </record>

    </data>
</odoo>