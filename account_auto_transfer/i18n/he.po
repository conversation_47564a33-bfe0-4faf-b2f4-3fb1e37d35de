# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# yael terner, 2024
# NoaFarkash, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# da<PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span>ל</span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_chart_template
msgid "Account Chart Template"
msgstr "תבנית לוח חשבונות"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "מודל העברת חשבון"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "שורת מודל להעברת חשבון"

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
msgid "Account automatic transfers: Perform transfers"
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "הפעל"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__active
msgid "Active"
msgstr "פעיל"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these analytic accounts to the destination account"
msgstr ""
"מוסיף תנאי שרק השורות המתאימות לניתוח החשבונות, יועברו מחשבון המקור לחשבון "
"היעד."

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these partners to the destination account"
msgstr ""
"מוסיף תנאי להעביר רק את השורות מחשבון המקור לחשבון היעד של השותפים האלו"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "מסנן אנליטי"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_search
msgid "Archived"
msgstr "בארכיון"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "העברה אוטומטית"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (%(percent)s%% from account %(origin_account)s)"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (-%s%%)"
msgstr "העברה אוטומטית (-%s%%)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (entries with analytic account(s): %(analytic_accounts)s "
"and partner(s): %(partners)s)"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "העברה אוטומטית (פקודות עם חשבונות אנליטיים: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "העברות אוטומטיות (רשומות עם שותף(ים):%s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s and partner(s): %(partners)s)"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s)"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with partner(s): "
"%(partners)s)"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (to account %s)"
msgstr "העברה אוטומטית (לחשבון %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
msgid "Automatic Transfers"
msgstr "העברות אוטומטיות"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "חברה"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "חברה מקושרת ליומן זה"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "חשב העברה"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "תיאור"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "חשבון יעד"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "חשבונות יעד"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "יומן יעד"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "השבת "

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "מושבת"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "תדירות"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "פקודות יומן שנוצרו"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "תנועות שנוצרו"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "מזהה"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS Automatic Transfers"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS rent expense transfer"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_journal
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "יומן"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move_line
msgid "Journal Item"
msgstr "תנועת יומן"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "חודשי"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "כמות מזהי תנועות"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "מודל תנועה"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "מודלים של תנועה"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "שם"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "התרחשות חשבון אחת בלבד לפי מודל העברה"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "חשבונות מקור"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
msgid "Originating Model"
msgstr "מודל מקור"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "מסנן שותפים"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "אחוז"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "אחוז (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "האחוז הוא לקריאה בלבד"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid ""
"Percentage of the sum of lines from the origin accounts will be transferred "
"to the destination account"
msgstr "אחוז מסכום השורות מחשבונות המקור יועבר לחשבון היעד"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "תקופה"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "רבעוני"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "רץ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "רצף"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "סטטוס"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "תאריך סיום"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The analytic filter %s is duplicated"
msgstr "פילטר ניתוח נתונים %s שוכפל"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"The partner filter %(partner_filter)s in combination with the analytic "
"filter %(analytic_filter)s is duplicated"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The partner filter %s is duplicated"
msgstr "פילטר השותף %s שוכפל"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The total percentage (%s) should be less or equal to 100!"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "אחוז כולל"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "מודל העברה"

#. module: account_auto_transfer
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "העברות"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "שנתי"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "Yearly liabilites auto transfers"
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has draft moves attached "
"('%s'). Please delete them before deleting this transfer."
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has posted moves attached "
"('%s')."
msgstr ""

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/account_move_line.py:0
msgid "You cannot set Tax on Automatic Transfer's entries."
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr "לדוג' העברה של הוצאה חדשית"
