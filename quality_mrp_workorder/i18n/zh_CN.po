# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr "<strong>批次/序列号：</strong>"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Work Order: </strong>"
msgstr "<strong>工单：</strong>"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "支票"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
msgid "Component Lot/Serial"
msgstr "组件批次/序列号"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Create a Quality Alert"
msgstr "创建质量警报"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Discard"
msgstr "丢弃"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.mrp_production_workorder_tree_editable_view_inherit_quality
msgid "Done"
msgstr "已完成"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Fail"
msgstr "失败"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Failure"
msgstr "失败"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_quality_mrp_workorder
msgid "Finished Lot/Serial"
msgstr "成品批次/系列"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_lot
msgid "Lot/Serial"
msgstr "批次/序列号"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "制造订单"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Manufacturing Steps"
msgstr "制造步骤"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Measure:"
msgstr "测量："

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_quality_check_confirmation_dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Open spreadsheet"
msgstr "打开电子表格"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_search_inherit_quality_mrp_workorder
msgid "Operation"
msgstr "作业"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Pass"
msgstr "通过"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_template
msgid "Product"
msgstr "产品"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "产品移动(移库明细)"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "质量检查"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Quality Check Failed"
msgstr "质量检查失败"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:ir.actions.act_window,name:quality_mrp_workorder.quality_check_action_wo
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
msgid "Quality Checks"
msgstr "质量检查"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "质量控制点"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Quality Points"
msgstr "质量点"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Save"
msgstr "保存"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_quality_check__operation_id
msgid "Step"
msgstr "步骤"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Success"
msgstr "成功"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The On-demand frequency is not possible with work order quality points."
msgstr "根据工单质量点数来设置“按需”频率是不可行的。"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr "数量质量检查类型与制造操作类型不兼容。"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Validate"
msgstr "验证"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "工单"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_tree_inherit_mrp_workorder
msgid "Work Order Operation"
msgstr "工单作业"
