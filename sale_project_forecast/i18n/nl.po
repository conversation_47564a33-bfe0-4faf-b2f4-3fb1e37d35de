# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project_forecast
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_project_forecast
#: model:ir.model.fields,help:sale_project_forecast.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_project_forecast.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order.                 With the 'auto plan' "
"feature, only employees with this role will be automatically assigned shifts"
" for Sales Orders containing this service.                 The system will "
"consider employee availability and the remaining time to be planned."
"                 You can also manually schedule open shifts for your Sales "
"Order or assign them to any employee you prefer."
msgstr ""
"Als dit is ingeschakeld, wordt er automatisch een dienst gegenereerd voor de"
" geselecteerde rol bij het bevestigen van de verkooporder.                 "
"Met de functie 'Automatisch plannen' krijgen alleen medewerkers met deze rol"
" automatisch diensten toegewezen voor verkooporders met deze service."
"                 Het systeem houdt rekening met de beschikbaarheid van "
"medewerkers en de resterende in te plannen tijd.                 Je kunt ook"
" handmatig open diensten inplannen voor je Verkooporder of deze toewijzen "
"aan een medewerker naar keuze."

#. module: sale_project_forecast
#: model:ir.model.fields,field_description:sale_project_forecast.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_project_forecast.field_product_template__planning_enabled
msgid "Plan Services"
msgstr "Diensten plannen"

#. module: sale_project_forecast
#: model:ir.model,name:sale_project_forecast.model_planning_slot
msgid "Planning Shift"
msgstr "Planning dienst"

#. module: sale_project_forecast
#: model:ir.model,name:sale_project_forecast.model_product_template
msgid "Product"
msgstr "Product"

#. module: sale_project_forecast
#: model:ir.model.fields,field_description:sale_project_forecast.field_planning_slot__sale_line_id
msgid "Sales Order Item"
msgstr "Verkooporderregel"

#. module: sale_project_forecast
#: model:ir.model,name:sale_project_forecast.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_project_forecast
#: model:ir.model.fields,help:sale_project_forecast.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""
"Verkooporderregel waarvoor deze dienst zal worden uitgevoerd. Wanneer "
"verkooporders automatisch worden gepland, wordt rekening gehouden met de "
"resterende uren van het verkooporderregel en met de rol die is gedefinieerd "
"op de service."
