# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp_event
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: whatsapp_event
#: model:ir.model,name:whatsapp_event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Tədbirin Avtomatik Göndərilməsi"

#. module: whatsapp_event
#: model:ir.model,name:whatsapp_event.model_event_registration
msgid "Event Registration"
msgstr "Tədbir qeydiyyatı"

#. module: whatsapp_event
#: model:ir.model,name:whatsapp_event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Tədbir Kateqoriyası üzrə Poçt Planlaşdırıcısı"

#. module: whatsapp_event
#: model:ir.model,name:whatsapp_event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Qeydiyyat Məktubu Planlaşdırıcısı"

#. module: whatsapp_event
#: model:ir.model.fields,field_description:whatsapp_event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:whatsapp_event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Göndərin"

#. module: whatsapp_event
#: model:ir.model.fields,field_description:whatsapp_event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:whatsapp_event.field_event_type_mail__template_ref
msgid "Template"
msgstr "Şablon"

#. module: whatsapp_event
#: model:ir.model.fields,field_description:whatsapp_event.field_event_registration__date_tz
msgid "Timezone"
msgstr "Saat qurşağı"

#. module: whatsapp_event
#: model:ir.model.fields.selection,name:whatsapp_event.selection__event_mail__notification_type__whatsapp
#: model:ir.model.fields.selection,name:whatsapp_event.selection__event_mail__template_ref__whatsapp_template
#: model:ir.model.fields.selection,name:whatsapp_event.selection__event_type_mail__notification_type__whatsapp
#: model:ir.model.fields.selection,name:whatsapp_event.selection__event_type_mail__template_ref__whatsapp_template
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: whatsapp_event
#: model:ir.model,name:whatsapp_event.model_whatsapp_template
msgid "WhatsApp Template"
msgstr ""

#. module: whatsapp_event
#. odoo-python
#: code:addons/whatsapp_event/models/event_mail.py:0
msgid "Whatsapp Templates in Events must have a phone field set."
msgstr ""
