# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_social
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_social
#. odoo-python
#: code:addons/event_social/models/event_mail.py:0
#: code:addons/event_social/models/event_type_mail.py:0
msgid ""
"As social posts have no recipients, they cannot be triggered by "
"registrations."
msgstr ""
"Sotsiaalpostitustel pole adressaate ning seetõttu ei saa need "
"registreerimist käivitada."

#. module: event_social
#: model:ir.model,name:event_social.model_event_mail
msgid "Event Automated Mailing"
msgstr "Sündmuste automatiseeritud postitamine"

#. module: event_social
#: model:ir.model,name:event_social.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Sündmuse kategooria kirjade ajastamine"

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__notification_type
msgid "Send"
msgstr "Saada"

#. module: event_social
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__template_ref__social_post_template
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__template_ref__social_post_template
msgid "Social Post"
msgstr "Sotsiaalmeedia postitus"

#. module: event_social
#: model:ir.model,name:event_social.model_social_post_template
msgid "Social Post Template"
msgstr "Sotsiaalmeedia postituse mall"

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__template_ref
msgid "Template"
msgstr "Mall"
