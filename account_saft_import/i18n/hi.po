# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_saft_import
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_saft_import
#: model:ir.model,name:account_saft_import.model_account_saft_import_wizard
msgid "Account SAF-T import wizard"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,help:account_saft_import.field_account_saft_import_wizard__attachment_id
msgid "Accounting SAF-T data file to be imported"
msgstr ""

#. module: account_saft_import
#. odoo-python
#: code:addons/account_saft_import/wizard/import_wizard.py:0
msgid "Accounting Settings"
msgstr ""

#. module: account_saft_import
#: model_terms:ir.ui.view,arch_db:account_saft_import.account_saft_import_form
msgid "Cancel"
msgstr "रद्द"

#. module: account_saft_import
#: model:ir.model,name:account_saft_import.model_res_company
msgid "Companies"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__company_id
msgid "Company"
msgstr "संस्था"

#. module: account_saft_import
#: model:ir.model.fields,help:account_saft_import.field_account_saft_import_wizard__company_id
msgid "Company used for the import"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__create_date
msgid "Created on"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__attachment_id
msgid "File"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__attachment_name
msgid "Filename"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__id
msgid "ID"
msgstr ""

#. module: account_saft_import
#: model_terms:ir.ui.view,arch_db:account_saft_import.account_saft_import_form
msgid "Import"
msgstr ""

#. module: account_saft_import
#. odoo-javascript
#: code:addons/account_saft_import/static/src/xml/account_saft_import_button.xml:0
msgid "Import SAF-T"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__import_opening_balance
msgid "Import account opening balances"
msgstr ""

#. module: account_saft_import
#: model_terms:ir.ui.view,arch_db:account_saft_import.account_saft_import_form
msgid "Import your accounting data from SAF-T file formats"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_saft_import
#: model:ir.model.fields,field_description:account_saft_import.field_account_saft_import_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_saft_import
#: model:ir.actions.act_window,name:account_saft_import.open_saft_import_wizard
#: model:ir.actions.server,name:account_saft_import.account_saft_import_action
#: model_terms:ir.ui.view,arch_db:account_saft_import.account_saft_import_form
msgid "SAF-T Import"
msgstr ""

#. module: account_saft_import
#. odoo-python
#: code:addons/account_saft_import/wizard/import_wizard.py:0
msgid "SAF-T opening balance move"
msgstr ""

#. module: account_saft_import
#: model_terms:ir.ui.view,arch_db:account_saft_import.account_saft_import_form
msgid "Select File"
msgstr ""

#. module: account_saft_import
#. odoo-python
#: code:addons/account_saft_import/wizard/import_wizard.py:0
msgid "You should install a Fiscal Localization first."
msgstr ""
