# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_helpdesk
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert"
msgstr "Converteren"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Helpdesktickets omzetten naar taken"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Projecttaken omzetten naar tickets"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/helpdesk.py:0
#: model:ir.actions.server,name:project_helpdesk.action_ticket_convert_to_task
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
msgid "Convert to Task"
msgstr "Omzetten naar taak"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
#: model:ir.actions.server,name:project_helpdesk.action_task_convert_to_ticket
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert to Ticket"
msgstr "Converteer naar ticket"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Converted Tasks"
msgstr "Omgezette taken"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Converted Tickets"
msgstr "Omgezette tickets"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Discard"
msgstr "Negeren"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__display_name
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Helpdeskticket"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__id
msgid "ID"
msgstr "ID"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__project_id
msgid "Project"
msgstr "Project"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
msgid "Recurring tasks cannot be converted into tickets."
msgstr "Herhalende taken kunnen niet in tickets omgezet worden."

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__stage_id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__stage_id
msgid "Stage"
msgstr "Fase"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task
msgid "Task"
msgstr "Taak"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Task converted into ticket %s"
msgstr "Taak omgezet in ticket %s"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__team_id
msgid "Team"
msgstr "Team"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Ticket converted into task %s"
msgstr "Ticket omgezet in taak %s"
