# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Batalkan memilih halaman ini</b> karena kami berencana untuk memproses "
"semua tagihan terlebih dahulu."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>Pilih</b> halaman ini untuk melanjutkan."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.account_move_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokume</span>"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "Pengaturan sudah ada untuk jurnal ini"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "Akuntansi"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_report
msgid "Accounting Report"
msgstr "Laporan Akuntansi"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_account_folder_id
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder_id
msgid "Accounting Workspace"
msgstr "Ruang Kerja Akuntansi"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Karena PDF ini memiliki lebih dari satu dokumen, mari pisahkan dan proses "
"secara bulk."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Lampiran"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "Pusatkan file dan dokumen akuntansi"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "Check them"
msgstr "Check them"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "Klik pada kartu untuk <b>memilih dokumen</b>."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Klik pada thumbnail untuk <b>pratinjau dokumen</b>."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klik pada <b>separator halaman</b>: kita tidak ingin memisahkan dua halaman "
"ini karena mereka berasal dari dokumen yang sama."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "Klik silang untuk <b>keluar dari pratinjau</b>."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Copy to Documents"
msgstr "Salin ke Dokumen"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note_code
msgid "Create Customer Credit Note"
msgstr "Buat Nota Kredit Pelangga"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice_code
msgid "Create Customer Invoice"
msgstr "Buat Faktur Pelanggan"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry_code
msgid "Create Misc Entry"
msgstr "Buat Entri Misc"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill_code
msgid "Create Vendor Bill"
msgstr "Buat Tagihan Vendor"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_receipt
msgid "Create Vendor Receipt"
msgstr "Buat Resi Vendor"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund_code
msgid "Create Vendor Refund"
msgstr "Buat Refund Vendor"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_document
msgid "Document"
msgstr "Dokumen"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "Pengaturan Akun Dokumen"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Export"
msgstr "Ekspor"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Ekspor format untuk laporan akuntansi"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Wizard ekspor untuk laporan akuntansi"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Laporan Keuangan"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Folder"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "Folder di mana untuk menyimpan file yang dibuat"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/wizard/account_reports_export_wizard.py:0
msgid "Generated Documents"
msgstr "Generated Documents"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__has_documents
#: model:ir.model.fields,field_description:documents_account.field_account_move__has_documents
msgid "Has Documents"
msgstr "Memiliki Dokume"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_document__has_embedded_pdf
msgid "Has Embedded PDF"
msgstr "Memiliki PDF yang Diembed"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement_code
msgid "Import Bank Statement"
msgstr "Impor Rekening Koran"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "Invoices"
msgstr "Faktur"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "Jurnal"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "Pengaturan Jurnal dan Folder"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Jurnal-jurnal"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "Jurnal untuk disinkronisasi"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Ayo proses dokumen di Inbox Anda.<br/><i>Tip: Gunakan Tag untuk memfilter "
"dokumen dan organisasikan proses Anda.</i>"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process these bills: turn them into vendor bills."
msgstr "Mari memproses tagihan berikut: jadikan mereka tagihan vendor."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "Ayo proses dokumen ini, datang dari scanner kami."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Ayo tag email ini sebagai legal<br/> <i>Tip: action dapat disesuaikan ke "
"proses Anda, menurut ruang kerja.</i>"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__suspense_statement_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__suspense_statement_line_id
msgid "Request document from a bank statement line"
msgstr "Minta dokumen dari baris laporan bank"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Kirim surat ini ke departemen legal, dengan memberikan tag yang tepat."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Label"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "Laporan Pajak"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "This invoice has been initiated by a bank transaction."
msgstr "Faktur ini telah diproses oleh transaksi bank."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Ingin menjadi <b>perusahaan yang paperless</b>? Ayo gunakan Odoo Documents."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Ruang Kerja"

#. module: documents_account
#: model_terms:web_tour.tour,rainbow_man_message:documents_account.documents_account_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Wow... 6 dokumen diproses dalam beberapa detik, Anda hebat.<br>Tur sudah "
"selesai. Coba unggah dokumen Anda sekarang."

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "You can not create account move on folder."
msgstr "Anda tidak dapat membuat pergerakkan akun pada folder."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder_id
msgid "account default folder"
msgstr "folder default akun"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "to mark this invoice as paid."
msgstr "to mark this invoice as paid."
