# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "%(project_name)s - %(task_name)s"
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Invoice your time and material</b> to your customer."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No products found. Let's create one!\n"
"                        </p><p>\n"
"                            Keep track of the products you are using to complete your tasks, and invoice your customers for the goods.\n"
"                            Tip: using kits, you can add multiple products at once.\n"
"                        </p><p>\n"
"                            When your task is marked as done, your stock will be updated automatically. Simply choose a warehouse\n"
"                            in your profile from where to draw stock.\n"
"                        </p>"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid ""
"<span invisible=\"not is_fsm\">\n"
"                    Define the service and the rate at which an employee's time is billed based on their expertise, skills or experience.\n"
"                </span>"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<span>Disc.%</span>"
msgstr "<span>Pop.%</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "<span>Products</span>"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Podzbroj</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Amount Due: </strong>"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Taxes</strong>"
msgstr "<strong>Porezi</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Untaxed amount</strong>"
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "A customer should be set on the task to generate a worksheet."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Add Products"
msgstr "Dodaj proizvode"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.industry_fsm_sale_product_catalog_inherit_search_view
msgid "Added Products"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Amount"
msgstr "Iznos"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/wizard/sale_make_invoice_advance.py:0
msgid "An invoice has been created: %s"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Naplatno"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Click on a product to add it to your <b>list of materials</b>. <i>Tip: for "
"large quantities, click on the number to edit it directly.</i>"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracijske postavke"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Confirm the creation of your <b>invoice</b>."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Kreiraj račun"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Default Service"
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/sale_order.py:0
msgid "Extra Quotation Created: %s"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
msgid "Extra Quotations"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Terenske usluge"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "U potpunosti izdan račun"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_task__under_warranty
msgid ""
"If ticked, the time and materials used for this task will not be billed to "
"the customer. However, the inventory of consumed materials will still be "
"updated."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid operator: %s"
msgstr "Neispravan operator: %s"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid value: %s"
msgstr "Neispravna vrijednost: %s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Invoice"
msgstr "Račun"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_invoice_count
msgid "Invoice Count"
msgstr "Broj računa"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_report_project_task_user_fsm__invoice_status
msgid "Invoice Status"
msgstr "Stanje računa"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Invoices"
msgstr "Računi"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's <b>track the material</b> you use for your task."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's create a new <b>product</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nova ponuda"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid "No tasks found. Let's create one!"
msgstr "Nema pronađenih zadataka. Kreiraj novi!"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Ništa za izdavanje računa"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_count
msgid "Number of invoices"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_1_product_template
msgid "Plumbing Valves"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_4_product_template
msgid "Plumbing Washers"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_quotation_count
msgid "Portal Quotation Count"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Product"
msgstr "Proizvod"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Proizvodi"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
msgid "Products on Tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Projekt"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Quantity"
msgstr "Količina"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
msgid "Quotation"
msgstr "Ponuda"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Broj ponuda"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Quotations"
msgstr "Ponude"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_2_product_template
msgid "Safety Gloves"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Račun za predujam"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Stavka prodajnog naloga"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajnog naloga"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Usluga"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Zadatak"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which this quotation have been created"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Taxes"
msgstr "Porezi"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_template.py:0
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_time
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_custom_page
msgid "Time &amp; Material"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm2
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__to_invoice
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "Za izdavanje računa"

#. module: industry_fsm_sale
#: model:ir.actions.server,name:industry_fsm_sale.project_task_to_invoice_fsm_server_action
msgid "To Invoice Server Action"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__under_warranty
msgid "Under Warranty"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Prilika za sugestivno proširenje ponude"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to navigate to your <b>list of products</b>."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr ""

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Wait for the invoice to show up"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_3_product_template
msgid "Water Testing Kits"
msgstr ""
