# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lt_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-19 11:09+0000\n"
"PO-Revision-Date: 2024-02-19 11:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_h
msgid "ACCRUALS AND DEFERRED INCOME"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1
msgid "AMOUNTS PAYABLE AFTER ONE YEAR AND OTHER LONG-TERM LIABILITIES"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g
msgid "AMOUNTS PAYABLE AND OTHER LIABILITIES"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_2
msgid "AMOUNTS RECEIVABLE WITHIN ONE YEAR"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_6
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_7
msgid "Advance payments"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_7
msgid "Advance payments and tangible assets under construction (production)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_2_3
msgid "Amounts owed by associates entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_2_2
msgid "Amounts owed by entities of the entities group"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_2
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_2
msgid "Amounts owed to credit institutions"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_7
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_7
msgid "Amounts payable to the associated entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_6
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_6
msgid "Amounts payable to the entities of the entities group"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_5
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_5
msgid "Amounts payable under the bills and checks "
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_8
msgid "Amounts receivable after one year"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_3
msgid "Amounts receivable from entities of the entities group"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_6
msgid "Amounts receivable from the associated entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_1
msgid "Assets arising from development"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_4_1
msgid "Assets of the deferred tax on profit"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_1_1
msgid "Authorized (subscribed) or primary capital"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.column,name:l10n_lt_reports.account_financial_report_balancesheet_lt_column
#: model:account.report.column,name:l10n_lt_reports.account_financial_report_profitandloss_lt_column
msgid "Balance"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report,name:l10n_lt_reports.account_financial_report_balancesheet_lt
msgid "Balance Sheet"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_4_2
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_5
msgid "Biological assets"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_6_2
msgid "Buildings"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_2
msgid "Buildings and structures"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_1
msgid "CAPITAL"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_4
msgid "CASH AND CASH EQUIVALENTS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_6
msgid "COMMON SUMMARY OF ACCOUNTS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b
msgid "CURRENT ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_4_1
msgid "Compulsory reserve or emergency (reserve) capital"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_4
msgid "Concessions, patents, licences, trade marks and similar rights"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_2_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_2
msgid "Cost of sales"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_1
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_1
msgid "Debenture loans"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d
msgid "EQUITY "
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3
msgid "FINANCIAL ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a
msgid "FIXED ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_3_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_3
msgid "Fair value adjustments of the biological assets"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_3
msgid "Finished goods"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_6
msgid "Fixed tangible assets held for sale"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_e
msgid "GRANTS, SUBSIDIES"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_4_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_4
msgid "GROSS PROFIT (LOSS)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_6_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_6
msgid "General and administrative expenses"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_4
msgid "Goods for resale"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_2
msgid "Goodwill"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1
msgid "INTANGIBLE ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_8_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_8
msgid ""
"Income from investments in the shares of parent, subsidiaries and associated"
" entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_9_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_9
msgid "Income from other long-term investments and loans"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_12_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_12
msgid "Interest and other similar expenses"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_6
msgid "Investment property"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_1
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_6_1
msgid "Land"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_8
msgid "Liabilities of tax on profit"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_9
msgid "Liabilities related to employment relations"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_5
msgid "Loans to associated entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_2
msgid "Loans to entities of the entities group"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_7
msgid "Long-term investments"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_3
msgid "Machinery and plant"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_15_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_15
msgid "NET PROFIT (LOSS)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_1_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_1
msgid "Net turnover"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_4
msgid "OTHER FIXED ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_8
msgid "Other amounts payable and long-term liabilities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_10
msgid "Other amounts payable and short-term liabilities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_4_3
msgid "Other assets"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_2_4
msgid "Other debtors"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_5
msgid "Other equipment, fittings and tools"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_9
msgid "Other financial assets"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_5
msgid "Other intangible assets"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_10_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_10
msgid "Other interest and similar income"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_3_2
msgid "Other investments"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_7_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_7
msgid "Other operating results"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_f_3
msgid "Other provisions"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_4_3
msgid "Other reserves"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_1_3
msgid "Own shares (–)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2
msgid "PAYABLES AND OTHER CURRENT LIABILITIES DUE WITHIN ONE YEAR"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_c
msgid "PREPAYMENTS AND ACCRUED INCOME"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_13_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_13
msgid "PROFIT (LOSS) BEFORE TAXATION"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_f
msgid "PROVISIONS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_3
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_3
msgid "Payments received on account"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_2
msgid "Production and work in progress"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_2_balance
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_2_balance_account_codes
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_2_balance_aggregate
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_2_bs_ppl_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_2
msgid "Profit (loss) brought forward"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_1_balance
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_1_balance_account_codes
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_1_balance_aggregate
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_1_bs_pl_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5_1
msgid "Profit (loss) for the reporting year "
msgstr ""

#. module: l10n_lt_reports
#: model:account.report,name:l10n_lt_reports.account_financial_report_profitandloss_lt
#: model:ir.actions.client,name:l10n_lt_reports.account_financial_report_l10n_lt_pl_action
msgid "Profit and Loss"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_f_1
msgid "Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_f_2
msgid "Provisions for taxation"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_4
msgid "RESERVES"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_5
msgid "RETAINED PROFIT (LOSS)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_3
msgid "REVALUATION RESERVE"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1_1
msgid "Raw materials, materials ir consumables"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_4_2
msgid "Reserve for acquiring own shares"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_2
msgid "SHARE PREMIUM ACCOUNT"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_3
msgid "SHORT-TERM INVESTMENTS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_1
msgid "STOCKS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_5_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_5
msgid "Selling expenses"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_4
msgid "Shares in associated entities"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_3_1
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_3_1
msgid "Shares in entities of the entities group"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_1_3
msgid "Software"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_d_1_2
msgid "Subscribed capital unpaid (–)"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2
msgid "TANGIBLE ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_debit
msgid "TOTAL ASSETS"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_credit
msgid "TOTAL EQUITY AND LIABILITIES"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_14_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_14
msgid "Tax on profit"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.expression,report_line_name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_11_balance
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_pnl_lt_11
msgid "The impairment of the financial assets and short-term investments"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_1_4
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_g_2_4
msgid "Trade creditors"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_b_2_1
msgid "Trade debtors"
msgstr ""

#. module: l10n_lt_reports
#: model:account.report.line,name:l10n_lt_reports.account_financial_html_report_line_bs_lt_a_2_4
msgid "Vehicles"
msgstr ""
