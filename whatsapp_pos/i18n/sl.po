# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp_pos
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Invoice Whatsapp template should have a phone field"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__invoice_template_id
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_invoice_template_id
msgid "Invoice template"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavitve POS-blagajne"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr "Naročila POS"

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Receipt Whatsapp template should have Image Header Type"
msgstr ""

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Receipt Whatsapp template should have a phone field"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__receipt_template_id
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_receipt_template_id
msgid "Receipt template"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr ""

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/pos_order.py:0
msgid "Send Whatsapp"
msgstr ""

#. module: whatsapp_pos
#: model_terms:ir.ui.view,arch_db:whatsapp_pos.res_config_settings_view_form
msgid "Send receipts Using WhatsApp"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__whatsapp_enabled
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_whatsapp_enabled
msgid "WhatsApp Enabled"
msgstr ""

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_whatsapp_template
msgid "WhatsApp Template"
msgstr ""

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/whatsapp_composer.py:0
msgid "WhatsApp messages triggered successfully!"
msgstr ""

#. module: whatsapp_pos
#: model_terms:ir.ui.view,arch_db:whatsapp_pos.view_pos_form_whatsapp_pos_inherit
msgid "Whatsapp"
msgstr ""
