# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_starshipit
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> Õigus <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid " after deleting the order on Starshipit"
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to select a service "
"for a valid configuration."
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In <b>Test Environment</b>, your shippings are automatically <b>archived</b> after the label creation. <br/>\n"
"                        Please note that charges can still occur for label creations and some shipping carriers may automatically validate the shipment when printing labels."
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping services depend on "
"enabled carriers in your Starshipit account."
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Cancel"
msgstr "Tühista"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Vedaja"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Choose Starshipit Shipping Service"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_starshipit_shipping_wizard
msgid "Choose from the available starshipit shipping methods"
msgstr "Vali saadaval olevate Starshipit saatmisviiside seast"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Confirm"
msgstr "Kinnitage"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_starshipit_shipping_wizard__available_services
msgid ""
"Contains the list of available services for the starshipit account to select"
" from."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_date
msgid "Created on"
msgstr "Loodud"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Vaikimisi pakendi tüüp"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid "Default Package Type for Starshipit"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Tarne"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Delivery Service"
msgstr "Transporditeenus"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Error: %(file_name)s file could not be obtained for order %(order_name)s."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this order."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Integration"
msgstr "Integratsioon"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Invalid Starshipit credentials."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Labels were generated for the order %s"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Options"
msgstr "Valikud"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was archived."
msgstr "Tellimus %s oli arhiveeritud."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was sent to the carrier."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid "Origin Address"
msgstr "Päritolu aadress"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid ""
"Package dimensions are required to get more accurate rates. You can define "
"these in a package type that you set as default"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Please delete the order on Starshipit then try again."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Please fill in the field %(field)s on the %(partner)s partner."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Teenusepakkuja"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_return_picking
msgid "Return Picking"
msgstr "Tagastuskorje"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return labels were generated for the order %s"
msgstr "Tagastussildid loodi tellimuse jaoks %s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was archived."
msgstr "Tagastatud tellimus%s oli arhiveeritud."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was sent to the carrier."
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Select a service linked to your starshipit account"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__selected_service_code
msgid "Selected Service"
msgstr "Valitud teenus"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_code
msgid "Service Code"
msgstr "Teenuse kood"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid "Service Name"
msgstr "Teenuse nimi"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Tarneviisid"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Shipping Product"
msgstr "Toode saatmiseks"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Something went wrong, please try again later: %s"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__delivery_carrier__delivery_type__starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__stock_package_type__package_carrier_type__starshipit
msgid "Starshipit"
msgstr "Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit API Integration key"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit API Subscription key"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit API rate exceeded. Please try again later."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit Api Key"
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Starshipit Configuration"
msgstr "Starshipit seadistus"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_parcel_reference
msgid "Starshipit Parcel Reference"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_return_parcel_reference
msgid "Starshipit Return Parcel Reference"
msgstr ""

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_sale_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_stock_starshipit
msgid "Starshipit Shipping Methods"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit Subscription Key"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Starshipit cannot generate returns for the carrier %s. Please handle this "
"return with the carrier directly."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit returned an error: %(message)s"
msgstr ""

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_package_type
msgid "Stock package type"
msgstr "Lao pakendi tüüp"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_carrier_code
msgid ""
"The carrier on starshipit used by this carrier. The service code belongs to "
"it."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The picking %(picking_name)s sequence is too long for Starshipit. Please "
"update your pickings sequence in order to use at most 50 characters."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "The return label creation failed."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The service code %(service_code)s is too long for Starshipit. Please update "
"the code inside starshipit to be at most 100 characters, then update your "
"shipping carrier %(shipping_carrier)s to the new code."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_code
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid ""
"The service that will be used for this carrier. This is set when you select "
"a carrier from the wizard."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"The shipping label creation failed with the following error:\n"
"%(error)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"There are no shipping services available, please verify the shipping address"
" or activate suitable carriers in your starshipit account."
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "There was an issue when creating the order, please try again"
msgstr "Tellimuse loomisel tekkis probleem. Palun proovige uuesti. "

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "This action requires a Starshipit carrier."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid ""
"This address will be used when fetching the available services from "
"starshipit."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_picking
msgid "Transfer"
msgstr "Siirded"
