# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_slides
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_helpdesk_slides
#. odoo-python
#: code:addons/website_helpdesk_slides/models/slide_channel.py:0
msgid "%(name)s's Helpdesk Teams"
msgstr "Équipes d'assistance de %(name)s"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.search_result
msgid ", by"
msgstr ", par"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
msgid "<i class=\"fa fa-2x fa-mortar-board\" title=\"eLearning Course\"/>"
msgstr "<i class=\"fa fa-2x fa-mortar-board\" title=\"Cours eLearning\"/>"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.search_result
msgid "<i class=\"fa fa-2x fa-mortar-board\" title=\"eLearning\"/>"
msgstr "<i class=\"fa fa-2x fa-mortar-board\" title=\"eLearning\"/>"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.view_slide_channel_form_inherit_website_helpdesk_slides
msgid "<span class=\"o_stat_text\">Helpdesk Teams</span>"
msgstr "<span class=\"o_stat_text\">Équipes d'assistance</span>"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.search_result
msgid ""
"Comments\n"
"                    | <i class=\"fa fa-folder-open-o\"/>"
msgstr ""
"Commentaires\n"
"                    | <i class=\"fa fa-folder-open-o\"/>"

#. module: website_helpdesk_slides
#: model:ir.model,name:website_helpdesk_slides.model_slide_channel
msgid "Course"
msgstr "Cours"

#. module: website_helpdesk_slides
#. odoo-python
#: code:addons/website_helpdesk_slides/models/helpdesk.py:0
#: model:ir.model.fields,field_description:website_helpdesk_slides.field_helpdesk_team__website_slide_channel_ids
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.knowledge_base_slides_card
msgid "Courses"
msgstr "Cours"

#. module: website_helpdesk_slides
#: model:ir.model.fields,help:website_helpdesk_slides.field_helpdesk_team__website_slide_channel_ids
msgid ""
"Customers will see only the content from chosen courses in the help center. "
"If you want all courses to be accessible, just leave the field empty. "
"Alternatively, you can make courses private to restrict this feature to "
"internal users."
msgstr ""
"Les clients ne verront que le contenu des cours sélectionnés dans le centre "
"d'assistance. Si vous souhaitez que tous les cours soient accessibles, "
"laissez le champ vide. Vous pouvez également rendre les cours privés afin de"
" limiter cette fonctionnalité aux utilisateurs internes."

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
msgid "Documents"
msgstr "Documents"

#. module: website_helpdesk_slides
#: model:ir.model,name:website_helpdesk_slides.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_slides.field_slide_channel__helpdesk_team_ids
msgid "Helpdesk Team"
msgstr "Équipe d'assistance"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
msgid ""
"Hours to Complete\n"
"                    |"
msgstr ""
"Heures pour compléter\n"
"                    |"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
msgid "Lessons"
msgstr "Leçons"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.knowledge_base_slides_card
msgid "Level up your skills with our training videos and tutorials"
msgstr ""
"Améliorez vos compétences grâce à nos vidéos de formation et à nos tutoriels"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.search_result
msgid ""
"Likes\n"
"                    |"
msgstr ""
"J'aime\n"
"                    |"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.channel_search_result
msgid ""
"Members\n"
"                    |"
msgstr ""
"Membres\n"
"                    |"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.knowledge_base_slides_card
msgid "Most popular"
msgstr "Les plus populaires"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.knowledge_base_slides_card
msgid "Quick Links"
msgstr "Liens rapides"

#. module: website_helpdesk_slides
#: model:ir.model,name:website_helpdesk_slides.model_slide_slide
msgid "Slides"
msgstr "Diapositives"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.knowledge_base_slides_card
msgid "Take courses"
msgstr "Suivre des cours"

#. module: website_helpdesk_slides
#: model_terms:ir.ui.view,arch_db:website_helpdesk_slides.search_result
msgid ""
"Views\n"
"                    |"
msgstr ""
"Vues\n"
"                    |"
