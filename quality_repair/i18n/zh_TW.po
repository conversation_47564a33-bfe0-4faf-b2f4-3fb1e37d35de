# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_repair
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "--&gt;"
msgstr "--&gt;"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-danger\">品質檢查</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-success\">品質檢查</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alerts</span>"
msgstr "<span class=\"o_stat_text\">品質警報</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text\">品質檢查</span>"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_ids
msgid "Alerts"
msgstr "警報"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_ids
msgid "Checks"
msgstr "檢查"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Alert"
msgstr "品質警報"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_count
msgid "Quality Alert Count"
msgstr "品質警報數目"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_check
msgid "Quality Check"
msgstr "品質檢查"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_fail
msgid "Quality Check Fail"
msgstr "品質檢查未能通過"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_todo
msgid "Quality Check Todo"
msgstr "品質檢查待辦事項"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Checks"
msgstr "品質檢查"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_point
msgid "Quality Control Point"
msgstr "品質控制點"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_repair_order
#: model:ir.model.fields,field_description:quality_repair.field_quality_alert__repair_id
#: model:ir.model.fields,field_description:quality_repair.field_quality_check__repair_id
msgid "Repair Order"
msgstr "維修單"

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with repair operation types."
msgstr "數量的品質檢查類型，不能與維修操作類型同時使用。"

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/repair.py:0
msgid "You still need to do the quality checks!"
msgstr "你仍是需要做品質檢查！"
