# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_sign
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_count
msgid "# of Signature Requests"
msgstr "# allekirjoituspyyntöjen määrä"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: sale_renting_sign
#: model:sign.template,redirect_url_text:sale_renting_sign.template_rental_sign
msgid "Close"
msgstr "Sulje"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_date
msgid "Created on"
msgstr "Luotu"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Default Document"
msgstr "Oletusasiakirja"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid "Default Document Template for Rentals"
msgstr "Vuokrauksen oletusasiakirjamalli"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__template_id
msgid "Document Template"
msgstr "Dokumenttipohja"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
msgid "Document(s) Signed"
msgstr "Allekirjoitetut dokumentit"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sale_order
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__order_id
#: model:ir.model.fields,field_description:sale_renting_sign.field_sign_send_request__sale_order_id
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Set a default document template for all rentals in the current company"
msgstr ""
"Aseta oletusasiakirjamalli kaikille nykyisen yrityksen vuokralle otetuille "
"vuokralle"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Document"
msgstr "Allekirjoita dokumentti"

#. module: sale_renting_sign
#: model:ir.actions.act_window,name:sale_renting_sign.rental_sign_documents
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Documents"
msgstr "Allekirjoita dokumentit"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_rental_sign_wizard
msgid "Sign Documents from a SO"
msgstr "Allekirjoita dokumentit myynniltä"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_send_request
msgid "Sign send request"
msgstr "Lähetä allekirjoituspyyntö"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_request
msgid "Signature Request"
msgstr "Allekirjoituspyyntö"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_ids
msgid "Signature Requests"
msgstr "Allekirjoituspyynnöt"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid ""
"This document template will be selected by default when signing documents "
"from a rental order. You should select a template accessible to all Sign "
"users."
msgstr ""
"Tämä asiakirjamalli valitaan oletusarvoisesti, kun allekirjoitat asiakirjat "
"vuokratilauksesta. Sinun tulisi valita malli, jota kaikki Sign-käyttäjät "
"voivat käyttää."

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.res_config_settings_view_form
msgid "Upload Template"
msgstr "Lataa malli"
