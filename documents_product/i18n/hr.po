# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "Centralizirani dokumenti pridruženi proizvodima"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracijske postavke"

#. module: documents_product
#: model:ir.actions.server,name:documents_product.ir_actions_server_create_product_template
msgid "Create Product Template"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "Zadane oznake"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_document
msgid "Document"
msgstr "Dokument"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "Postavke dokumentacije proizvoda"

#. module: documents_product
#. odoo-python
#: code:addons/documents_product/models/document.py:0
msgid "Invalid %s search"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
#: model:ir.model.fields,field_description:documents_product.field_documents_document__product_template_id
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "Proizvod"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "Product > DataSheets"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "Product > MSDS"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "Product > New"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Product > Plans"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Product > Specs"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tag_ids
msgid "Product Tag"
msgstr "Oznaka proizvoda"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tag_ids
msgid "Product Tags"
msgstr "Oznake proizvoda"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_documents_document__product_id
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder_id
msgid "Product Workspace"
msgstr "Radni prostor proizvoda"

#. module: documents_product
#. odoo-python
#: code:addons/documents_product/models/document.py:0
msgid "Product created from Documents"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "Radni prostor"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder_id
msgid "product default workspace"
msgstr "zadani radni prostor proizvoda"
