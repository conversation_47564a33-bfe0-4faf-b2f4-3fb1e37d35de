# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_enterprise
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "%s days"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "(Enterprise Edition)"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "1 month"
msgstr "1 μήνας"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/views/list/list_renderer_desktop.xml:0
msgid "Add Custom Field"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/views/kanban/kanban_header_patch.js:0
msgid "Automations"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Build new apps from scratch"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Build new reports"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/share_url/burger_menu.xml:0
msgid "Close menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"Contact your sales representative to help you to unlink your previous "
"database"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Create automation rules"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Customize Reports"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Customize any screen"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/color_scheme/color_scheme_menu_items.js:0
msgid "Dark Mode"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "Database expiration:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Define webhooks"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Discard"
msgstr "Απόρριψη"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Dismiss"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Error reason:"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu_service.js:0
msgid "Home"
msgstr "Αρχική"

#. module: web_enterprise
#: model:ir.model.fields,field_description:web_enterprise.field_res_users_settings__homemenu_config
msgid "Home Menu Configuration"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
msgid "Home menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "I paid, please recheck!"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Install Odoo Studio and its dependencies"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Learn More"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Log in as an administrator to correct the issue."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "No result"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "Odoo"
msgstr "Odoo"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "Odoo Enterprise Edition License V1.0"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/views/list/list_renderer_desktop.js:0
msgid "Odoo Studio - Add new fields to any view"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/views/kanban/kanban_header_patch.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Odoo Support"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Paste code here"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
msgid "Previous view"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "Register"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Register your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Renew your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "Retry"
msgstr "Επανάληψη"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Send an email"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Sending the instructions by email ..."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/share_url/share_url.js:0
msgid "Share"
msgstr "Κοινοποίηση"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/share_url/burger_menu.xml:0
msgid "Share URL"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"Something went wrong while registering your database. You can try again or "
"contact"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Start using Odoo Studio"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Subscription Code:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "TIP"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"Thank you, your registration was successful! Your database is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/enterprise_subscription_service.js:0
msgid ""
"Thank you, your registration was successful! Your database is valid until "
"%s."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"The instructions to unlink your subscription from the previous database(s) "
"have been sent"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This database has expired. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This database will expire in %s. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This demo database will expire in %s. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Unable to send the instructions by email, please contact the"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Unleash the power of Odoo Studio:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Upgrade your subscription"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Want to tailor-make your Odoo?"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"You have more users or more apps installed than your subscription allows."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ""
"You will be able to register your database once you have installed your "
"first app."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription code"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription is already linked to a database."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription was updated and is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "and more!"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "buy a subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "buy a subscription."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "or"
msgstr "ή"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "to the subscription owner to confirm the change, enter a new code or"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "— open me anywhere with"
msgstr ""
