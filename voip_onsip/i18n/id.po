# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip_onsip
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_users_settings__onsip_auth_username
msgid "OnSIP Auth Username"
msgstr "Username Auth OnSIP"

#. module: voip_onsip
#: model_terms:ir.ui.view,arch_db:voip_onsip.voip_provider_tree_view_inherit
msgid "OnSIP Domain"
msgstr "Domain OnSIP"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_users__onsip_auth_username
msgid "Onsip Auth Username"
msgstr "Onsip Auth Username"

#. module: voip_onsip
#: model:ir.model.fields,help:voip_onsip.field_voip_provider__ws_server
msgid "The URL of your WebSocket"
msgstr "URL dari WebSocket Anda"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users
msgid "User"
msgstr "Pengguna"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users_settings
msgid "User Settings"
msgstr "Pengaturan User"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_voip_provider
msgid "VoIP Provider"
msgstr "VoIP Provider"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_voip_provider__ws_server
msgid "WebSocket"
msgstr "WebSocket"
