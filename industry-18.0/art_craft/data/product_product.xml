<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_10" model="product.product">
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="default_code">SHI GDH</field>
        <field name="standard_price">12.0</field>
        <field name="barcode">23451</field>
    </record>
    <record id="product_product_11" model="product.product">
        <field name="product_tmpl_id" ref="product_template_10"/>
        <field name="default_code">PK TOTE</field>
        <field name="standard_price">3.68</field>
        <field name="barcode">34562</field>
    </record>
    <record id="product_product_12" model="product.product">
        <field name="product_tmpl_id" ref="product_template_11"/>
        <field name="default_code">DP TOTE</field>
        <field name="standard_price">10.0</field>
        <field name="barcode">45673</field>
    </record>
    <record id="product_product_13" model="product.product">
        <field name="product_tmpl_id" ref="product_template_12"/>
        <field name="default_code">PK CB/S</field>
        <field name="standard_price">15.0</field>
        <field name="barcode">09876</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_3')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_3')])]"/>
    </record>
    <record id="product_product_14" model="product.product">
        <field name="product_tmpl_id" ref="product_template_12"/>
        <field name="default_code">PK CB/L</field>
        <field name="standard_price">20.0</field>
        <field name="barcode">78964</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_4')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_4')])]"/>
    </record>
    <record id="product_product_16" model="product.product">
        <field name="product_tmpl_id" ref="product_template_13"/>
        <field name="default_code">WP T12</field>
        <field name="standard_price">15.0</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_5')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_5')])]"/>
    </record>
    <record id="product_product_17" model="product.product">
        <field name="product_tmpl_id" ref="product_template_13"/>
        <field name="standard_price">16.0</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_6')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_6')])]"/>
        <field name="image_variant_1920" type="base64" file="art_craft/static/src/binary/product_product/17-image_variant_1920"/>
    </record>
    <record id="product_product_22" model="product.product">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="standard_price">5.0</field>
    </record>
    <record id="product_product_23" model="product.product">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="standard_price">5.0</field>
    </record>
    <record id="product_product_24" model="product.product">
        <field name="product_tmpl_id" ref="product_template_18"/>
        <field name="default_code">ART- Khubiram Gopilal</field>
        <field name="standard_price">100.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_9')])]"/>
    </record>
    <record id="product_product_26" model="product.product">
        <field name="product_tmpl_id" ref="product_template_19"/>
        <field name="default_code">ART-Jamini Roy</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_10')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_10')])]"/>
    </record>
    <record id="product_product_27" model="product.product">
        <field name="product_tmpl_id" ref="product_template_19"/>
        <field name="default_code">ART-Jamini R.</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_11')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_11')])]"/>
    </record>
    <record id="product_product_28" model="product.product">
        <field name="product_tmpl_id" ref="product_template_20"/>
        <field name="default_code">ART-Anand Gadapa</field>
        <field name="standard_price">170.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_12')])]"/>
    </record>
    <record id="product_product_29" model="product.product">
        <field name="product_tmpl_id" ref="product_template_21"/>
        <field name="standard_price">110.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_13'), ref('product_template_attribute_value_14')])]"/>
    </record>
    <record id="product_product_30" model="product.product">
        <field name="product_tmpl_id" ref="product_template_22"/>
        <field name="standard_price">25.0</field>
    </record>
    <record id="product_product_31" model="product.product">
        <field name="product_tmpl_id" ref="product_template_23"/>
        <field name="standard_price">9.99</field>
    </record>
    <record id="product_product_32" model="product.product">
        <field name="product_tmpl_id" ref="product_template_24"/>
        <field name="standard_price">15.0</field>
    </record>
    <record id="product_product_33" model="product.product">
        <field name="product_tmpl_id" ref="product_template_25"/>
    </record>
    <record id="product_product_34" model="product.product">
        <field name="product_tmpl_id" ref="product_template_26"/>
    </record>
    <record id="product_product_6" model="product.product">
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_product_8" model="product.product">
        <field name="product_tmpl_id" ref="product_template_8"/>
        <field name="default_code">WPL NANDI/s</field>
        <field name="standard_price">50.80</field>
        <field name="barcode">12345</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_1')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_1')])]"/>
    </record>
    <record id="product_product_9" model="product.product">
        <field name="product_tmpl_id" ref="product_template_8"/>
        <field name="default_code">WPL NANDI/L</field>
        <field name="standard_price">60.47</field>
        <field name="barcode">67890</field>
        <field name="product_template_variant_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_2')])]"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_2')])]"/>
    </record>
</odoo>
