# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bar_industry
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>l<PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:06+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ", and feel free to"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "1. Point of sale"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "2. Replenishment"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this one, you can simulate an Happy Hour. To do so, click the \"More\" button in the order summary, and choose the pricelist \"Happy Hour\" instead of the Default one.\n"
"                You will see that if you take twice the same drink, it'll cost half the price."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Go back to your "
"backend by clicking the \"Backend\" menu in the right corner, then switch to"
" the Kitchen Display App."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "3. Plan your services"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"4. Open your preparation screen, you can see that your orders are there. You"
" can now mark as done either line by line, or the entire order by clicking "
"on the top."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🔁</i>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics:</strong></span>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Use case:</strong></span>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "<strong><span class=\"display-4-fs\">Bar</span></strong><br/>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"A free, personalized, integrated <strong><font class=\"text-o-color-1\">Website</font></strong> in a few clicks. Start receiving orders directly in Odoo or go live with your \n"
"                        <strong><font class=\"text-o-color-1\">E-shop</font></strong> in a few minutes."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""

#. module: bar_industry
#: model:planning.role,name:bar_industry.planning_role_2
msgid "Bar"
msgstr "Juosta"

#. module: bar_industry
#: model:planning.role,name:bar_industry.planning_role_3
msgid "Bar & Service"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Basics:"
msgstr ""

#. module: bar_industry
#: model:product.template,name:bar_industry.product_product_26_product_template
msgid "Blond Beer"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products."
msgstr ""

#. module: bar_industry
#: model:project.project,name:bar_industry.project_project_2
msgid "Cleaning"
msgstr ""

#. module: bar_industry
#: model:product.pricelist,name:bar_industry.product_pricelist_1
msgid "Default"
msgstr "Numatytasis"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Do you want to go further?"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""

#. module: bar_industry
#: model:project.task.type,name:bar_industry.project_task_type_2
msgid "Done"
msgstr "Atlikta"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""

#. module: bar_industry
#: model:product.pricelist,name:bar_industry.product_pricelist_2
msgid "Happy Hour"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Hello there!"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case (In the next Knowledge "
"Article)."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">Email Marketing</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, and <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""

#. module: bar_industry
#: model:planning.role,name:bar_industry.planning_role_1
msgid "Service"
msgstr "Paslauga"

#. module: bar_industry
#: model:project.project,name:bar_industry.project_project_1
msgid "Suppliers Orders"
msgstr ""

#. module: bar_industry
#: model:project.project,label_tasks:bar_industry.project_project_1
#: model:project.project,label_tasks:bar_industry.project_project_2
msgid "Tasks"
msgstr "Užduotys"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Kitchen Display</font></strong> "
"will ensure a great follow up of every order in your bar and kitchen."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""

#. module: bar_industry
#: model:project.task.type,name:bar_industry.project_task_type_1
msgid "To Do"
msgstr "Reikia atlikti"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Use case:"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Inventory App</font></strong>"
" to manage your stock and receive products."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Planning App</font></strong> "
"to schedule your shifts and share it with your employees."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Point of Sale</font></strong>"
" for your sales at the desk. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Project App</font></strong> "
"to never miss a reordering or a cleaning task."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Purchase App</font></strong> "
"to reorder your products."
msgstr ""

#. module: bar_industry
#: model_terms:web_tour.tour,rainbow_man_message:bar_industry.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection of your Point of Sale."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory  &amp;gt; Products  &amp;gt; Blond Beer"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps  &amp;gt; "
"Industries  &amp;gt; Upgrade your Bar  &amp; Pub package and check the "
"related box"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You just installed the Odoo for Bar package. By doing so, we have installed "
"a bunch of necessary apps to run your bar efficiently."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "academy"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "and"
msgstr "ir"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "documentation"
msgstr "dokumentacija"

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "if you need help!<br/>"
msgstr ""

#. module: bar_industry
#: model_terms:ir.ui.view,arch_db:bar_industry.welcome_article_body
msgid "request a demo"
msgstr ""
