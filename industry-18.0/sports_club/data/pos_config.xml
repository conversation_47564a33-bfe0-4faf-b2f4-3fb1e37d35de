<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record model="pos.config" id="pos_config_shop">
        <field name="name">Sports Shop</field>
        <field name="iface_available_categ_ids" eval="[Command.link(ref('pos_category_6')), Command.link(ref('pos_category_7')), Command.link(ref('pos_category_8')), Command.link(ref('pos_category_4'))]"/>
        <field name="module_pos_restaurant" eval="False"/>
    </record>
    <record model="pos.config" id="pos_config_shop">
        <field name="payment_method_ids" eval="[
            Command.link(ref('pos_payment_method_1')),
            Command.link(ref('pos_payment_method_2')),
        ]"/>
    </record>
    <record model="pos.config" id="pos_config_bar">
        <field name="name">Bar</field>
        <field name="limit_categories" eval="1"/>
        <field name="iface_available_categ_ids" eval="[Command.link(ref('pos_category_2')), Command.link(ref('pos_category_1'))]"/>
        <field name="floor_ids" eval="[(6, 0, [ref('restaurant_floor_1')])]"/>
    </record>
</odoo>
