<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        <p>
            <span style="font-size: 36px;"><strong>Agricultural Retail</strong></span>
        </p>
        <p>
            <font color="#343541">
                Tailored setup for agricultural retail businesses engaged in B2B and B2C sales of farming products. Point of Sale (POS) stores &amp; website offer categorised items such as seeds, pesticides, plant nutrition, and farming equipment.
            </font>
        </p>
        <p>
            <span style="color: rgb(52, 53, 65); font-style: normal; font-weight: 400;">
                They utilize a suite of modules including Point of Sale, Inventory, Sales, Purchase, Survey, Accounting, Contact and Dashboard. Expanding into the online realm, a website and E-commerce applications can significantly aid their
                growth in the agricultural retail sector.
            </span>
        </p>
        <div data-oe-protected="true" class="o_knowledge_behavior_anchor o_knowledge_behavior_type_toc">
            <div class="o_knowledge_toc_content">
                <a href="#" data-oe-nodeid="1" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">​Business Flows </a>
                <a href="#" data-oe-nodeid="2" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 1: Purchase</a>
                <a href="#" data-oe-nodeid="3" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 2: Sales from Website (B2B / B2C) </a>
                <a href="#" data-oe-nodeid="4" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 3: Sales from POS </a>
                <a href="#" data-oe-nodeid="5" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 4: Opportunity from Website (B2B) and Survey fill</a>
            </div>
        </div>
        <h1>​Business Flows<br /></h1>
        <h2 style="margin-bottom: 0px;"><font class="text-o-color-5">Flow 1: Purchase</font></h2>
        <br />
        <ul style="margin-bottom: 0px;">
            <li>
                Go to Purchase > Orders > Requests for Quotation
            </li>
            <li>
                Create RFQ for product "<font style="color: rgb(57, 132, 198);"><strong>Onion Seeds</strong></font>" for vendor "<span style="color: rgb(57, 132, 198); font-size: 0.9625rem;"><strong>Gloster Organics</strong></span>"
            </li>
            <li>
                Create other RFQ for same product with vendor "<font style="color: rgb(57, 132, 198);"><strong>Astron Agro</strong></font>"
            </li>
            <li>Compare RFQs</li>
            <li>Confirm RFQ based on best price</li>
            <li><font style="color: var(--color) !important;">Validate the reception for all quantities at once. </font></li>
        </ul>
        <h2 style="margin-bottom: 0px;">
            <font class="text-black"><strong>Flow 2: Sales from Website (B2B / B2C) </strong></font>
        </h2>
        <br />
        <p>If the user is selling their Products from the website.</p>
        <p>To do that,</p>
        <ul>
            <li>Website > configure pricelist</li>
            <li>Go to Shop Section and Choose Products</li>
            <li>Process Check Out</li>
            <li>
                Add Promocode "<strong><font style="color: rgb(57, 132, 198);">AGROFARMA10</font></strong>"
            </li>
            <li>Make Payment<font class="text-o-color-5">​</font></li>
        </ul>
        <h2 style="margin-bottom: 0px;">Flow 3: Sales from POS</h2>
        <br />
        <p style="margin-bottom: 0px;">For Small Products, customers place orders and make payments at the shop.</p>
        <br />
        <ul style="margin-bottom: 0px;">
            <li>Create a POS order with required product</li>
            <li>
                Add a Promocode "<font style="color: rgb(57, 132, 198);"><strong>AGROFARMA10</strong></font>"
            </li>
            <li>Receive Payment</li>
        </ul>
        <h2 style="margin-bottom: 0px;"><font class="text-black">Flow 4: Opportunity from Website (B2B) and Survey fill</font></h2>
        <br />
        <p style="margin-bottom: 0px;">Receive the opportunities through a website. <br /></p>
        <br />
        <ul style="margin-bottom: 0px;">
            <li>
                ​Prospect will go to website and fill the "<font style="color: rgb(57, 132, 198);"><strong>Contact us</strong></font>" form.
            </li>
            <li>Once form is submitted, opportunity will be created in the system.</li>
            <li>Sales person visit the farmer and fill survey(According to Farmer's Input).</li>
            <li>Sales person shares quotation and confirm the order.</li>
            <li>Make Invoice.</li>
        </ul>
    </template>    

    <record id="welcome_article" model="knowledge.article">
        <field name="name">Agriculture Store</field>
        <field name="internal_permission">write</field>
        <field name="icon">🍀</field>
        <field name="cover_image_id" ref="knowledge_cover_6"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="is_locked" eval="True"/>
        <field name="body">
            <![CDATA[]]>
        </field>
    </record>
</odoo>
