# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* toy_store
# 
# Translators:
# Гэрэлтцог Цогтбаатар, 2024
# hish, 2024
# Batmunk<PERSON> Ganbat <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:13+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.homepage
msgid "&amp;nbsp;Gepetto's is your friendly neighboorhoud&amp;nbsp;toy store."
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.welcome_article_body
msgid ""
"&lt;h1&gt;Toy Store&lt;/h1&gt;\n"
"                &lt;p&gt;Hi!&lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                This guide is designed to help toy store owners and managers&amp;nbsp;manage their businesses using the Odoo Toy Store module. From setting up product catalogs and loyalty programs to optimizing stock replenishment, you'll find clear\n"
"                instructions and best practices to help you succeed.\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;Are you new to Odoo? Explore Odoo's&amp;nbsp;eLearning platform to get you started with video tutorials and exercises, and check out the documentation to become an Odoo master!&lt;/p&gt;\n"
"                &lt;p&gt;&lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/slides\"&gt;🎓 Odoo Learn&amp;nbsp;(tutorials in English)&lt;/a&gt; &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/documentation/\"&gt;📖 Odoo docs&lt;/a&gt;&lt;/p&gt;\n"
"                &lt;div class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\" data-oe-protected=\"true\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div class=\"w-100 px-3\" data-oe-protected=\"false\"&gt;\n"
"                        &lt;p&gt;\n"
"                        &lt;span class=\"h4-fs\"&gt;&lt;strong&gt;Demo: try it yourself!&lt;/strong&gt;&lt;/span&gt;\n"
"                        &lt;/p&gt;\n"
"                        &lt;p&gt;Follow the steps described in these&amp;nbsp;banners to discover the features.&lt;/p&gt;\n"
"                        &lt;p&gt;\n"
"                        &lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;\n"
"                                ⚠️&lt;span style=\"width: 11px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​You will need the&amp;nbsp;\n"
"                        &lt;/span&gt;\n"
"                        &lt;strong style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size);\"&gt;demo data&lt;/strong&gt;\n"
"                        &lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size);\"&gt;&amp;nbsp;to do these exercises&lt;/span&gt;. Demo data includes prepopulated products, contacts, and settings for a hands-on\n"
"                        experience.&amp;nbsp;&lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;If you didn't load&amp;nbsp;them when installing this&amp;nbsp;&lt;/span&gt;\n"
"                        &lt;em style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;Toy Store&lt;/em&gt;\n"
"                        &lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;&amp;nbsp;module, you can still do it by going to&amp;nbsp;&lt;/span&gt;\n"
"                        &lt;em style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;Apps → Industries&lt;/em&gt;\n"
"                        &lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;&amp;nbsp;and upgrading the &lt;em&gt;Toy Store&lt;/em&gt; module with &lt;/span&gt;\n"
"                        &lt;em style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;Load demo data&amp;nbsp;&lt;/em&gt;\n"
"                        &lt;span style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;checked.&lt;/span&gt;\n"
"                        &lt;/p&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h2&gt;Table of content&lt;/h2&gt;\n"
"                &lt;div data-oe-protected=\"true\" class=\"o_knowledge_behavior_anchor o_knowledge_behavior_type_toc\" tabindex=\"-1\"&gt;\n"
"                &lt;div class=\"o_knowledge_toc_content\"&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"0\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0\"&gt;Toy Store&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"1\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1\"&gt;Table of content&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"2\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1\"&gt;Odoo apps&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"3\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt; ​Point of Sale&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"4\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_3\"&gt;Demo:&amp;nbsp; complete a cash sale and issue an invoice to your customer&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"5\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt; ​Website and eCommerce&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"6\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_3\"&gt;Demo: order online with the Demo payment method&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"7\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt; ​Invoicing&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"8\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt; ​Purchase&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"9\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt; ​Inventory&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"10\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;To go further&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"11\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1\"&gt;Business flows&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"12\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;🎁 ​Loyalty program&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"13\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_3\"&gt;Demo: Sell in-store and give your customer a free product&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"14\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;🔎 ​Add characteristics&amp;nbsp;on your products' web pages&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"15\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;🔄 ​Automatic replenishment&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"16\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_3\"&gt;Demo: automize the replenishment of the game \"Pandemic\"&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"17\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;🚚 ​Receive products from a purchase order&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"18\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_3\"&gt;Demo:&amp;nbsp;receive products and update the stock levels&lt;/a&gt;\n"
"                        &lt;a href=\"#\" data-oe-nodeid=\"19\" class=\"o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2\"&gt;🧮 ​Seasonal physical&amp;nbsp;inventory&amp;nbsp;count&lt;/a&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h2&gt;Odoo apps&lt;/h2&gt;\n"
"                &lt;p&gt;The following apps were installed on your Odoo database and pre-configured as part of the Toy Store module. Take some time to discover them to take advantage of all the features that could be helpful for your store.&lt;/p&gt;\n"
"                &lt;div data-oe-protected=\"true\" class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-info pb-0 pt-3\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;💡&lt;/i&gt;\n"
"                &lt;div data-oe-protected=\"false\" class=\"w-100 px-3\"&gt;\n"
"                        &lt;p&gt;Odoo apps share all the data in real time, so&amp;nbsp;you won't have to encode customers' details or products multiple times.&lt;/p&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1056\" /&gt;&lt;span style=\"width: 40px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Point of Sale&lt;/h3&gt;\n"
"                &lt;p&gt;Manage customer purchases, returns, and exchanges. Access real-time sales data and inventory updates. Enhance customer satisfaction through loyalty programs and personalized offers.&lt;/p&gt;\n"
"                &lt;div data-oe-protected=\"true\" class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-info pb-0 pt-3\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;💡&lt;/i&gt;\n"
"                &lt;div data-oe-protected=\"false\" class=\"w-100 px-3\"&gt;\n"
"                        &lt;p&gt;Point of Sale works on any device with a web browser, even if you are temporarily offline.&lt;/p&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/slides/point-of-sale-28\"&gt;🎓 Tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/documentation/latest/applications/sales/point_of_sale.html\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;div class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\" data-oe-protected=\"true\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div class=\"w-100 px-3\" data-oe-protected=\"false\"&gt;\n"
"                        &lt;h4&gt;Demo:&amp;nbsp; complete a cash sale and issue an invoice to your customer&lt;/h4&gt;\n"
"                        &lt;ul&gt;\n"
"                        &lt;li&gt;In Point of Sale, open the &lt;em&gt;Shop&lt;/em&gt; register.&lt;/li&gt;\n"
"                        &lt;li&gt;Select a customer or create a new one, and add products.&lt;/li&gt;\n"
"                        &lt;li&gt;Click on &lt;em&gt;Payment&lt;/em&gt;, then select &lt;em&gt;Cash&lt;/em&gt;&amp;nbsp;as payment method and&amp;nbsp;&lt;em&gt;Invoice&lt;/em&gt;&amp;nbsp;next to the total amount.&lt;/li&gt;\n"
"                        &lt;li&gt;Finalize the checkout, then open the PDF file with the invoice.&lt;/li&gt;\n"
"                        &lt;/ul&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1058\" /&gt;&lt;span style=\"width: 40px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Website and eCommerce&lt;/h3&gt;\n"
"                &lt;p&gt;Create a professional website, design beautiful product pages, and process online orders seamlessly. Drive sales through marketing and SEO optimization.&lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a class=\"btn btn-secondary\" href=\"https://www.odoo.com/slides/website-25\"&gt;🎓 Website tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a class=\"btn btn-secondary\" href=\"https://www.odoo.com/documentation/latest/applications/websites/website.html\"&gt;📖 Website documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/ecommerce-26\" class=\"btn btn-secondary\"&gt;🎓 eCommerce tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;strong&gt;&lt;a href=\"https://www.odoo.com/documentation/latest/applications/websites/ecommerce.html\" class=\"btn btn-secondary\"&gt;📖 eCommerce documentation&lt;/a&gt;&lt;/strong&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;div data-oe-protected=\"true\" class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div data-oe-protected=\"false\" class=\"w-100 px-3\"&gt;\n"
"                        &lt;h4&gt;Demo: order online with the &lt;em&gt;Demo&lt;/em&gt; payment method&lt;/h4&gt;\n"
"                        &lt;p&gt;The &lt;em&gt;Demo&lt;/em&gt;&amp;nbsp;payment method&amp;nbsp;is for testing purposes. It is safe to play with, and you can choose whether&amp;nbsp;the payment is successful or not.&lt;/p&gt;\n"
"                        &lt;ul&gt;\n"
"                        &lt;li&gt;Go to Website and add products to your cart.&lt;/li&gt;\n"
"                        &lt;li&gt;Go to checkout and use &lt;em&gt;Demo&lt;/em&gt;&amp;nbsp;as the payment method, with the &lt;em&gt;Payment Status&lt;/em&gt;&amp;nbsp;set as &lt;em&gt;Successful&lt;/em&gt;.​&lt;br /&gt;&lt;/li&gt;\n"
"                        &lt;/ul&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1062\" /&gt;&lt;span style=\"width: 40px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Invoicing&lt;/h3&gt;\n"
"                &lt;p&gt;Issue professional-looking invoices in no time,&amp;nbsp;configure the fiscal settings such as the tax rates according to your country's needs, and set up online payment methods.&lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/accounting-and-invoicing-19\" class=\"btn btn-secondary mb-2\"&gt;🎓 Tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/finance/accounting.html\" class=\"btn btn-secondary mb-2\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1059\" /&gt;&lt;span style=\"width: 40px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Purchase&lt;/h3&gt;\n"
"                &lt;p&gt;Create and manage purchase orders, track vendor performance, and ensure timely deliveries. Optimize purchasing decisions based on product demand and supplier performance.&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/slides/purchase-23\"&gt;🎓 Tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/purchase.html\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1060\" /&gt;&lt;span style=\"width: 40px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Inventory&lt;/h3&gt;\n"
"                &lt;p&gt;Accurately track product quantities and locations. Prepare products for shipment, including packing and labeling. Use automated reordering rules to prevent stockouts and overstocking.&lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/slides/inventory-24\"&gt;🎓 Tutorials&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a class=\"btn btn-secondary mb-2\" href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/inventory.html\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;To go further&lt;/h3&gt;\n"
"                &lt;p&gt;Here are other Odoo apps you might be interested in installing on your database:&lt;/p&gt;\n"
"                &lt;ul&gt;\n"
"                &lt;li&gt;Sales&lt;/li&gt;\n"
"                &lt;li&gt;Email Marketing&lt;/li&gt;\n"
"                &lt;li&gt;Accounting&lt;/li&gt;\n"
"                &lt;/ul&gt;\n"
"                &lt;p&gt;&lt;a href=\"https://www.odoo.com/documentation/latest/applications/general/apps_modules.html\" class=\"btn btn-secondary\"&gt;📖 Apps installation documentation&lt;/a&gt;&lt;/p&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h2&gt;Business flows&lt;/h2&gt;\n"
"                &lt;h3&gt;🎁&lt;span style=\"width: 17px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Loyalty program&lt;/h3&gt;\n"
"                &lt;p&gt;\n"
"                A great way to get your customers to come back to your store or your eCommerce is to&amp;nbsp;reward them based on their previous purchases. Use the&amp;nbsp;\n"
"                &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;Discount &amp;amp; Loyalty&lt;/font&gt;&lt;/strong&gt; feature&amp;nbsp;to set up&amp;nbsp;loyalty programs such as free products, discounts, or free shipping.\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/slide/4524?fullscreen=1\" class=\"btn btn-secondary mb-2\"&gt;🎓 Tutorial&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/sales/sales/products_prices/loyalty_discount.html\" class=\"btn btn-secondary mb-2\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;div class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\" data-oe-protected=\"true\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div class=\"w-100 px-3\" data-oe-protected=\"false\"&gt;\n"
"                        &lt;h4&gt;Demo: Sell in-store and give your customer a free product&lt;/h4&gt;\n"
"                        &lt;p&gt;The &lt;em&gt;loyalty card&lt;/em&gt; program grants &lt;em&gt;loyalty points&lt;/em&gt; based on the purchase.&amp;nbsp;You can then exchange these points for a reward.&lt;/p&gt;\n"
"                        &lt;ul&gt;\n"
"                        &lt;li&gt;In Point of Sale, open the &lt;em&gt;Shop&lt;/em&gt; register.&lt;/li&gt;\n"
"                        &lt;li&gt;Select a customer and add products for a minimum of $50.&lt;/li&gt;\n"
"                        &lt;li&gt;Click on the &lt;em&gt;Reward&lt;/em&gt; button, then select &lt;em&gt;Free Product - 12 coloring pencils&lt;/em&gt;.&lt;/li&gt;\n"
"                        &lt;li&gt;Click on &lt;em&gt;Payment&lt;/em&gt;&amp;nbsp;and finalize the checkout.&lt;/li&gt;\n"
"                        &lt;/ul&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;🔎&lt;span style=\"width: 17px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Add characteristics&amp;nbsp;on your products' web pages&lt;/h3&gt;\n"
"                &lt;p&gt;\n"
"                By default, &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;attributes&lt;/font&gt;&lt;/strong&gt; are used to create &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;product variants&lt;/font&gt;&lt;/strong&gt;&amp;nbsp;based on characteristics such as&amp;nbsp;color\n"
"                (see the product &lt;em&gt;Micro scooter&lt;/em&gt; as an example).\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/slide/2723?fullscreen=1\" class=\"btn btn-secondary\"&gt;🎓 Tutorial&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/websites/ecommerce/products/variants.html\" class=\"btn btn-secondary\"&gt;📖 Documentation&lt;/a&gt;&lt;br /&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                Informative characteristics, such as the brand, the minimum age required, and number of players, are also defined as &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;attributes&lt;/font&gt;&lt;/strong&gt;&amp;nbsp;on the product form. However,\n"
"                each&amp;nbsp;attribute must be correctly set up before adding values to avoid creating new product variants. To do so, set the attribute's&amp;nbsp;&lt;em&gt;Variants Creation Mode&lt;/em&gt; field to&amp;nbsp;&lt;em&gt;Never (option)&lt;/em&gt;.\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;Characteristics&amp;nbsp;with multiple values, such as the number of players, should be added multiple times: one attribute line per value. This ensures a proper display on the products' pages.&lt;/p&gt;\n"
"                &lt;p&gt;&lt;img class=\"img img-fluid o_we_custom_image\" src=\"/web/image/toy_store.ir_attachment_1049\" /&gt;&lt;/p&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;🔄&lt;span style=\"width: 17px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Automatic replenishment&lt;/h3&gt;\n"
"                &lt;p&gt;\n"
"                With thousands of products to manage,&amp;nbsp;automizing product replenishment saves you time and prevents&amp;nbsp;stock shortages. Set &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;reordering rules&lt;/font&gt;&lt;/strong&gt;&amp;nbsp;to automatically\n"
"                generate purchase orders when product quantities fall below specified levels. Use the &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;replenishment&lt;/font&gt;&lt;/strong&gt; tool to review and validate these purchase orders before confirming\n"
"                them.\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/slide/5378?fullscreen=1\" class=\"btn btn-secondary mb-2\"&gt;🎓 Tutorial&lt;/a&gt;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/inventory/product_management/product_replenishment/reordering_rules.html\" class=\"btn btn-secondary mb-2\"&gt;📖 Documentation&lt;/a&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;div class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\" data-oe-protected=\"true\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div class=\"w-100 px-3\" data-oe-protected=\"false\"&gt;\n"
"                        &lt;h4&gt;Demo: automize the replenishment of the game \"Pandemic\"&lt;/h4&gt;\n"
"                        &lt;p&gt;\n"
"                        &lt;span style=\"margin: 0px; padding: 0px;\"&gt;\n"
"                                Adjust the&amp;nbsp;&lt;em&gt;Reordering Rule&lt;/em&gt;&amp;nbsp;for the&amp;nbsp;&lt;em&gt;Pandemic game&lt;/em&gt;&amp;nbsp;so that when the stock quantity drops below 5, Odoo automatically creates&amp;nbsp;a purchase order to replenish the stock to 15 games.\n"
"                        &lt;/span&gt;\n"
"                        &lt;br /&gt;\n"
"                        &lt;/p&gt;\n"
"                        &lt;ul&gt;\n"
"                        &lt;li&gt;\n"
"                                Go to&amp;nbsp;\n"
"                                &lt;em&gt;\n"
"                                Inventory&amp;nbsp;\n"
"                                &lt;em style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;\n"
"                                        → Operations &lt;em style=\"text-align: inherit; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight);\"&gt;→ Replenishment&lt;/em&gt;\n"
"                                &lt;/em&gt;\n"
"                                &lt;/em&gt;\n"
"                                .\n"
"                        &lt;/li&gt;\n"
"                        &lt;li&gt;Review the reordering rule for &lt;em&gt;Pandemic&lt;/em&gt;&amp;nbsp;and change the &lt;em&gt;Max&lt;/em&gt;&amp;nbsp;quantity to 15.&lt;/li&gt;\n"
"                        &lt;li&gt;Click on&amp;nbsp;&lt;span class=\"fa fa-refresh\"&gt;&lt;/span&gt;&lt;em&gt;&amp;nbsp;Automate&lt;/em&gt;.&lt;/li&gt;\n"
"                        &lt;li&gt;Review the new draft Purchase Order.&lt;/li&gt;\n"
"                        &lt;/ul&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;🚚&lt;span style=\"width: 17px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Receive products from a purchase order&lt;/h3&gt;\n"
"                &lt;p&gt;\n"
"                &lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;Purchase orders&lt;/font&gt;&lt;/strong&gt; simplify&amp;nbsp;\n"
"                &lt;span style=\"margin: 0px; padding: 0px;\"&gt;\n"
"                        communication with vendors and tracking of ordered products.&amp;nbsp;&lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;Inventory operations&lt;/font&gt;&lt;/strong&gt; confirm the reception or delivery of products and update&amp;nbsp;\n"
"                &lt;/span&gt;\n"
"                stock levels.\n"
"                &lt;/p&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/slide/5316?fullscreen=1\" class=\"btn btn-secondary\"&gt;🎓 Tutorial&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/inventory/shipping_receiving/daily_operations/shipments_deliveries.html\" class=\"btn btn-secondary\"&gt;📖 Documentation&lt;/a&gt;&lt;br /&gt;\n"
"                &lt;/p&gt;\n"
"                &lt;div class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3\" data-oe-protected=\"true\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;🚀&lt;/i&gt;\n"
"                &lt;div class=\"w-100 px-3\" data-oe-protected=\"false\"&gt;\n"
"                        &lt;h4&gt;Demo:&amp;nbsp;receive products and update the stock levels&lt;/h4&gt;\n"
"                        &lt;ul&gt;\n"
"                        &lt;li&gt;Go to &lt;em&gt;Purchase&lt;/em&gt;&amp;nbsp;and open the order&amp;nbsp;&lt;em&gt;P00001&lt;/em&gt;.&lt;/li&gt;\n"
"                        &lt;li&gt;Click​ on &lt;em&gt;Receive Products&lt;/em&gt;&lt;/li&gt;\n"
"                        &lt;li&gt;&lt;em&gt;Validate&lt;/em&gt;&amp;nbsp;the receipt&lt;br /&gt;&lt;/li&gt;\n"
"                        &lt;/ul&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;&lt;br /&gt;&lt;/p&gt;\n"
"                &lt;h3&gt;🧮&lt;span style=\"width: 17px;\" class=\"oe-tabs\"&gt; &lt;/span&gt;​Seasonal physical&amp;nbsp;inventory&amp;nbsp;count&lt;/h3&gt;\n"
"                &lt;p&gt;\n"
"                In many countries, ensuring inventory accuracy is a legal requirement. Use the&amp;nbsp;&lt;strong&gt;&lt;font style=\"color: rgb(113, 75, 103);\"&gt;Physical Inventory&lt;/font&gt;&lt;/strong&gt;​ feature in the Inventory app or&amp;nbsp;the Barcode app to ease and\n"
"                speed up the process.\n"
"                &lt;/p&gt;\n"
"                &lt;div data-oe-protected=\"true\" class=\"o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-info pb-0 pt-3\"&gt;\n"
"                &lt;i class=\"o_editor_banner_icon mb-3 fst-normal\"&gt;💡&lt;/i&gt;\n"
"                &lt;div data-oe-protected=\"false\" class=\"w-100 px-3\"&gt;\n"
"                        &lt;p&gt;Use the Barcode app on mobile devices&amp;nbsp;to scan products with the&amp;nbsp;integrated camera. To speed up the inventory count, split the workload between several people using their personal devices.&lt;/p&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;/div&gt;\n"
"                &lt;p&gt;\n"
"                &lt;a href=\"https://www.odoo.com/slides/slide/695\" class=\"btn btn-secondary mb-2\"&gt;🎓 Barcode tutorial&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/inventory/warehouses_storage/inventory_management/count_products.html\" class=\"btn btn-secondary mb-2\"&gt;📖 Inventory documentation&lt;/a&gt;&amp;nbsp;\n"
"                &lt;a href=\"https://www.odoo.com/documentation/latest/applications/inventory_and_mrp/barcode/operations/adjustments.html\" class=\"btn btn-secondary mb-2\"&gt;📖 Barcode documentation&lt;/a&gt;\n"
"                &lt;/p&gt;"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_1
msgid "0 month"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_3
msgid "1 year"
msgstr "1 жил"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_12
msgid "10 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_62
msgid "10+"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_13
msgid "11 years"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_18
msgid "12 coloring pencils - STABILO"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_14
msgid "12 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_38
msgid "13 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_39
msgid "14 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_40
msgid "15 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_41
msgid "16 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_52
msgid "17 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_53
msgid "18 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_4
msgid "2 years"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_5
msgid "3 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_6
msgid "4 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_7
msgid "5 years"
msgstr ""

#. module: toy_store
#: model:loyalty.reward,description:toy_store.loyalty_reward_2
msgid "5% on your order"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_2
msgid "6 months"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_8
msgid "6 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_9
msgid "7 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_10
msgid "8 years"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_11
msgid "9 years"
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_9
msgid ""
"A 1 000 piece jigsaw featuring the Marauders Map.\n"
"<br>\n"
"<br>\n"
"Measures approx 35×13 inches when assembled. <br>\n"
"Please note all dimensions under ‘Information’ refer to the boxed product."
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.homepage
msgid "A little place <br/>of paradise"
msgstr ""

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_5
msgid "Arts and crafts"
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_7
msgid ""
"As skilled members of a disease-fighting team, you and the other players work together to keep the world safe from outbreaks and epidemics. Only through teamwork will you have a chance to find a cure.\n"
"<br>\n"
"<br>\n"
"Pandemic is a cooperative board game in which players work as a team to treat infections around the world while gathering resources for cures."
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_65
msgid "Assorted"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_46
msgid "Black"
msgstr "Хар"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_47
msgid "Blue"
msgstr "Цэнхэр"

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_1
msgid "Board games"
msgstr ""

#. module: toy_store
#: model:product.attribute,name:toy_store.product_attribute_1
msgid "Brand"
msgstr "Брэнд"

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_4
msgid "Building blocks and sets"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_66
msgid "Calico Critters"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_26
msgid "Calico Critters - Chocolate Rabbit Family"
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_26
msgid ""
"Calico Critters miniature dollhouses playsets and figures are timeless and classic high-quality toys.\n"
"<br>\n"
"<br>\n"
"A little overview: Mother Heidi is always taking care of her family and keeps the house clean and nicely decorated. Father Harlin enjoys planning all the fun community events in Calico Village. Sister Bell is very cheerful and likes having\n"
"fun with her family and friends. Brother Skip is a great soccer player and is on his school’s team."
msgstr ""

#. module: toy_store
#: model:pos.category,name:toy_store.pos_category_1
msgid "Candies"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_5
msgid "Catan - 5-6 player extension"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_4
msgid "Catan - Base game"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_30
msgid "Catan studios"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_22
msgid "Chupa Chups"
msgstr ""

#. module: toy_store
#: model:product.attribute,name:toy_store.product_attribute_4
msgid "Color"
msgstr "Өнгө"

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_4
msgid ""
"Embark on a thrilling quest for dominance in Catan! Settle the isle of "
"Catan, trade resources, and build your way to victory. With a modular board "
"for endless replayability and strategic gameplay balanced with luck, every "
"game night is an exciting adventure. High-quality wooden pieces and easy-to-"
"learn rules make this award-winning board game perfect for players 10 and "
"up."
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "Facebook"
msgstr "Facebook"

#. module: toy_store
#: model:product.tag,name:toy_store.product_tag_1
msgid "Featured"
msgstr "Онцолсон"

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "Follow us"
msgstr "Биднийг дагах"

#. module: toy_store
#: model:loyalty.reward,description:toy_store.loyalty_reward_3
msgid "Free Product - 12 coloring pencils - STABILO"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_43
msgid "Gund"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_44
msgid "Hape"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_9
msgid "Harry Potter: The Marauder's Map Puzzle"
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_21
msgid ""
"Hyper lightweight 10 balance trainer with puncture-proof tires and a high-quality metal frame. The perfect toy to train your toddler gross motor skills balance and steering.\n"
"<br>\n"
"<br>\n"
"Thanks to its lightweight frame your toddler can easily control the bike and in combination with the integrated carry handle it makes it easy for parents to carry it as well."
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_5
msgid ""
"Let additional players join the fun with this Catan 5-6 Player Extension "
"Strategy Board Game. It includes 11 new unique terrain lines for added "
"variety and it has dedicated wooden settlements cities and roads for players"
" 5 and 6."
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: toy_store
#: model:loyalty.program,name:toy_store.loyalty_program_2
msgid "Loyalty Cards"
msgstr ""

#. module: toy_store
#: model:loyalty.program,portal_point_name:toy_store.loyalty_program_2
msgid "Loyalty point(s)"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_12
msgid "Maple Wood Building Blocks"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_21
msgid "Micro scooter"
msgstr ""

#. module: toy_store
#: model:product.attribute,name:toy_store.product_attribute_2
msgid "Minimum recommended age"
msgstr ""

#. module: toy_store
#: model:pos.category,name:toy_store.pos_category_2
msgid "Misc"
msgstr "Бусад"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_32
#: model:product.template,name:toy_store.product_template_6
msgid "Monopoly"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_63
msgid "Multi"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_11
msgid "My 1st Teddy Bear 15\""
msgstr ""

#. module: toy_store
#: model:product.attribute,name:toy_store.product_attribute_3
msgid "Number of players"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.homepage
msgid ""
"Our <span class=\"o_text_highlight o_text_highlight_freehand_3 "
"o_text_highlight_fill\" style=\"--text-highlight-width: 6px; --text-"
"highlight-color:#D15C01;\">selection</span>"
msgstr ""

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_6
msgid "Outdoors"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_7
msgid "Pandemic"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_51
msgid "Pink"
msgstr ""

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_3
msgid "Plush toys"
msgstr ""

#. module: toy_store
#: model:product.public.category,name:toy_store.product_public_category_2
msgid "Puzzles"
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_6
msgid ""
"Select a favorite Monopoly token, place it on Go and roll the dice to own it all!\n"
"<br>\n"
"<br>\n"
"It's all about buying, selling, and trading properties to win. Chance or Community cards can be a game changer. Will they make a player richer - or send someone to Jail?"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.homepage
msgid "Shop <span class=\"fa fa-angle-right ms-2\"/>"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_50
msgid "Silver"
msgstr "Мөнгөн"

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_45
msgid "Stabilo"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_37
msgid "The Noble Collection"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "Twitter"
msgstr "Жиргээ"

#. module: toy_store
#: model_terms:web_tour.tour,rainbow_man_message:toy_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: toy_store
#: model_terms:product.template,description_ecommerce:toy_store.product_template_10
msgid ""
"While a red fox in the wild is typically a solitary creature the red fox "
"plush by Wild Republic is the perfect partner in crime for any stuffed "
"animal lover. At 12 long this plush toy is the perfect size to take on any "
"adventure."
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_10
msgid "Wild Republic - Red Fox"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_42
msgid "Wild republic"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.homepage
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""

#. module: toy_store
#: model:product.attribute.value,name:toy_store.product_attribute_value_36
msgid "Z-man games"
msgstr ""

#. module: toy_store
#: model:product.template,name:toy_store.product_template_23
msgid "[Example product] PEZ dispenser and candy"
msgstr ""

#. module: toy_store
#: model_terms:ir.ui.view,arch_db:toy_store.customfooter_view
msgid "<EMAIL>"
msgstr ""
