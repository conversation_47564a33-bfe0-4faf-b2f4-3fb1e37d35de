# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* construction
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 12:51+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"*This list will, of course, depend on your installed apps and configuration."
msgstr ""
"*Deze lijst is natuurlijk afhankelijk van je geïnstalleerde apps en "
"configuratie."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"<i class=\"o_editor_banner_icon mb-3 fst-normal\" aria-label=\"Banner "
"Info\">💡</i>"
msgstr ""
"<i class=\"o_editor_banner_icon mb-3 fst-normal\" aria-label=\"Banner "
"Info\">💡</i>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<span class=\"h2-fs\"><strong>Employees 🧑‍🤝‍🧑</strong></span>"
msgstr "<span class=\"h2-fs\"><strong>Werknemers 🧑‍🤝‍🧑</strong></span>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<span class=\"h2-fs\"><strong>Equipment 🛠️</strong></span>"
msgstr "<span class=\"h2-fs\"><strong>Uitrusting 🛠️</strong></span>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<span class=\"h2-fs\"><strong>Take care of your resources </strong></span>"
msgstr "<span class=\"h2-fs\"><strong>Zorg voor je bronnen </strong></span>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<span class=\"h2-fs\">Make your customers happy </span>🆘"
msgstr "<span class=\"h2-fs\">Maak je klanten blij </span>🆘"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Accounting made easy 🧮</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Eenvoudig boekhouden 🧮</span></strong>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"<strong><span class=\"h2-fs\">Create an attractive website 🌐</span></strong>"
msgstr ""
"<strong><span class=\"h2-fs\">Maak een aantrekkelijke website "
"🌐</span></strong>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Fleet 🚚</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Vloot 🚚</span></strong>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Sell product sets 🔩</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Productsets verkopen 🔩</span></strong>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"A good work cannot be achieved without motivated and skilled employees, "
"reliable equipment nor suitable vehicles."
msgstr ""
"Goed werk kan niet worden geleverd zonder gemotiveerde en vakkundige "
"medewerkers, betrouwbare apparatuur of geschikte voertuigen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"A link to reach the online quotation from where the customer can confirm and"
" sign it."
msgstr ""
"Een link naar de online offerte van waaruit de klant deze kan bevestigen en "
"ondertekenen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Accurately track the project 🔍"
msgstr "Het project nauwkeurig volgen 🔍"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"All these components will give you an accurate overview on the project "
"profitability."
msgstr ""
"Al deze onderdelen geven je een nauwkeurig overzicht van de winstgevendheid "
"van het project."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Amazing employees deserve amazing equipment. Make the sure all your tools are fit and well by tracking their health in the\n"
"            <strong><font class=\"text-o-color-1\">Maintenance</font></strong> app."
msgstr ""
"Geweldige werknemers verdienen geweldige apparatuur. Zorg ervoor dat al je gereedschap fit en gezond is door hun gezondheid bij te houden in de\n"
"            <strong><font class=\"text-o-color-1\">Onderhoud</font></strong> app."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Analytic Items ➡️ Track any cost using Analytic Items from the Accounting "
"App"
msgstr ""
"Analytische items ➡️ Volg alle kosten met behulp van Analytische items "
"vanuit de boekhoudapplicatie"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Architect"
msgstr "Architect"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"As the first invoicing step, create a down payment from the sales order. This will generate the first invoice in the\n"
"            <strong><font class=\"text-o-color-1\">Invoicing</font></strong> app. Then, schedule one or several \"Invoicing Schedule\" activities on the sales order with the aim to never forget to send an intermediary invoice in order to sustain\n"
"            your cash flow."
msgstr ""
"Maak als eerste factureringsstap een aanbetaling aan op de verkooporder. Dit genereert de eerste factuur in de\n"
"            <strong><font class=\"text-o-color-1\">Facturatie</font></strong> app. Plan vervolgens één of meerdere \"Factureringsschema\" activiteiten in op de verkooporder met als doel om nooit te vergeten een tussentijdse factuur te sturen om je cashflow op peil te houden\n"
"            je cashflow."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Assign the tasks to the right responsible and easily check the progress in "
"the Kanban view."
msgstr ""
"Wijs de taken toe aan de juiste verantwoordelijke en controleer eenvoudig de"
" voortgang in de Kanban-weergave."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"At any time, you can create from an opportunity a beautiful and tailor-made "
"quotation that displays the price, the discount, the unit of measure, and "
"the tax of each:"
msgstr ""
"Op elk moment kun je van een kans een mooie en op maat gemaakte offerte "
"maken die de prijs, de korting, de maateenheid en de belasting weergeeft:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Basics"
msgstr "Basisprincipes"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Before generating an intermediary invoice in the\n"
"            <strong><font class=\"text-o-color-1\">Invoicing</font></strong> app, freeze the project status in the <strong><font class=\"text-o-color-1\">Project</font></strong> app to get an overview on the progress in terms of:"
msgstr ""
"Voordat je een tussenfactuur genereert in de\n"
"            <strong><font class=\"text-o-color-1\">Facturatie</font></strong> app, bevries je de projectstatus in de <strong><font class=\"text-o-color-1\">Project</font></strong> app om een overzicht te krijgen van de voortgang in termen van:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Before starting the project, make sure you have enough stock. The confirmation of the quotation also triggers the creation of a delivery in the <strong><font class=\"text-o-color-1\">Inventory</font></strong>\n"
"            app where you can check the stock levels of each needed storable product."
msgstr ""
"Zorg dat je voldoende voorraad hebt voordat je aan het project begint. De bevestiging van de offerte zorgt er ook voor dat er een levering wordt aangemaakt in de <strong><font class=\"text-o-color-1\">Voorraad</font></strong>\n"
"            app waar je de voorraadniveaus kunt controleren van elk product dat je nodig hebt."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Bills of Materials"
msgstr "Stuklijsten"

#. module: construction
#: model:product.template,name:construction.product_product_5_product_template
msgid "Building Blocks 39x14x19cm"
msgstr "Bouwstenen 39x14x19cm"

#. module: construction
#: model:project.task.type,name:construction.planning_project_stage_4
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Centralize all the important information 📝"
msgstr "Alle belangrijke informatie centraliseren 📝"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Construction"
msgstr "Bouw"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Create an opportunity each time you face a potential customer, receive a "
"phone call, etc."
msgstr ""
"Maak een kans telkens als je een potentiële klant tegenkomt, een telefoontje"
" krijgt, enz."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Dashboard ➡️ Your project's status at a glance."
msgstr "Dashboard ➡️ De status van je project in één oogopslag."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Do you want to go further?"
msgstr "Wil je verder gaan?"

#. module: construction
#: model:project.task.type,name:construction.planning_project_stage_3
msgid "Done"
msgstr "Gereed"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Down Payment"
msgstr "Aanbetaling"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"During and after the construction project, provide a five star customer service by answering the questions from your customer through the\n"
"            <strong><font class=\"text-o-color-1\">Helpdesk</font></strong> app. All the emails sent to"
msgstr ""
"Bied tijdens en na het bouwproject een vijfsterrenklantenservice door de vragen van je klant te beantwoorden via de\n"
"            <strong><font class=\"text-o-color-1\">Helpdesk</font></strong> app. Alle e-mails die naar"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Easily create beautiful quotations 💲"
msgstr "Maak eenvoudig mooie citaten 💲"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Efficiently schedule the work 📆"
msgstr "Het werk efficiënt inplannen 📆"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Enrich the core apps by installing the following ones."
msgstr "Verrijk de kernapps door de volgende apps te installeren."

#. module: construction
#: model:account.analytic.account,name:construction.account_analytic_account_2
msgid "Field Service"
msgstr "Buitendienst"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"For each ordered material, don't forget to fill the analytic distribution in"
" which you will be able to set for which project it will be used."
msgstr ""
"Vergeet niet om voor elk besteld materiaal de analytische verdeling in te "
"vullen waarin je kunt instellen voor welk project het zal worden gebruikt."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"From WH ➡️ Deliveries from your warehouse to this specific construction site"
msgstr ""
"Van WH ➡️ Leveringen van jouw magazijn naar deze specifieke bouwplaats"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"If a technical operation such as a repair is needed, directly create a task in the <strong><font class=\"text-o-color-1\">Field Service</font></strong> project from a ticket. Then assign it and schedule it to quickly solve the\n"
"            problem. Your employee will be able to use some material to fix the issue. Then these materials and the time spent could be invoiced to the customer."
msgstr ""
"Als een technische handeling zoals een reparatie nodig is, maak dan direct een taak aan in de <strong><font class=\"text-o-color-1\">Buitendienst</font></strong> project vanuit een ticket. Wijs hem dan toe en plan hem in om het probleem snel op te lossen\n"
"            probleem op te lossen. Je medewerker kan wat materiaal gebruiken om het probleem op te lossen. Deze materialen en de bestede tijd kunnen dan worden gefactureerd aan de klant."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"If the customer replies to the email, the answer is automatically logged in the chatter on the right of the screen. Thereby, all the discussions regarding the quotation, its updates, and its fine tuning are attached, and the history\n"
"            can be easily consulted by all the involved users"
msgstr ""
"Als de klant de e-mail beantwoordt, wordt het antwoord automatisch bijgehouden in de chatter rechts op het scherm. Zo worden alle discussies over de offerte, de updates en de fijnafstemming bijgehouden en kan de geschiedenis\n"
"            kan gemakkelijk worden geraadpleegd door alle betrokken gebruikers"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"If the stock levels are too low, resupply the materials from the\n"
"            <strong><font class=\"text-o-color-1\">Purchase</font></strong> app by creating a purchase order linked to the right vendor. In the <strong><font class=\"text-o-color-1\">Contact</font></strong> app, you can easily manage all your\n"
"            preferred vendors and don't hesitate to set a warning to remind if there is a minimum amount to get free transport costs."
msgstr ""
"Als de voorraden te laag zijn, vul dan de materialen opnieuw aan vanuit de\n"
"            <strong><font class=\"text-o-color-1\">Inkoop</font></strong> app door een inkooporder aan te maken die gekoppeld is aan de juiste leverancier. In de <strong><font class=\"text-o-color-1\">Contact</font></strong> app kun je eenvoudig al je\n"
"            en aarzel niet om een waarschuwing in te stellen als er een minimumbedrag is om gratis transportkosten te krijgen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr ""
"Als je nog veel verder wilt gaan met het maken van offertes en alle kracht "
"van Spreadsheet wilt toevoegen aan elke regel van je verkooporders, gebruik "
"dan de functie Offerteberekening."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"If your country benefits from a tax rebate under certain conditions, you can"
" easily apply it to your entire quotation by creating a fiscal position that"
" will automatically replace the default tax rate by the reduced one"
msgstr ""
"Als jouw land onder bepaalde voorwaarden profiteert van een "
"belastingkorting, kun je deze eenvoudig toepassen op je hele offerte door "
"een fiscale positie te creëren die automatisch het standaard belastingtarief"
" vervangt door het verlaagde tarief"

#. module: construction
#: model:project.task.type,name:construction.planning_project_stage_2
msgid "In Progress"
msgstr "In behandeling"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Increase you visibility and get more leads by creating a beautiful\n"
"            <strong><font class=\"text-o-color-1\">Website</font></strong>! When a potential customer sends a request through the contact form, it could automatically create an opportunity in the\n"
"            <strong><font class=\"text-o-color-1\">CRM</font></strong> app."
msgstr ""
"Vergroot je zichtbaarheid en krijg meer leads door een prachtige\n"
"            <strong><font class=\"text-o-color-1\">Website</font></strong>! Wanneer een potentiële klant een verzoek stuurt via het contactformulier, kan dit automatisch een kans creëren in de\n"
"            <strong><font class=\"text-o-color-1\">CRM</font></strong> app."

#. module: construction
#: model:account.analytic.account,name:construction.account_analytic_account_1
#: model:project.project,name:construction.project_project_1
#: model:project.task.type,name:construction.internal_project_default_stage
msgid "Internal"
msgstr "Intern"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Invoice at the right time 📈"
msgstr "Factureer op het juiste moment 📈"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Invoices"
msgstr "Facturen"

#. module: construction
#: model:mail.activity.type,name:construction.mail_activity_type_20
msgid "Invoicing Schedule"
msgstr "Factureringsschema"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Keep an eye on materials 📦"
msgstr "Houd materialen in de gaten 📦"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Know the skills of each employee by filling them in the\n"
"            <strong><font class=\"text-o-color-1\">Employees</font></strong> app. This will help you to find the right person to make a good work."
msgstr ""
"Ken de vaardigheden van elke werknemer door ze in te vullen in de\n"
"            <strong><font class=\"text-o-color-1\">Medewerkers</font></strong> app. Dit zal je helpen de juiste persoon te vinden om goed werk te leveren."

#. module: construction
#: model:product.template,name:construction.product_product_8_product_template
msgid "Labor hour with equipment"
msgstr "Arbeidsuren met apparatuur"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Make your customers happy 🆘"
msgstr "Maak je klanten blij 🆘"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Manage your vehicles in the\n"
"            <strong><font class=\"text-o-color-1\">Fleet</font></strong> app."
msgstr ""
"Beheer je voertuigen in de\n"
"            <strong><font class=\"text-o-color-1\">Vloot</font></strong> app."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Manufacturing Orders"
msgstr "Productieorders"

#. module: construction
#: model:product.template,name:construction.product_product_6_product_template
msgid "Masonry mortar 25kg"
msgstr "Metselmortel 25kg"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Material that will be used to build the project."
msgstr "Materiaal dat wordt gebruikt om het project te bouwen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Materials purchased."
msgstr "Aangeschafte materialen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Materials sold."
msgstr "Verkochte materialen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Never miss an opportunity 🎯"
msgstr "Mis nooit een kans 🎯"

#. module: construction
#: model:project.task.type,name:construction.planning_project_stage_0
msgid "New"
msgstr "Nieuw"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Odoo empowers you to manage every aspect of your construction projects "
"effortlessly and efficiently, and its integrated applications ensure "
"unparalleled success and client satisfaction."
msgstr ""
"Odoo stelt je in staat om elk aspect van je bouwprojecten moeiteloos en "
"efficiënt te beheren en de geïntegreerde toepassingen zorgen voor "
"ongeëvenaard succes en klanttevredenheid."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Of course, feel free to create all the products you use often. This will "
"save you time when preparing when preparing your next quotations."
msgstr ""
"Voel je natuurlijk vrij om alle producten die je vaak gebruikt aan te maken."
" Dit bespaart je tijd bij het voorbereiden van je volgende offertes."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Of course, these default tasks can be adapted in the Project Template "
"according to your personal way of managing a construction project."
msgstr ""
"Natuurlijk kunnen deze standaardtaken in het Project Sjabloon worden "
"aangepast aan jouw persoonlijke manier om een bouwproject te beheren."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Once confirmed, the quotation becomes a sales order. All quotations and "
"sales orders are centralized in the <strong><font class=\"text-o-"
"color-1\">Sales</font></strong> app."
msgstr ""
"Na bevestiging wordt de offerte een verkooporder. Alle offertes en "
"verkooporders worden gecentraliseerd in de <strong><font class=\"text-o-"
"color-1\">Verkoop</font></strong> app."

#. module: construction
#: model:uom.uom,name:construction.uom_uom_28
msgid "Pallet of 48"
msgstr "Pallet van 48"

#. module: construction
#: model:uom.uom,name:construction.uom_uom_27
msgid "Pallet of 96"
msgstr "Pallet van 96"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Planification"
msgstr "Planificatie"

#. module: construction
#: model:project.task.type,name:construction.planning_project_stage_1
msgid "Planned"
msgstr "Gepland"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Planning"
msgstr "Planning"

#. module: construction
#: model:account.analytic.account,name:construction.account_analytic_account_3
#: model:project.project,name:construction.project_project_3
msgid "Project Template"
msgstr "Projectsjabloon"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Purchase Orders"
msgstr "Inkooporders"

#. module: construction
#: model:hr.job,name:construction.hr_job_1
msgid "Qualified Worker"
msgstr "Gekwalificeerd werknemer"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Quickly start the construction projects 🏗️"
msgstr "Start snel de bouwprojecten 🏗️"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Receipt"
msgstr "Ontvangst"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Record the time spent by your employees on each project by using the\n"
"            <strong><font class=\"text-o-color-1\">Timesheets</font></strong> app. Don't forget to define a hourly cost on the employee form."
msgstr ""
"Noteer de tijd die je medewerkers aan elk project hebben besteed met behulp van de optie\n"
"            <strong><font class=\"text-o-color-1\">Urenstaten</font></strong> app. Vergeet niet om een uurtarief te definiëren op het werknemersformulier."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Redirect the emails sent to"
msgstr "Stuur de e-mails door naar"

#. module: construction
#: model:account.analytic.account,name:construction.account_analytic_account_4
#: model:project.project,name:construction.project_project_4
msgid "S00001 - VDK Project"
msgstr "S00001 - VDK Project"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Sales Orders"
msgstr "Verkooporders"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Save time by directly sending the quotation by email to your customer. The "
"personalized email provides:"
msgstr ""
"Bespaar tijd door de offerte direct per e-mail naar je klant te sturen. De "
"gepersonaliseerde e-mail biedt:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Schedule a demo"
msgstr "Een demo inplannen"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Service such as labor hours, traveling costs, etc."
msgstr "Service zoals arbeidsuren, reiskosten, enz."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Speed up the signing process thanks to the\n"
"            <strong><font class=\"text-o-color-1\">Sign</font></strong> app. Once again, all the signed documents will be stored in the <strong><font class=\"text-o-color-1\">Documents</font></strong> app."
msgstr ""
"Versnel het ondertekeningsproces dankzij de\n"
"            <strong><font class=\"text-o-color-1\">Ondertekenen</font></strong> app. Nogmaals, alle ondertekende documenten worden opgeslagen in de <strong><font class=\"text-o-color-1\">Documenten</font></strong> app."

#. module: construction
#: model:product.template,description_sale:construction.product_product_5_product_template
msgid ""
"Standard gray concrete block, hollow, 39x14x19 cm, for all load-bearing and "
"non-load-bearing masonry, indoors and outdoors. This concrete block meets "
"the strict quality requirements of the BENOR label."
msgstr ""
"Standaard grijs betonblok, hol, 39x14x19 cm, voor alle dragend en niet-"
"dragend metselwerk, binnen en buiten. Dit betonblok voldoet aan de strenge "
"kwaliteitseisen van het BENOR-label."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Starting a construction project implies a lot of important documents such as contracts, building plans, instructions for use, subcontracting agreements, etc. Instead of scattering them in your emails, storage services or computer,\n"
"            attach them in the chatter and they will be automatically centralized in the\n"
"            <strong><font class=\"text-o-color-1\">Documents</font></strong> app."
msgstr ""
"Het starten van een bouwproject brengt veel belangrijke documenten met zich mee, zoals contracten, bouwtekeningen, gebruiksaanwijzingen, onderaannemingsovereenkomsten, enz. In plaats van ze te verspreiden in je e-mails, opslagdiensten of computer,\n"
"            voeg ze toe aan de chatter en ze worden automatisch gecentraliseerd in de\n"
"            <strong><font class=\"text-o-color-1\">Documenten</font></strong> app."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Take advantage of every opportunity. The <strong><font class=\"text-o-color-1\">CRM</font></strong> centralizes everything and allows you to ensure the follow-up of each request efficiently. If the request becomes concrete, create the\n"
"            customer in the <strong><font class=\"text-o-color-1\">Contact</font></strong> app."
msgstr ""
"Maak gebruik van elke kans. De <strong><font class=\"text-o-color-1\">CRM</font></strong> centraliseert alles en stelt je in staat om elke aanvraag efficiënt op te volgen. Als het verzoek concreet wordt, maak je de\n"
"            klant aan in de <strong><font class=\"text-o-color-1\">Contact</font></strong> app."

#. module: construction
#: model:project.project,label_tasks:construction.project_project_1
#: model:project.project,label_tasks:construction.project_project_3
#: model:project.project,label_tasks:construction.project_project_4
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Tasks"
msgstr "Taken"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Thanks to the invoicing policy set on delivered quantities, the generated "
"invoice is always the reflect of the reality on the field and it is fair for"
" the customer."
msgstr ""
"Dankzij het factureringsbeleid dat is ingesteld op geleverde hoeveelheden, "
"is de gegenereerde factuur altijd een afspiegeling van de realiteit op het "
"veld en eerlijk voor de klant."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"> <strong>CRM</strong> </font> application"
" allows you to collect requests from your potential customers by aggregating"
" several sources:"
msgstr ""
"De <font class=\"text-o-color-1\"> <strong>CRM</strong> </font> applicatie "
"kun je aanvragen van je potentiële klanten verzamelen door verschillende "
"bronnen samen te voegen:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "The customer reminders."
msgstr "De herinneringen van de klant."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "The financial reporting."
msgstr "De financiële verslaglegging."

#. module: construction
#: model:product.template,description_sale:construction.product_product_6_product_template
msgid ""
"The prepared mortar is a dry, industrially premixed masonry mortar in "
"strength class M5. The mortar is mainly designed for load-bearing and non-"
"load-bearing masonry."
msgstr ""
"De bereide mortel is een droge, industrieel voorgemengde metselspecie in "
"sterkteklasse M5. De mortel is voornamelijk bedoeld voor dragend en niet-"
"dragend metselwerk."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"The quotation confirmation automatically triggers the creation of a "
"dedicated project in the... <strong><font class=\"text-o-"
"color-1\">Project</font></strong> app! Each project is initiated with "
"several default tasks, such as:"
msgstr ""
"De offertebevestiging leidt automatisch tot het aanmaken van een speciaal "
"project in... <strong><font class=\"text-o-color-1\">Project</font></strong>"
" app! Elk project wordt gestart met verschillende standaardtaken, zoals:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "The quotation in PDF format"
msgstr "De offerte in PDF-formaat"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "The synchronisation with the banks."
msgstr "De synchronisatie met de banken."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "The vendor bills payment."
msgstr "De verkoper factureert de betaling."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"This industry is tailor-made for construction businesses that excel in managing construction projects. It encompasses the crucial stages of project conceptualization, accurate quotation(s), seamless project kick-off, meticulous\n"
"            planning, streamlined (re)supply, flawless execution, effective collaboration with third parties, meticulous budget follow-up, prompt invoicing, and exceptional customer service."
msgstr ""
"Deze branche is op maat gemaakt voor bouwbedrijven die uitblinken in het beheren van bouwprojecten. Het omvat de cruciale stadia van projectconceptualisatie, nauwkeurige offerte(s), naadloze projectstart, nauwgezette planning, gestroomlijnde (ver)levering, vlekkeloze uitvoering, effectieve samenwerking met derden, nauwgezette budgetopvolging, prompte facturering en uitzonderlijke klantenservice\n"
"            planning, gestroomlijnde (ver)levering, vlekkeloze uitvoering, effectieve samenwerking met derden, nauwgezette budgetopvolging, prompte facturering en uitzonderlijke klantenservice."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Time spent on the project."
msgstr "Tijd besteed aan het project."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Timesheets"
msgstr "Urenstaten"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "To WH ➡️ Products sent back to your warehouse"
msgstr "Naar WH ➡️ Producten terugsturen naar je magazijn"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"To ease the quotation of usually sold together, the\n"
"            <strong><font class=\"text-o-color-1\">Manufacturing</font></strong> app allows you to create kits. These can contain several products. When added in a quotation, the kits is displayed as a single line but the delivery order displays\n"
"            all the components to pick up."
msgstr ""
"Om het noteren van meestal samen verkochte producten te vergemakkelijken, is de\n"
"            <strong><font class=\"text-o-color-1\">Productie</font></strong> app kun je kits maken. Deze kunnen meerdere producten bevatten. Wanneer ze worden toegevoegd aan een offerte, worden de kits weergegeven als één regel, maar de leveringsorder toont\n"
"            alle onderdelen om op te halen."

#. module: construction
#: model:product.template,name:construction.product_product_7_product_template
msgid "Travelling costs"
msgstr "Reiskosten"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Accounting</font></strong> "
"app to easily handle:"
msgstr ""
"Gebruik de <strong><font class=\"text-o-"
"color-1\">Boekhouding</font></strong> app om eenvoudig af te handelen:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Planning</font></strong> app to organize the work of your employees. Define on which project and when they have to work. Easily manage shift conflicts, workload and replacements in case\n"
"            of leaves."
msgstr ""
"Gebruik de <strong><font class=\"text-o-color-1\">Planning</font></strong> app om het werk van je medewerkers te organiseren. Bepaal op welk project en wanneer ze moeten werken. Beheer eenvoudig shiftconflicten, werklast en vervangingen in geval van\n"
"            van verlof."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"Use the Website app to create a contact form that will redirect all the "
"requests to the <strong><font class=\"text-o-color-1\">CRM</font></strong>."
msgstr ""
"Gebruik de Website-app om een contactformulier te maken dat alle verzoeken "
"doorstuurt naar het <strong><font class=\"text-o-"
"color-1\">CRM</font></strong>."

#. module: construction
#: model_terms:web_tour.tour,rainbow_man_message:construction.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welkom! Veel plezier met verkennen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Work"
msgstr "Werk"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Wil je jouw Odoo setup met ons bespreken of nog verder gaan?"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"You can adapt your overview for each project by selecting the small \"Sliders\" icon at the top right of your screen. It will allow you to add a bunch of practical views to get the complete overview of your project,\n"
"                    including*:"
msgstr ""
"Je kunt je overzicht voor elk project aanpassen door het kleine \"schuifregelaars\" pictogram rechtsboven in je scherm te selecteren. Hiermee kun je een aantal praktische weergaven toevoegen om een compleet overzicht van je project te krijgen,\n"
"                    inclusief*:"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid ""
"You can add other views with specific filters and ease your construction "
"project follow-up."
msgstr ""
"Je kunt andere weergaven met specifieke filters toevoegen en de follow-up "
"van je bouwproject vereenvoudigen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "to the <strong><font class=\"text-o-color-1\">CRM</font></strong>."
msgstr "naar de <strong><font class=\"text-o-color-1\">CRM</font></strong>."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "will create a ticket that can be assigned to ensure the follow-up."
msgstr ""
"maakt een ticket aan dat kan worden toegewezen om de follow-up te "
"garanderen."

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Accounting"
msgstr "boekhouding"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Activities"
msgstr "activiteiten"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 CRM"
msgstr "cRM"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Documents"
msgstr "documenten"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Employees"
msgstr "werknemers"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Field Service"
msgstr "buitendienst"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Fiscal Positions"
msgstr "fiscale posities"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Fleet"
msgstr "vloot"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Helpdesk"
msgstr "helpdesk"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Inventory"
msgstr "🎓 Voorraad"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Invoicing"
msgstr "facturering"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Maintenance"
msgstr "onderhoud"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Manufacturing"
msgstr "productie"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Project"
msgstr "project"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Purchase"
msgstr "🎓 Inkoop"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Sign"
msgstr "teken"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Timesheets"
msgstr "urenstaten"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓 Website"
msgstr "website"

#. module: construction
#: model_terms:ir.ui.view,arch_db:construction.welcome_article_body
msgid "🎓Sales"
msgstr "verkoop"
