# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* furniture_store
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:39+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"&amp; For large or heavy products, the order  &amp;amp; payment is done at "
"the shop, but the delivery is sometimes scheduled &amp; later."
msgstr "&amp; 부피가 크거나 무거운 제품의 경우 주문 및 결제는 매장에서 이루어지지만 배송은 추후에 예약되는 경우가 있습니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"1) Show the B2B Form: on the main page, you can \"<strong><font "
"style=\"color: rgb(0, 0, 255);\">Request a Quote</font></strong>\""
msgstr ""
"1) B2B 양식 표시: 기본 페이지에서 \"<strong><font style=\"color: rgb(0, 0, 255);\">견적 "
"요청</font></strong>\"을 할 수 있습니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"2) Visit the B2C Shop on the website: show variants and\n"
"            <strong>\n"
"                <font style=\"color: rgb(0, 0, 255);\">select other colors on Sofa</font>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-weight: normal;\">\n"
"                        .<br/>\n"
"                        <br/>\n"
"                         &amp; Size and price of the product change according to the variant.\n"
"                    </span>\n"
"                </font>\n"
"            </strong>\n"
"            <br/>"
msgstr ""
"2) 웹사이트의 B2C 상점으로 이동: 품목 세부 사항을 표시하고\n"
"            <strong>\n"
"                <font style=\"color: rgb(0, 0, 255);\">소파의 다른 색상을 선택합니다</font>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-weight: normal;\">\n"
"                        .<br/>\n"
"                        <br/>\n"
"                         &amp; 선택한 품목 세부사항에 따라 제품의 크기와 가격이 조정됩니다.\n"
"                    </span>\n"
"                </font>\n"
"            </strong>\n"
"            <br/>"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_6
msgid "4 seater"
msgstr "4인승"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_7
msgid "6 seater"
msgstr "6인승"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font><br/>\n"
"                                                        Sports Reporter"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font><br/>\n"
"                                                        스포츠부 기자"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font><br/>\n"
"                                                        Editor-in-chief"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font><br/>\n"
"                                                        편집장"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font><br/>\n"
"                                                        Photograph"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font><br/>\n"
"                                                        사진 작가"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font><br/>\n"
"                                                        Political Reporter"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font><br/>\n"
"                                                        정치부 기자"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">\n"
"                    <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\">\n"
"                        <span style=\"font-size: 14px;\">Point of Sale with Ship later</span>\n"
"                    </span>\n"
"                </font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">\n"
"                    <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\">\n"
"                        <span style=\"font-size: 14px;\">추후 배송 POS</span>\n"
"                    </span>\n"
"                </font>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">\n"
"                <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\"><span style=\"font-size: 14px;\">The setup includes:</span></span>\n"
"            </font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">\n"
"                <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\"><span style=\"font-size: 14px;\">다음 내용이 설정에 포함됩니다:</span></span>\n"
"            </font>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">사이트 제목</font>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">전화 번호</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">제목</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">회사</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">이메일</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">이름</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"<span class=\"s_website_form_label_content\">Your requirement</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">요청 사항</span>\n"
"                                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">Later, in the</span>\n"
"                <span style=\"color: rgb(0, 0, 255); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\"> &amp; </span>\n"
"                <span style=\"font-size: 14px; font-style: normal; font-weight: 400; color: rgb(0, 0, 255); background-color: rgb(255, 255, 255);\"><strong style=\"font-weight: 500;\">Inventory App</strong></span>\n"
"                <span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">, the worker &amp; uses the list of orders to &amp; deliver in the \"</span>\n"
"                <span style=\"font-size: 14px; font-style: normal; color: rgb(0, 0, 255); background-color: rgb(255, 255, 255);\"><strong>Delivery Orders</strong></span>\n"
"                <span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">\n"
"                    \" section. Check what products &amp; are ready to be delivered, and validate the delivery order as you send them to clients.\n"
"                </span>\n"
"                <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">나중에</span>\n"
"                <span style=\"color: rgb(0, 0, 255); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\"> &amp; </span>\n"
"                <span style=\"font-size: 14px; font-style: normal; font-weight: 400; color: rgb(0, 0, 255); background-color: rgb(255, 255, 255);\"><strong style=\"font-weight: 500;\">재고 관리 앱</strong></span>\n"
"                <span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">에서 작업자는 \"</span>\n"
"                <span style=\"font-size: 14px; font-style: normal; color: rgb(0, 0, 255); background-color: rgb(255, 255, 255);\"><strong>배송 주문</strong></span>\n"
"                <span style=\"color: rgb(55, 65, 81); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);\">\n"
"                    \" 섹션에서 배송할 주문 목록을 사용합니다. 배송 준비가 완료된 제품을 검토하고 고객에게 전송할 때 배송 주문을 확인합니다.\n"
"                </span>\n"
"                <br/>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<span style=\"font-size: 11pt; color: rgb(0, 0, 0); background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none;\"/>\n"
"                <font style=\"color: var(--color) !important;\">\n"
"                    <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\">\n"
"                        <span style=\"font-size: 14px;\">Pricelists: Regular customer, Default pricelist</span>\n"
"                    </span>\n"
"                </font>"
msgstr ""
"<span style=\"font-size: 11pt; color: rgb(0, 0, 0); background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none;\"/>\n"
"                <font style=\"color: var(--color) !important;\">\n"
"                    <span style=\"color: var(--color) !important; background-color: transparent; font-weight: 400; font-style: normal; text-decoration: none; font-size: 18px;\">\n"
"                        <span style=\"font-size: 14px;\">가격 목록: 일반 고객, 기본 가격표</span>\n"
"                    </span>\n"
"                </font>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-color-5\">Flow 4 :"
" Sell Product in KIT</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-color-5\">흐름 4: 키트"
" 제품 판매</font></span>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\"><font style=\"color: rgb(0, 0, "
"255);\">Create a &amp; POS order</font></strong> &amp; with heavy products "
"(i.e. dining Table)"
msgstr ""
"무거운 제품 (예: 식탁)으로 <strong style=\"font-weight: 500;\"><font style=\"color: "
"rgb(0, 0, 255);\">&amp; POS 주문 만들기</font></strong> &amp; "

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\"><font style=\"color: rgb(0, 0, "
"255);\">Create a &amp; POS order</font></strong> &amp; with the required "
"product(s)"
msgstr ""
"필요한 제품으로 <strong style=\"font-weight: 500;\"><font style=\"color: rgb(0, 0, "
"255);\">&amp; POS 주문 만들기</font></strong> &amp; "

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-size: 18px;\"><span style=\"color: var(--color) !important;\">Ship Later</span></span>\n"
"                </font>\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-size: 18px;\"><span style=\"color: var(--color) !important;\">추후 배송</span></span>\n"
"                </font>\n"
"            </strong>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-weight: normal;\">\n"
"                         &amp; When customer have specific requirement then based on that requirement we will buy that product from 3rd party (Vendor).<br/>\n"
"                        <br/>\n"
"                    </span>\n"
"                </font>\n"
"            </strong>\n"
"            To do that start with Sales app,"
msgstr ""
"<strong>\n"
"                <font class=\"text-o-color-5\">\n"
"                    <span style=\"font-weight: normal;\">\n"
"                         &amp; 고객에게 특정한 요구 사항이 있는 경우 해당 요청에 따라 타사 (공급업체)로부터 해당 품목을 구매합니다.<br/>\n"
"                        <br/>\n"
"                    </span>\n"
"                </font>\n"
"            </strong>\n"
"            진행하려면 판매 앱에서 작업을 시작합니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-size: 18px; font-weight: bolder;\">Flow 3 : Order for customized product</span></font>\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-size: 18px; font-weight: bolder;\">흐름 3: 맞춤형 제품 주문</span></font>\n"
"            </strong>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">To do this;</span></font>\n"
"            </strong>\n"
"            <br/>"
msgstr ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">진행하려면;</span></font>\n"
"            </strong>\n"
"            <br/>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">You can use this flow for a product \"Bed\"  &amp; as configuration is already setup. &amp;  &amp;   &amp;  &amp;  &amp;   &amp;  &amp; </span></font>\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">환경 설정 메뉴가 이미 설정되어 있으므로 \"침대\" 제품에 이 흐름을 적용할 수 있습니다. &amp;  &amp;   &amp;  &amp;  &amp;   &amp;  &amp; </span></font>\n"
"            </strong>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "<strong>Salesperson meet client</strong>"
msgstr "<strong>영업 담당자 고객 방문</strong>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "<strong>Website: Request a Quote</strong>"
msgstr "<strong>웹사이트: 견적 요청</strong>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "A Section Subtitle"
msgstr "영역 소제목"

#. module: furniture_store
#: model:website.menu,name:furniture_store.website_menu_9
msgid "About Us"
msgstr "회사 소개"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "About us"
msgstr "회사 소개"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Add component for that product with quantity"
msgstr "해당 제품에 대한 구성품을 수량과 함께 추가"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She likes to capture the moment from another point of view, another "
"perspective."
msgstr ""
"Aline은 자신의 일을 사랑하는 대표적인 인물 중 한 명입니다. 또 다른 관점, 다른 시각에서 순간을 포착하는 것을 즐깁니다."

#. module: furniture_store
#: model:product.template,description_sale:furniture_store.product_template_9
msgid ""
"Andrie Engineered Wood 2 Door Wardrobe With Mirror In White & Walnut Finish"
msgstr "화이트 & 월넛 마감의 거울이 달린 앤드리 엔지니어드 우드 2도어 옷장"

#. module: furniture_store
#: model:product.template,description_sale:furniture_store.product_template_10
msgid "Bayleef Solid Wood Queen Size Drawer Storage Bed In Teak Finish."
msgstr "티크 마감의 베이리프 원목 퀸 사이즈 서랍 수납 침대."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Be yourself, create your own design"
msgstr "나만의 디자인을 만들어 보세요"

#. module: furniture_store
#: model:product.public.category,name:furniture_store.product_public_category_1
#: model:product.template,name:furniture_store.product_template_10
msgid "Bed"
msgstr "침대"

#. module: furniture_store
#: model:pos.category,name:furniture_store.pos_category_3
msgid "Beds"
msgstr "침대"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Bills of material : Kit"
msgstr "자재명세서: 키트"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_8
msgid "Black"
msgstr "검정색"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_2
msgid "Blue"
msgstr "파란색"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_13
msgid "Brown"
msgstr "갈색"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Business Flows"
msgstr "업무 흐름"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_3
msgid "Cafeteria Chair"
msgstr "식당 의자"

#. module: furniture_store
#: model:pos.payment.method,name:furniture_store.pos_payment_method_1
msgid "Cash"
msgstr "현금"

#. module: furniture_store
#: model:account.journal,name:furniture_store.cash
msgid "Cash (Furniture)"
msgstr "현금 (가구)"

#. module: furniture_store
#: model:product.public.category,name:furniture_store.product_public_category_2
msgid "Chair"
msgstr "의자"

#. module: furniture_store
#: model:pos.category,name:furniture_store.pos_category_4
msgid "Chairs"
msgstr "의자"

#. module: furniture_store
#: model:product.attribute,name:furniture_store.product_attribute_2
msgid "Color"
msgstr "색상"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Create a <font style=\"color: rgb(0, 0, 255);\"><strong>Bills of material "
"</strong></font><font class=\"text-black\">with product name as set of "
"product &amp; </font>"
msgstr ""
"<font class=\"text-black\">제품 세트로 된 제품명을 사용하여</font> <font style=\"color: "
"rgb(0, 0, 255);\"><strong>자재명세서</strong></font>를 생성합니다. &amp;"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Create a sales order  &amp;amp; confirm"
msgstr "판매주문서 생성 및 확정"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_9
msgid "Cupboard"
msgstr "찬장"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_9
msgid "Custom"
msgstr "사용자 정의"

#. module: furniture_store
#: model:pos.payment.method,name:furniture_store.pos_payment_method_2
msgid "Customer Account"
msgstr "고객 계정"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Customer can buy products in a bundle (eg. sofa set)<br/>\n"
"            <br/>\n"
"            To do that,"
msgstr ""
"고객은 번들로 제품을 구매할 수 있습니다 (예: 소파 세트) <br/>\n"
"            <br/>\n"
"            이렇게 구매하려면,"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Deliver the product"
msgstr "제품 배송"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_5
msgid "Dining Table"
msgstr "식탁"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Discover"
msgstr "알아보기"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Discover it"
msgstr "확인해 보세요."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "Discover more"
msgstr "더 알아보기"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Discover the new designs."
msgstr "새로운 디자인을 살펴보세요."

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_15
msgid "Double door"
msgstr "이중문"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Flow 1: Website - &amp;gt; CRM - &amp;gt; Quotation &amp;"
msgstr "흐름 1: 웹사이트 - &amp;gt; CRM - &amp;gt; 견적 &amp;"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Flow 2: Selling through Point of Sale."
msgstr "흐름 2: POS를 통한 판매"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Flow 3 : Order for customized product"
msgstr "흐름 3: 맞춤형 제품 주문"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Flow 4 : Sell Product in KIT"
msgstr "흐름 4: KIT 제품 판매"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves to keep his hands full by participating in the development of the magazine and does not hesitate to\n"
"                                                        carry out field surveys."
msgstr ""
"창립자이자 수석 비전가인 Tony는 회사의 원동력입니다.그는 잡지 개발에 적극적으로 참여하는 것을 즐기며\n"
"                                                         현장 조사를 하는 데도 주저함이 없습니다."

#. module: furniture_store
#: model:product.attribute,name:furniture_store.product_attribute_3
msgid "Frame"
msgstr "프레임"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"From the CRM, schedule a meeting. As you meet the client, you can<strong> </strong><font style=\"color: rgb(0, 0, 255);\"><strong>create a quotation</strong></font><strong> &amp; </strong>and use the\n"
"            <strong>product <font style=\"color: rgb(0, 0, 255);\">configurator</font> &amp; </strong> &amp; for selecting size, color etc. Also use the &amp; <strong><font style=\"color: rgb(0, 0, 255);\">product catalog</font> </strong>to add swag."
msgstr ""
"CRM에서 미팅을 예약합니다. 고객과의 미팅 중에<strong> </strong><font style=\"color: rgb(0, 0, 255);\"><strong>견적을 생성</strong></font><strong> &amp; </strong>하고\n"
"            <strong>제품 <font style=\"color: rgb(0, 0, 255);\">설정</font> &amp; </strong> &amp;을 사용하여 크기, 색상 등을 선택할 수 있습니다.또한 &amp; <strong><font style=\"color: rgb(0, 0, 255);\">제품 카탈로그</font> </strong>를 사용하여 장식을 추가할 수도 있습니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "From the website:"
msgstr "웹사이트에서:"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Furniture Store"
msgstr "가구 매장"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Get Free Quote"
msgstr "무료 견적 받기"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"Go off beaten track and discover unexpected and curious places. Here’s our "
"ultimate guide to the best-kept secret spots that you simply must visit!"
msgstr ""
"정해진 길을 벗어나서 예상하지 못한 호기심 가득한 장소를 발견해 보세요. 반드시 방문해야만 하는 최고의 비밀 명소에 대한 궁극의 가이드를"
" 선사해 드립니다!"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"Iris, with her international experience, helps us easily understand global politics. She is determined to drive success and delivers her professional acumen to bring the magazine to\n"
"                                                        the next level."
msgstr ""
"국제적 전문성을 갖춘 아이리스는 세계 정치에 대한 명확한 통찰력을 제공합니다. 성공을 향한 그녀의 결단력과 전문적인 통찰력은 잡지를 새로운 차원으로 끌어올리는 데\n"
"                                                        핵심적인 역할을 합니다."

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_11
msgid "King size"
msgstr "킹 사이즈"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "Learn more"
msgstr "추가 정보"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_4
msgid "Medium"
msgstr "중간"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"Mich loves taking on challenges. With his multi-year experience as a "
"journalist, he wanted to join a team that puts people at the center of the "
"stories."
msgstr ""
"마이클은 도전하는 것을 좋아합니다. 다년 간의 저널리스트 경험을 바탕으로, 이야기의 중심에 사람이 있는 팀에 합류하고 싶었습니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Multi-routes : MTO"
msgstr "다중 경로: MTO"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_17
msgid "Office Chair"
msgstr "사무용 의자"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Pay the order via payment method. &amp;"
msgstr "결제 수단을 통해 주문을 결제합니다. &amp;"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Product variants"
msgstr "제품 유형"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_12
msgid "Queen size"
msgstr "퀸 사이즈"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Read more"
msgstr "더 읽기"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Receive the product by clicking on the<font style=\"color: rgb(0, 0, "
"255);\"><strong> Purchase smart button</strong></font>"
msgstr ""
"<font style=\"color: rgb(0, 0, 255);\"><strong>구매 스마트 버튼</strong></font>을 "
"클릭하여 제품 수령"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_3
msgid "Red"
msgstr "빨간색"

#. module: furniture_store
#: model:product.pricelist,name:furniture_store.product_pricelist_2
msgid "Regular Customer Pricelist"
msgstr "일반 고객 요금표"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "Section Subtitle"
msgstr "영역 소제목"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Select BOM type as a<font style=\"color: rgb(0, 0, 255);\"><strong> "
"KIT</strong></font>"
msgstr ""
"BOM 유형을 <font style=\"color: rgb(0, 0, 255);\"><strong>KIT</strong></font>로 "
"선택"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Select the customer"
msgstr "고객 선택하기"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Set as<font style=\"color: rgb(0, 0, 255);\"> &amp; </font><strong "
"style=\"font-weight: 500;\"><font style=\"color: rgb(0, 0, 255);\">mark "
"\"Ship Later\"</font></strong>"
msgstr ""
"<font style=\"color: rgb(0, 0, 255);\"> &amp; </font><strong style=\"font-"
"weight: 500;\"><font style=\"color: rgb(0, 0, 255);\">\"나중에 "
"배송\"</font></strong>으로 설정 후 표시"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Set the customer to define the &amp; <font style=\"color: rgb(0, 0, "
"255);\"><strong>shipping address</strong></font>"
msgstr ""
"고객이 <font style=\"color: rgb(0, 0, 255);\"><strong>배송지 주소</strong></font>를 "
"지정하도록 설정"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"Show the &amp; \n"
"                <font>\n"
"                    <font color=\"#0000ff\"><b>stock </b></font><font class=\"text-o-color-5\" color=\"#0000ff\">availability &amp;  to customer.</font>\n"
"                </font>"
msgstr ""
"고객이 &amp; \n"
"                <font>\n"
"                    <font color=\"#0000ff\"><b>재고 </b></font><font class=\"text-o-color-5\" color=\"#0000ff\">현황을 &amp;  확인할 수 있습니다.</font>\n"
"                </font>"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_14
msgid "Single door"
msgstr "싱글 도어"

#. module: furniture_store
#: model:product.attribute,name:furniture_store.product_attribute_4
msgid "Size"
msgstr "사이즈"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_5
msgid "Small"
msgstr "작게"

#. module: furniture_store
#: model:product.public.category,name:furniture_store.product_public_category_3
msgid "Sofa"
msgstr "소파"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_13
msgid "Sofa (Double seat)"
msgstr "소파 (더블 좌석)"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_12
msgid "Sofa (Single seat)"
msgstr "소파 (싱글 좌석)"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_14
msgid "Sofa (Triple seat)"
msgstr "소파 (3인 좌석)"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_15
msgid "Sofa Set (3+1+1)"
msgstr "소파 세트 (3+1+1)"

#. module: furniture_store
#: model:product.template,name:furniture_store.product_template_16
msgid "Sofa Set (3+2)"
msgstr "소파 세트 (3+2)"

#. module: furniture_store
#: model:pos.category,name:furniture_store.pos_category_1
msgid "Sofas"
msgstr "소파"

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_10
msgid "Standard"
msgstr "표준"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid "Start with the customer – find out what they want and give it to them."
msgstr "고객과 함께 시작하세요. – 고객이 원하는 것이 무엇인지 알아보고 고객에게 제공하십시오."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "Summer Essentials"
msgstr "여름 필수품"

#. module: furniture_store
#: model:product.public.category,name:furniture_store.product_public_category_4
msgid "Table"
msgstr "테이블"

#. module: furniture_store
#: model:pos.category,name:furniture_store.pos_category_2
msgid "Tables"
msgstr "표"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid ""
"The right summer essentials can make the difference between a good time and "
"a stand-out great time. Discover our selection for everyday wear during the "
"warm summer months."
msgstr ""
"딱 맞는 여름 필수품이 만들어 내는 차이를 확인해 보세요. 그냥 좋은 시간을 보낼지 아니면 잊지 못할 멋진 시간으로 만들지를 결정합니다."
" 더운 여름 기간을 위한 일상복 컬렉션을 만나 보세요."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "Then pay the order<br/>"
msgstr "그런 다음 주문을 결제하세요<br/>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"This customized form will create an opportunity in the CRM, with the right "
"information, and the requirement."
msgstr "이 사용자 지정 양식을 통해 알맞은 정보와 요구 사항을 적용하여 CRM에 영업기회를 생성합니다."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid ""
"This setup is for furniture stores selling furniture like chairs, sofas, dining tables, Beds, Cupboards etc. &amp; <br/>\n"
"            <br/>\n"
"            They use the Point of Sale application, Inventory, Sales, Purchase, Invoice, &amp; Contact and &amp; Dashboard. &amp; In addition, the company runs an eCommerce, for individual buyers to buy products online. Furniture &amp; are defined\n"
"            with multiple variants, like size, frame and color.<br/>"
msgstr ""
"이 설정은 의자, 소파, 식탁, 침대, 찬장 등과 같은 품목을 판매하는 가구점을 위해 설계되었습니다. &amp; <br/>\n"
"            <br/>\n"
"            재고, 판매, 구매, 송장, 연락처 및 대시보드 기능과 함께 POS 애플리케이션을 활용합니다. 또한 이 회사는 이커머스 플랫폼을 운영하여 개인 고객이 온라인으로 제품을 구매할 수 있도록 지원합니다. 가구 품목은 크기, 프레임, 색상 등 여러 가지\n"
"            변형으로 정의됩니다.<br/>"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "To do that, &amp;"
msgstr "이렇게 진행하려면, &amp;"

#. module: furniture_store
#: model:product.attribute,name:furniture_store.product_attribute_5
msgid "Type"
msgstr "유형"

#. module: furniture_store
#: model_terms:web_tour.tour,rainbow_man_message:furniture_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.homepage
msgid "World's top Hidden gems"
msgstr "세계 최고의 감추어진 보물"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.website_page_aboutus
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr "제품이나 서비스에 대한 설명을 한두 개의 단락으로 작성하십시오. 방문자에게 유용한 콘텐츠일수록 성공 확률이 높아집니다."

#. module: furniture_store
#: model:product.attribute.value,name:furniture_store.product_attribute_value_1
msgid "Yellow"
msgstr "노랑"

#. module: furniture_store
#: model_terms:ir.ui.view,arch_db:furniture_store.welcome_article_body
msgid "​Setup"
msgstr "설정"
