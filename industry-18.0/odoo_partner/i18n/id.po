# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* odoo_partner
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> Many<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:02+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_19
msgid "10 analysis workshops"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<span class=\"display-4-fs\"><strong>Odoo Partners</strong></span>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong> 7. Provide excellent support: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>1. Use the CRM religiously: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>2. Leverage quote templates: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>3. Standardize project management: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>4. Encourage document usage: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>5. Track time accurately: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>6. Plan resources effectively: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong>8. Stay on top of invoicing: </strong>"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_2
msgid "Analyse"
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_16
msgid "Analysis"
msgstr "Analisis"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_20
msgid "Analysis, estimation & report preparation"
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_15
msgid "Backlog"
msgstr "Backlog"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Basics"
msgstr "Basic"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Become a partner"
msgstr ""

#. module: odoo_partner
#: model:product.template,name:odoo_partner.product_product_5_product_template
msgid "Business Analyst"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "CRM 🎯"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Calculate the recommanded Success Packs for any implementation in a few click using our Project Estimator. To try it, create a new Quotation with the Template \"Success Pack\". Open the Quote Calculator selecting the smart\n"
"                    button and try it!"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Collaborate with your team and clients"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_22
msgid "Configuration and preparation of the project"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Create a library of professional templates to speed up your sales process."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Create a stunning Website to showcase your Odoo expertise and attract new "
"clients."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Create beautiful and comprehensive quotes with the PDF Quote Builder"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Create project templates for common implementation types"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_7
msgid "Custom development"
msgstr ""

#. module: odoo_partner
#: model:project.tags,name:odoo_partner.project_tags_3
msgid "Customer Projects"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_10
msgid ""
"Data mapping and transformation needs\n"
"Imports and validation"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_9
msgid "Data transformation & adaptations"
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_18
msgid "Deployed"
msgstr "Deployed"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_4
msgid "Deployment"
msgstr ""

#. module: odoo_partner
#: model:product.template,name:odoo_partner.product_product_6_product_template
msgid "Developer"
msgstr "Developer"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Documents 📁"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Don't forget to \"Save in SO\" after you've setup the estimator, it will "
"automatically update your quotation."
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_14
#: model:project.task.type,name:odoo_partner.project_task_type_28
msgid "Done"
msgstr "Selesai"

#. module: odoo_partner
#: model:product.pricelist,name:odoo_partner.product_pricelist_2
msgid "Europe"
msgstr "Eropa"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Follow up on inquiries and proposals"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Implement the Marketing Automation app to nurture leads and keep clients "
"engaged."
msgstr ""

#. module: odoo_partner
#: model:project.project,name:odoo_partner.project_project_4
#: model:project.task.type,name:odoo_partner.project_task_type_17
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_3
msgid "Implementation"
msgstr "Implementasi"

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_2
msgid "Inbox"
msgstr "Kotak Pesan"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_8
msgid "Installation, installation rights and configuration"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Keep client requirements, project plans, and technical documentation in one "
"place"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Keep your pipeline up-to-date and set reminders for follow-ups."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Leverage the Sales app to create professional quotes and manage your sales "
"process:"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Manage your sales pipeline"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Manage your team's skills and certifications with the Employees and Skills "
"Management apps."
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_5
msgid "Needs analysis and stakeholders onboarding"
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_12
msgid "New"
msgstr "Baru"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Odoo offers you even more possibilities to enhance your services business:"
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_13
#: model:project.task.type,name:odoo_partner.project_task_type_27
msgid "On-going"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Organize Odoo-related events and webinars using the Events app."
msgstr ""

#. module: odoo_partner
#: model:appointment.type,name:odoo_partner.appointment_type_2
msgid "Personal Meeting"
msgstr "Meeting Pribadi"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_13
msgid "Preparation"
msgstr "Persiapan"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_15
msgid "Presentation"
msgstr "Presentasi"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_21
msgid "Presentation of results"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_11
msgid "Project Management"
msgstr "Manajemen Project"

#. module: odoo_partner
#: model:product.template,name:odoo_partner.product_product_4_product_template
msgid "Project Manager"
msgstr "Manajer Proyek"

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_6
msgid "Project configuration and follow up"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Projects 📊"
msgstr ""

#. module: odoo_partner
#: model:project.project,name:odoo_partner.project_project_6
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_14
msgid "ROI Analysis"
msgstr ""

#. module: odoo_partner
#: model:product.template,name:odoo_partner.product_product_7_product_template
msgid "ROI Analysis Setup"
msgstr ""

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_16
msgid "ROI Kick Off Call"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Reach us"
msgstr "Hubungi kami"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Regularly review and send invoices to maintain healthy cash flow."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Remember that we have a whole team dedicated to your success. You want to "
"become an Odoo Partner ?"
msgstr ""

#. module: odoo_partner
#: model:project.project,name:odoo_partner.project_project_1
msgid "S00006 - ROI Analysis"
msgstr ""

#. module: odoo_partner
#: model:project.project,name:odoo_partner.project_project_2
msgid "S00007 - Implementation"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Sales and Quote Management 💼"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Share documents securely with your team and clients"
msgstr ""

#. module: odoo_partner
#: model:product.pricelist,name:odoo_partner.product_pricelist_4
msgid "South America"
msgstr "South America"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Store and organize important files in the Documents app:"
msgstr ""

#. module: odoo_partner
#: model:product.template,name:odoo_partner.product_product_3_product_template
msgid "Success Pack"
msgstr ""

#. module: odoo_partner
#: model:project.project,name:odoo_partner.project_project_3
msgid "Success Packs"
msgstr ""

#. module: odoo_partner
#: model:project.project,label_tasks:odoo_partner.project_project_1
#: model:project.project,label_tasks:odoo_partner.project_project_2
#: model:project.project,label_tasks:odoo_partner.project_project_3
#: model:project.project,label_tasks:odoo_partner.project_project_4
#: model:project.project,label_tasks:odoo_partner.project_project_6
msgid "Tasks"
msgstr "Kegiatan"

#. module: odoo_partner
#: model:project.tags,name:odoo_partner.project_tags_1
msgid "Template"
msgstr "Template"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"These additional features are included in your current subscription. Feel "
"free to explore and expand your Odoo partnership capabilities!"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"This guide will gives you a very basic overview of what you can do with this"
" precise industry. Not how to became an Odoo Partner. Reach our team if you "
"want to start the venture with us."
msgstr ""

#. module: odoo_partner
#: model:project.task.type,name:odoo_partner.project_task_type_26
msgid "To do"
msgstr "Todo"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Track potential clients and ongoing projects"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Track tasks, milestones, and deadlines"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Track your sales orders and revenue"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Train your team to store and retrieve documents efficiently."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Use project templates to ensure consistency across implementations."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Use tags and folders for easy retrieval"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Use the CRM app to manage your leads and opportunities:"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Use the Helpdesk app to manage client inquiries and track satisfaction "
"levels."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "Use the Quote Calculator to pre-populate your pricing and offers."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Use the Timesheet app to monitor project profitability and resource "
"allocation."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Use the eLearning platform to offer online Odoo training courses to your "
"clients."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Utilize the Planning app to manage your team's workload and availability."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Utilize the Project app to manage Success Packs and large implementations:"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Utilize the Survey app to gather client feedback and improve your services."
msgstr ""

#. module: odoo_partner
#: model:appointment.type,name:odoo_partner.appointment_type_3
msgid "Video Call"
msgstr "Video Call"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Want to create stunning Success Pack quotes in a few click? Use the PDF Quote Builder to select the relevant pages you want to integrate. As you can see, we have already prepared a bunch of great Customer Success Stories you can\n"
"                    share to promote Odoo."
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid ""
"Welcome to your Odoo Partners package! This guide will help you navigate the"
" key features and optimize your Odoo partnership business."
msgstr ""

#. module: odoo_partner
#: model_terms:web_tour.tour,rainbow_man_message:odoo_partner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welcome! Happy exploring."

#. module: odoo_partner
#: model:sale.order.template.line,name:odoo_partner.sale_order_template_line_18
msgid "Workshops preparation"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "🎓 Documents"
msgstr "🎓 Dokume"

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "🎓 Projects"
msgstr ""

#. module: odoo_partner
#: model_terms:ir.ui.view,arch_db:odoo_partner.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Sales"
