# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* custom_furniture
# 
# Translators:
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:37+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"*This list will, of course, depend on your installed apps and configuration."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
", and feel free\n"
"                        to"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "1. Opportunities &amp; CRM 🎯"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "2. Products &amp; services 🪑"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "3. Quotations &amp; sales 💰"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_24_product_template
msgid "3/8 Hex Head Bolts"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_28_product_template
msgid "38x38 - 270 pine"
msgstr ""

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_3
msgid "3D Designer"
msgstr ""

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_1
#: model:project.project,name:custom_furniture.project_project_1
msgid "3D Renderings"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "4. Purchases &amp; inventory 📦"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "5. Project &amp; Field Services 🦺"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "6. Manufacture &amp; Quality ✅"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "7. Planning &amp; resources 🗓️"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "8. Invoicing 🧾"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further? 🚀</strong></span>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<span style=\"font-weight: normal;\">🎓 Reordering rules</span>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong><span class=\"h1-fs\" style=\"font-size: 3rem;\">Custom Furniture "
"Production</span></strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h1-fs\">Use case:</span></strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h4-fs\">Goods</span></strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h4-fs\">Services</span></strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets, subcontracted</strong> (Design presentation "
"&amp; revisions, this config will create a new Purchase Order to buy this "
"prestation and create a task in the dedicated project)."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets</strong> (Concept Development and 3D renderings,"
" this one will create a specific task in the existing \"3D Renderings\" "
"Project)"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets</strong> - Field services (Delivery and "
"installation &amp; Electrical and lighting installation) will automatically "
"create a task in the Field Service App."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Components </strong>that will serve to create manufactured products."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Consumable goods:</strong> These will not be tracked (Such as bolts,"
" finish painting, and sandpaper)."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Jane Design</strong>: She is your subcontractor for Design "
"presentations and revisions."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Manufactured Goods </strong>will be manufactured based on a Bill of "
"Material."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Chairs Vendor</strong>: Will provide you Conference chairs to "
"complete your order."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Metal Vendor</strong>: Will provide the Conference Table Metal "
"Legs you need to manufacture the conference table."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Rug Vendor</strong>: Will provide you the custom area rug in "
"sufficient square meters."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Wood Vendor</strong>: Will send you the Oak Wood Panels you need "
"to create the tables."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Prepaid services, fixed price</strong> (Initial design consultation "
"&amp; Final Inspection. Note that the first one will also create a new "
"project for each confirmed order to ensure Project follow-up)."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Purchased Goods</strong> that will be purchased and sold to your "
"customers."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"A Quality Check Point has been set for each product. It requires your worker"
" or the Quality Team to take a picture of any manufactured item. Use any "
"image to pass the check for this demo."
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_7_product_template
msgid "Accent chairs"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"All these components will give you an accurate overview on the project "
"profitability."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Analytic Items ➡️ Track any cost using Analytic Items from the Accounting "
"App"
msgstr ""

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_2
msgid "Architect"
msgstr ""

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_17
msgid "Backlog"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Basics:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Before generating an intermediary invoice in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Invoicing</font>\n"
"                </strong>\n"
"                app, freeze the project status in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Project</font>\n"
"                </strong>\n"
"                app to get an overview of the progress in terms of:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Bills of Materials"
msgstr "Λογαριασμός των Υλικών (ΒοΜ)"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_1
msgid "Black"
msgstr "Μαύρο"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_3
msgid "Blue"
msgstr "Μπλε"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"By default, there is a filter on \"Sales\" products; remove it from the "
"search bar to see all products available."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"complete flow from the opportunity to delivery and some demo data to execute"
" the flows described hereunder."
msgstr ""

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_1
msgid "Concept & Creation"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_3_product_template
msgid "Concept development and 3D renderings"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_19
msgid "Conference Table Metal Legs"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_9_product_template
msgid "Conference chairs"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Confirm the quotation. Your top bar shows that it creates different tasks in different projects, an upcoming delivery, a purchase, and various hours to plan. These are all automated actions based on your product configuration.\n"
"                These will be detailed in the following topics."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create a down payment from the sales order as the first invoicing step. This will generate the first invoice in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Invoicing</font>\n"
"                </strong>\n"
"                app. Then, schedule one or several \"Invoicing Schedule\" activities on the sales order so you never forget to send an intermediary invoice to sustain your cash flow."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create a stunning <strong><font class=\"text-o-color-1\">website</font></strong> and start receiving orders directly in Odoo. Go live with your <strong><font class=\"text-o-color-1\">E-shop</font></strong> in a few\n"
"                        minutes."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create an opportunity each time you face a potential customer, receive a "
"phone call, etc."
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_12_product_template
msgid "Custom area rug"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_8
msgid "Custom conference table"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_10_product_template
msgid "Custom dresser"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_5_product_template
msgid "Custom reception desk"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_11_product_template
msgid "Custom wall shelving unit"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Dashboard ➡️ Your project's status at a glance."
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_13_product_template
msgid "Delivery and installation"
msgstr ""

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_4
#: model:project.project,name:custom_furniture.project_project_4
msgid "Design Subcontracting"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_4_product_template
msgid "Design presentation and revisions"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Do you want to go further? 🚀"
msgstr ""

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_21
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_14_product_template
msgid "Electrical and lighting installation"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Ensure a frictionless manufacturing process by accessing the Manufacture "
"App."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Ensure a perfect follow-up of your projects with <strong><font class=\"text-"
"o-color-1\">Project</font></strong> &amp; <strong><font class=\"text-o-"
"color-1\">Field Services Apps</font></strong>."
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_6_product_template
msgid "Executive chairs"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Explore our documentation to ensure the correct configuration for each "
"product, and do not hesitate to duplicate and edit an existing product."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Fasten your <strong><font class=\"text-o-"
"color-1\">Purchases</font></strong>. Keep track of your inventory, and "
"follow your deliveries in the <strong><font class=\"text-o-"
"color-1\">Inventory App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_3
msgid "Field Service"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_15_product_template
msgid "Final inspection and client walkthrough"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Follow your equipment status and maintenance with the <strong><font "
"class=\"text-o-color-1\">Maintenance App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Follow your planning and keep track of worked time in the <strong><font "
"class=\"text-o-color-1\">Planning &amp; Timesheets Apps</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"For this use case, open the David Patel quotation. You can see a complete "
"setup using all the products mentioned in the previous topic."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "From WH ➡️ Deliveries from your warehouse to this specific customer"
msgstr ""

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_3
msgid "Furnitures"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Go to the Shop Floor App and validate the first step of your production by "
"clicking on it. Continue with the other steps until you can register your "
"production."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Hello there!"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you do not have a specific installation task or want to deliver it "
"separately, you can do it in the Inventory App."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you usually structure your quotations similarly, use the Quotation "
"Template feature. It will allow you to configure a set of products and "
"sections that will always be imported when you select it."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you want to remove this part of your process, you can use reordering "
"rules. These will ensure a fluid replenishment based on minimum quantities "
"of each product or based on orders."
msgstr ""

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_19
msgid "In progress"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"In the Purchase Apps, you can see all your Requests For Quotation, which are"
" ready to be sent to your providers. When you have confirmation that your "
"order has been placed, confirm each RFQ to continue the process."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"In this App, you can see that the delivery is not yet ready to be shipped, as you currently don't have the necessary products. If you navigate to Operations &gt; Replenishment, you will find the list of suggested purchase/manufacture\n"
"                orders missing to complete this order. Select everything, and click on \"Replenish\" &gt; \"Order.\""
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_2_product_template
msgid "Initial design consultation and space assessment"
msgstr ""

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_5
msgid "Installation"
msgstr ""

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_1
msgid "Installer"
msgstr ""

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_6
msgid "Invisible"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Invoice at the right time with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Level up your Manufacture process with the <strong><font class=\"text-o-"
"color-1\">Manufacture App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">Email Marketing</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, and <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your employees and all your human resources using the <strong><font "
"class=\"text-o-color-1\">HR Suite.</font></strong>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your entire projects with the Project App. You can see that your Sale Order has created a Project with a bunch of already configured tasks. Follow the evolution of your project with tasks, stages, status, and\n"
"                activities."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Managing your products and their characteristics is critical in Odoo. This "
"package includes several configs, which you can explore by navigating to the"
" Sales App -&gt; Products."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Manufacturing Orders"
msgstr "Εντολές Παραγωγής"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Materials purchased."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Materials sold."
msgstr ""

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_5
msgid "Natural"
msgstr ""

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_1
#: model:project.task.type,name:custom_furniture.project_task_type_15
msgid "New"
msgstr "Νέα"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_33_product_template
msgid "Oak Wood Panel 120x160"
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_18_product_template
msgid "Oak Wood Panel 320x160"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Odoo is also integrated into an App. You may want your worker to complete "
"their reports and follow the task on their mobile phone!"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"On your dashboard, you can see 1 order \"To Launch\" in the Core Board "
"Composing Machine. Open it to start logging your manufacturing process."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Once all your orders are confirmed, you are ready to receive them in the "
"Inventory App. Access it and select the 4 to process button in the Receipts "
"category. You can now validate the reception of all your orders."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Once your products are correctly set up, creating perfect quotations is "
"extremely easy. Access the Sales App and click the New button to create a "
"new quotation. Select your customer and start adding your products."
msgstr ""

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_27_product_template
msgid "Orbital Sander 20x20"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_4
msgid "Others"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Planning"
msgstr "Σχεδιασμός"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_21
msgid "Polyurethane Finish 10L"
msgstr ""

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_2
msgid "Production"
msgstr "Παραγωγή"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Provide an excellent after-sales service using the <strong><font "
"class=\"text-o-color-1\">Helpdesk App</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Purchase Orders"
msgstr "Παραγγελίες Αγοράς"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_4
msgid "Purple"
msgstr "Μωβ"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Record your employees' time spent on each project using the Timesheets app. "
"On the employee form, remember to define an hourly cost."
msgstr ""

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_2
msgid "Red"
msgstr "Κόκκινο"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Redirect the emails sent to"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Sales Orders"
msgstr "Παραγγελίες"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Suppose you navigate to the Field Service App. You can also find service tasks like Delivery and Installation or Electrical and lighting installation. This will allow your workers to log time, pick products, and keep track of the tasks\n"
"                directly from there."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Take advantage of every opportunity. The <strong> <font class=\"text-o-color-1\">CRM</font> </strong> centralizes everything and allows you to ensure the efficient follow-up of each request. If the request becomes concrete, create\n"
"                the customer in the <strong> <font class=\"text-o-color-1\">Contact</font> </strong> app."
msgstr ""

#. module: custom_furniture
#: model:project.project,label_tasks:custom_furniture.project_project_1
#: model:project.project,label_tasks:custom_furniture.project_project_4
#: model:project.project,label_tasks:custom_furniture.project_project_6
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Tasks"
msgstr "Εργασίες"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"> <strong>CRM</strong> </font> application"
" allows you to collect requests from your potential customers by aggregating"
" several sources:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"The Manufacture App comes with the Shop Floor app. This will provide a "
"specific view for your worker to keep track of any task in a particular Work"
" Center."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"There are 4 different kinds of services available in this configuration:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "This will create two purchase orders and a manufacturing order."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Time spent on the project."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Timesheets"
msgstr "Κάρτες Χρόνου"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "To WH ➡️ Products sent back to your warehouse"
msgstr ""

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_18
msgid "To do"
msgstr "Να κάνω"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"To manage your inventory and everything you need to complete the order, "
"navigate to the Inventory App."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Use case:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">CRM</font></strong> and the "
"<strong><font class=\"text-o-color-1\">Sales Apps </font></strong>to manage "
"your customer relations, your opportunities and create stunning quotations."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Website App</font></strong> "
"to create a contact form that will redirect all the requests to the <strong>"
" <font class=\"text-o-color-1\">CRM</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the Planning in the Field Service <strong><font class=\"text-o-color-1\">​</font> </strong> app to organize the work of your employees. Define which project and when they have to work. Easily manage shift conflicts, workload, and\n"
"                replacements in case of leave."
msgstr ""

#. module: custom_furniture
#: model_terms:web_tour.tour,rainbow_man_message:custom_furniture.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"When you are done with your production, you can finally deliver the complete order to your customer. To do so, navigate to the Field Service Apps. From the Installation task, you can see 31 Products that require pick-up. Click\n"
"                on the smart button and validate this picking."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can adapt your overview for each project by selecting the small "
"\"Sliders\" icon at the top right of your screen. It will allow you to add a"
" bunch of practical views to get the complete overview of your project, "
"including* :"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can add other views with specific filters and ease your project follow-"
"up."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "You can also find 4 different kinds of goods:"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can see a new Manufacture order has been posted by navigating into "
"Operations &gt; Manufacture Orders."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it if you like. Go to Apps "
"&gt; Industries &gt; Upgrade your Custom Furniture Production package and "
"check the related box."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You just installed the Odoo package for Custom Furniture Production "
"companies. By doing so, we have installed many necessary apps to run your "
"company efficiently."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various actions you can quickly execute with this package using Odoo."
msgstr ""

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_6
#: model:project.project,name:custom_furniture.project_project_6
msgid "[TEMPLATE] Interior Design Project"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "academy"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "and"
msgstr "και"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "documentation"
msgstr "Τεκμηρίωση"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "if you need help!<br/>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<EMAIL>"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "request a demo"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "to the <strong> <font class=\"text-o-color-1\">CRM</font></strong>."
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Activities"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 CRM"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Invoicing"
msgstr ""

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Timesheets"
msgstr ""
