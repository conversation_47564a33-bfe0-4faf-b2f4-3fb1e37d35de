# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* custom_furniture
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# W<PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:37+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"*This list will, of course, depend on your installed apps and configuration."
msgstr ""
"*Essa lista dependerá, é claro, dos aplicativos instalados e da "
"configuração."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
", and feel free\n"
"                        to"
msgstr ""
"e fique à vontade\n"
"para"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "1. Opportunities &amp; CRM 🎯"
msgstr "1. Oportunidades & CRM 🎯"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "2. Products &amp; services 🪑"
msgstr "2. Produtos e serviços 🪑"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "3. Quotations &amp; sales 💰"
msgstr "3. Cotações e Vendas 💰"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_24_product_template
msgid "3/8 Hex Head Bolts"
msgstr "Parafusos de cabeça sextavada 3/8"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_28_product_template
msgid "38x38 - 270 pine"
msgstr "38x38 - 270 pinho"

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_3
msgid "3D Designer"
msgstr "Designer 3D"

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_1
#: model:project.project,name:custom_furniture.project_project_1
msgid "3D Renderings"
msgstr "Renderizações 3D"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "4. Purchases &amp; inventory 📦"
msgstr "4. COmpras e Inventário 📦"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "5. Project &amp; Field Services 🦺"
msgstr "5. Projeto e Serviços de Campo 🦺"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "6. Manufacture &amp; Quality ✅"
msgstr "6. Fabricação e Qualidade ✅"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "7. Planning &amp; resources 🗓️"
msgstr "7. Planejamento e Recursos 🗓️"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "8. Invoicing 🧾"
msgstr "8. Faturamento 🧾"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further? 🚀</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>Quer ir mais além? 🚀</strong></span>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<span style=\"font-weight: normal;\">🎓 Reordering rules</span>"
msgstr "<span style=\"font-weight: normal;\">🎓 Regras de reposição</span>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong><span class=\"h1-fs\" style=\"font-size: 3rem;\">Custom Furniture "
"Production</span></strong>"
msgstr ""
"<strong><span class=\"h1-fs\" style=\"font-size: 3rem;\">Produção de móveis "
"sob medida</span></strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h1-fs\">Use case:</span></strong>"
msgstr "<strong><span class=\"h1-fs\">Caso de uso:</span></strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h4-fs\">Goods</span></strong>"
msgstr "<strong><span class=\"h4-fs\">Mercadorias</span></strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong><span class=\"h4-fs\">Services</span></strong>"
msgstr "<strong><span class=\"h4-fs\">Serviços</span></strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets, subcontracted</strong> (Design presentation "
"&amp; revisions, this config will create a new Purchase Order to buy this "
"prestation and create a task in the dedicated project)."
msgstr ""
"<strong>Baseado em planilhas de horas, subcontratação</strong> (Apresentação"
" e revisões do design; essa configuração criará um novo pedido de compra "
"para adquirir essa prestação e criará uma tarefa no projeto dedicado)."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets</strong> (Concept Development and 3D renderings,"
" this one will create a specific task in the existing \"3D Renderings\" "
"Project)"
msgstr ""
"<strong>Baseado em planilhas de horas</strong> (Desenvolvimento de conceito "
"e renderizações em 3D, este criará uma tarefa específica no projeto "
"\"Renderizações em 3D\" existente)"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Based on timesheets</strong> - Field services (Delivery and "
"installation &amp; Electrical and lighting installation) will automatically "
"create a task in the Field Service App."
msgstr ""
"<strong>Com base em planilhas de horas</strong> - Os serviços de campo "
"(entrega e instalação elétrica e de iluminação) criarão automaticamente uma "
"tarefa no aplicativo Serviço de Campo."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>Elementos básicos:</strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Components </strong>that will serve to create manufactured products."
msgstr ""
"<strong>Componentes </strong>que servirão para criar produtos manufaturados."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Consumable goods:</strong> These will not be tracked (Such as bolts,"
" finish painting, and sandpaper)."
msgstr ""
"<strong>Mercadorias consumíveis:</strong> Não serão monitoradas (como "
"parafusos, pintura de acabamento e lixa)."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Jane Design</strong>: She is your subcontractor for Design "
"presentations and revisions."
msgstr ""
"<strong>Jane Design</strong>: Ela é sua subcontratada para apresentações e "
"revisões de design."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Manufactured Goods </strong>will be manufactured based on a Bill of "
"Material."
msgstr ""
"<strong>Mercadorias fabricadas </strong> serão fabricadas com base em uma "
"lista de materiais."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Chairs Vendor</strong>: Will provide you Conference chairs to "
"complete your order."
msgstr ""
"<strong>Meu fornecedor de cadeiras</strong>: Fornecerá cadeiras de "
"conferência para concluir seu pedido."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Metal Vendor</strong>: Will provide the Conference Table Metal "
"Legs you need to manufacture the conference table."
msgstr ""
"<strong>Meu fornecedor de metal</strong>: Fornecerá as pernas metálicas da "
"mesa de conferência que você precisa para fabricar a mesa de conferência."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Rug Vendor</strong>: Will provide you the custom area rug in "
"sufficient square meters."
msgstr ""
"<strong>My Rug Vendor</strong>: Fornecerá o tapete personalizado com "
"metragem quadrada suficiente."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>My Wood Vendor</strong>: Will send you the Oak Wood Panels you need "
"to create the tables."
msgstr ""
"<strong>Meu fornecedor de madeira</strong>: entregará os painéis de madeira "
"de carvalho necessários para criar as mesas."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Prepaid services, fixed price</strong> (Initial design consultation "
"&amp; Final Inspection. Note that the first one will also create a new "
"project for each confirmed order to ensure Project follow-up)."
msgstr ""
"<strong>Serviços pré-pagos, preço fixo</strong> (Consulta inicial de projeto"
" e Inspeção final. Observe que o primeiro também criará um novo projeto para"
" cada pedido confirmado para garantir o acompanhamento do projeto)."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"<strong>Purchased Goods</strong> that will be purchased and sold to your "
"customers."
msgstr ""
"<strong>Mercadorias compradas</strong> que serão compradas e vendidas a seus"
" clientes."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"A Quality Check Point has been set for each product. It requires your worker"
" or the Quality Team to take a picture of any manufactured item. Use any "
"image to pass the check for this demo."
msgstr ""
"Foi definido um ponto de controle de qualidade para cada produto. Isso exige"
" que o funcionário ou a equipe de qualidade tire uma foto de qualquer item "
"fabricado. Use qualquer imagem para passar na verificação desta "
"demonstração."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_7_product_template
msgid "Accent chairs"
msgstr "Poltronas decorativas"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"All these components will give you an accurate overview on the project "
"profitability."
msgstr ""
"Todos esses componentes fornecerão uma visão geral precisa da rentabilidade "
"do projeto."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Analytic Items ➡️ Track any cost using Analytic Items from the Accounting "
"App"
msgstr ""
"Itens analíticos ➡️ Rastreie qualquer custo usando itens analíticos no "
"aplicativo Financeiro"

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_2
msgid "Architect"
msgstr "Arquiteto"

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_17
msgid "Backlog"
msgstr "Lista de pendências"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Basics:"
msgstr "Elementos básicos:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Before generating an intermediary invoice in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Invoicing</font>\n"
"                </strong>\n"
"                app, freeze the project status in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Project</font>\n"
"                </strong>\n"
"                app to get an overview of the progress in terms of:"
msgstr ""
"Antes de gerar uma fatura intermediária no app\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Faturamento</font>\n"
"                </strong>\n"
"                congele o status do projeto no app\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Projeto</font>\n"
"                </strong>\n"
"                para obter uma visão geral do progresso em termos de:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Bills of Materials"
msgstr "Listas de materiais"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_1
msgid "Black"
msgstr "Preto"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_3
msgid "Blue"
msgstr "Azul"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"By default, there is a filter on \"Sales\" products; remove it from the "
"search bar to see all products available."
msgstr ""
"Por padrão, há um filtro nos produtos de \"Venda\"; remova-o da barra de "
"pesquisa para ver todos os produtos disponíveis."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"complete flow from the opportunity to delivery and some demo data to execute"
" the flows described hereunder."
msgstr ""
"Ao fazer upload dos dados de demonstração deste módulo, sua base de dados "
"foi preenchida com um fluxo completo, desde oportunidade até entrega, e com "
"alguns dados de demonstração para executar os fluxos descritos abaixo."

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_1
msgid "Concept & Creation"
msgstr "Conceito e Criação"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_3_product_template
msgid "Concept development and 3D renderings"
msgstr "Desenvolvimento de conceito e renderização 3D"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_19
msgid "Conference Table Metal Legs"
msgstr "Pernas de metal para mesa de reuniões"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_9_product_template
msgid "Conference chairs"
msgstr "Cadeiras para reuniões"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Confirm the quotation. Your top bar shows that it creates different tasks in different projects, an upcoming delivery, a purchase, and various hours to plan. These are all automated actions based on your product configuration.\n"
"                These will be detailed in the following topics."
msgstr ""
"Confirme a cotação. Sua barra superior mostra que foram criadas diferentes tarefas em diferentes projetos, uma entrega futura, uma compra e várias horas a planejar. Tudo isso são ações automatizadas baseadas na configuração de seu produto.\n"
"Elas serão detalhadas nos tópicos a seguir."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create a down payment from the sales order as the first invoicing step. This will generate the first invoice in the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Invoicing</font>\n"
"                </strong>\n"
"                app. Then, schedule one or several \"Invoicing Schedule\" activities on the sales order so you never forget to send an intermediary invoice to sustain your cash flow."
msgstr ""
"Crie um adiantamento a partir do pedido de vendas como a primeira etapa de faturamento. Isso gerará a primeira fatura no app\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Faturamento</font>\n"
"                </strong>\n"
"                . Em seguida, agende uma ou mais atividades de \"Cronograma de faturamento\" no pedido de venda para que você nunca se esqueça de enviar uma fatura intermediária para sustentar seu fluxo de caixa."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create a stunning <strong><font class=\"text-o-color-1\">website</font></strong> and start receiving orders directly in Odoo. Go live with your <strong><font class=\"text-o-color-1\">E-shop</font></strong> in a few\n"
"                        minutes."
msgstr ""
"Crie um <strong><font class=\"text-o-color-1\">site</font></strong> incrível"
" e comece a receber pedidos diretamente no Odoo. Comece a operar a sua "
"<strong><font class=\"text-o-color-1\">loja on-line</font></strong> em "
"instantes."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Create an opportunity each time you face a potential customer, receive a "
"phone call, etc."
msgstr ""
"Crie uma oportunidade toda vez que estiver diante de um cliente em "
"potencial, seja em ligações telefônicas ou de outro modo."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_12_product_template
msgid "Custom area rug"
msgstr "Tapete sob medida"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_8
msgid "Custom conference table"
msgstr "Mesa de reuniões sob medida"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_10_product_template
msgid "Custom dresser"
msgstr "Cômoda sob medida"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_5_product_template
msgid "Custom reception desk"
msgstr "Balcão de recepção sob medida"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_11_product_template
msgid "Custom wall shelving unit"
msgstr "Estante sob medida"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Dashboard ➡️ Your project's status at a glance."
msgstr "Painéis ➡️ Veja o status do seu projeto com um olhar."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_13_product_template
msgid "Delivery and installation"
msgstr "Entrega e instalação"

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_4
#: model:project.project,name:custom_furniture.project_project_4
msgid "Design Subcontracting"
msgstr "Subcontratação do design"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_4_product_template
msgid "Design presentation and revisions"
msgstr "Apresentação e revisões do design"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Saiba mais sobre o Odoo mergulhando em nosso"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"Conheça os aspectos básicos desse pacote e explore todas as possibilidades "
"que o Odoo oferece para melhorar sua experiência."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Do you want to go further? 🚀"
msgstr "Quer ir mais além? 🚀"

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_21
msgid "Done"
msgstr "Concluído"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_14_product_template
msgid "Electrical and lighting installation"
msgstr "Instalação elétrica e de iluminação"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Ensure a frictionless manufacturing process by accessing the Manufacture "
"App."
msgstr ""
"Garanta que seu processo de fabricação não sofra transtornos acessando o "
"aplicativo Fabricação."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Ensure a perfect follow-up of your projects with <strong><font class=\"text-"
"o-color-1\">Project</font></strong> &amp; <strong><font class=\"text-o-"
"color-1\">Field Services Apps</font></strong>."
msgstr ""
"Garanta que o acompanhamento de seus projetos seja perfeito com os "
"apps<strong><font class=\"text-o-color-1\">Projeto</font></strong> e "
"<strong><font class=\"text-o-color-1\">Serviços de Campo</font></strong>."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_6_product_template
msgid "Executive chairs"
msgstr "Cadeiras executivas"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Explore our documentation to ensure the correct configuration for each "
"product, and do not hesitate to duplicate and edit an existing product."
msgstr ""
"Explore nossa documentação para garantir a configuração correta de cada "
"produto e não hesite em duplicar e editar um produto existente."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Fasten your <strong><font class=\"text-o-"
"color-1\">Purchases</font></strong>. Keep track of your inventory, and "
"follow your deliveries in the <strong><font class=\"text-o-"
"color-1\">Inventory App</font></strong>."
msgstr ""
"Acelere suas <strong><font class=\"text-o-color-1\">Compras</font></strong>."
" Monitore seu inventário e acompanhe suas entregas no <strong><font "
"class=\"text-o-color-1\">app Inventário</font></strong>."

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_3
msgid "Field Service"
msgstr "Serviço de campo"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_15_product_template
msgid "Final inspection and client walkthrough"
msgstr "Inspeção final e apresentação ao cliente"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Follow your equipment status and maintenance with the <strong><font "
"class=\"text-o-color-1\">Maintenance App</font></strong>."
msgstr ""
"Acompanhe o status e a manutenção dos seus equipamentos com o app "
"<strong><font class=\"text-o-color-1\">Manutenção</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Follow your planning and keep track of worked time in the <strong><font "
"class=\"text-o-color-1\">Planning &amp; Timesheets Apps</font></strong>."
msgstr ""
"Acompanhe seu planejamento e controle o tempo trabalhado nos apps "
"<strong><font class=\"text-o-color-1\">Planejamento e Planilhas de "
"Horas</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"For this use case, open the David Patel quotation. You can see a complete "
"setup using all the products mentioned in the previous topic."
msgstr ""
"Para esse caso de uso, abra a cotação de David Patel. Você pode ver uma "
"configuração completa usando todos os produtos mencionados no tópico "
"anterior."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "From WH ➡️ Deliveries from your warehouse to this specific customer"
msgstr "De WH ➡️ Entregas, do seu armazém para esse cliente específico"

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_3
msgid "Furnitures"
msgstr "Móveis"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Go to the Shop Floor App and validate the first step of your production by "
"clicking on it. Continue with the other steps until you can register your "
"production."
msgstr ""
"Vá para o aplicativo Chão de Fábrica e valide a primeira etapa de sua "
"produção clicando nela. Prossiga com as outras etapas até poder registrar "
"sua produção."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Hello there!"
msgstr "Olá!"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you do not have a specific installation task or want to deliver it "
"separately, you can do it in the Inventory App."
msgstr ""
"Se você não tiver uma tarefa de instalação específica ou quiser entregá-la "
"separadamente, poderá fazer isso no aplicativo Inventário."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you usually structure your quotations similarly, use the Quotation "
"Template feature. It will allow you to configure a set of products and "
"sections that will always be imported when you select it."
msgstr ""
"Se você costuma estruturar suas cotações de forma semelhante, use o recurso "
"Modelo de Cotação. Isso permitirá que você configure um conjunto de produtos"
" e seções que sempre será importado quando você selecionar o modelo."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"Se quiser fazer um tour guiado deste módulo, importe os dados de "
"demonstração e teste Demo - Caso de Uso (no próximo Artigo de Conhecimento)."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"If you want to remove this part of your process, you can use reordering "
"rules. These will ensure a fluid replenishment based on minimum quantities "
"of each product or based on orders."
msgstr ""
"Se quiser remover essa parte do processo, você pode usar regras de "
"reposição. Isso garantirá reposições fluidas com base nas quantidades "
"mínimas de cada produto ou com base em pedidos."

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_19
msgid "In progress"
msgstr "Em andamento"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"In the Purchase Apps, you can see all your Requests For Quotation, which are"
" ready to be sent to your providers. When you have confirmation that your "
"order has been placed, confirm each RFQ to continue the process."
msgstr ""
"No aplicativo Compras, você pode ver todas as suas solicitações de cotação "
"que estão prontas para serem enviadas aos seus fornecedores. Quando você "
"tiver a confirmação de que seu pedido foi feito, confirme cada SDC para "
"continuar o processo."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"In this App, you can see that the delivery is not yet ready to be shipped, as you currently don't have the necessary products. If you navigate to Operations &gt; Replenishment, you will find the list of suggested purchase/manufacture\n"
"                orders missing to complete this order. Select everything, and click on \"Replenish\" &gt; \"Order.\""
msgstr ""
"Nesse aplicativo, é possível ver que a entrega ainda não está pronta para ser enviada, pois você não tem os produtos necessários no momento. Se você navegar até Operações > Reposição, encontrará a lista de sugestões de pedidos de compra/fabricação\n"
"que faltam para concluir esse pedido. Selecione tudo e clique em \"Reposição\" > \"Pedido\"."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_2_product_template
msgid "Initial design consultation and space assessment"
msgstr "Avaliação do espaço e consultoria de desing iniciais"

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_5
msgid "Installation"
msgstr "Instalação"

#. module: custom_furniture
#: model:planning.role,name:custom_furniture.planning_role_1
msgid "Installer"
msgstr "Instalador"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_6
msgid "Invisible"
msgstr "Invisível"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Invoice at the right time with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""
"Fature no momento certo com o app <strong><font class=\"text-o-"
"color-1\">Faturamento</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Invoices"
msgstr "Faturas"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Level up your Manufacture process with the <strong><font class=\"text-o-"
"color-1\">Manufacture App</font></strong>."
msgstr ""
"Aprimore seu processo de Fabricação com o app <strong><font class=\"text-o-"
"color-1\">Fabricação</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">Email Marketing</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, and <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""
"Gerencie todos os seus canais de comunicação em um só lugar com o Odoo <strong><font class=\"text-o-color-1\">Automação de Marketing</font></strong>, <strong><font class=\"text-o-color-1\">Marketing por e-mail</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Marketing de Redes Sociais</font></strong> e <strong><font class=\"text-o-color-1\">Marketing por SMS</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"Gerencie sua contabilidade com mais facilidade do que nunca em um ambiente "
"totalmente integrado. (<strong><font class=\"text-o-color-1\">app "
"Financeiro</font></strong>)"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your employees and all your human resources using the <strong><font "
"class=\"text-o-color-1\">HR Suite.</font></strong>"
msgstr ""
"Gerencie seus funcionários e todos os seus recursos humanos usando a "
"<strong><font class=\"text-o-color-1\">suíte de RH.</font></strong>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Manage your entire projects with the Project App. You can see that your Sale Order has created a Project with a bunch of already configured tasks. Follow the evolution of your project with tasks, stages, status, and\n"
"                activities."
msgstr ""
"Gerencie todos os seus projetos com o aplicativo Projeto. Você pode ver que "
"seu pedido de venda criou um projeto com várias tarefas já configuradas. "
"Acompanhe a evolução de seu projeto com tarefas, estágios, status e "
"atividades."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Managing your products and their characteristics is critical in Odoo. This "
"package includes several configs, which you can explore by navigating to the"
" Sales App -&gt; Products."
msgstr ""
"Gerenciar seus produtos e suas características é fundamental no Odoo. Esse "
"pacote inclui várias configurações, que você pode explorar navegando até o "
"aplicativo Vendas > Produtos."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Manufacturing Orders"
msgstr "Ordens de produção"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Materials purchased."
msgstr "Materiais adquiridos."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Materials sold."
msgstr "Materiais vendidos."

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_5
msgid "Natural"
msgstr "Natural"

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_1
#: model:project.task.type,name:custom_furniture.project_task_type_15
msgid "New"
msgstr "Novo"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_33_product_template
msgid "Oak Wood Panel 120x160"
msgstr "Painel de madeira de carvalho 120x160"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_18_product_template
msgid "Oak Wood Panel 320x160"
msgstr "Painel de madeira de carvalho 320x160"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Odoo is also integrated into an App. You may want your worker to complete "
"their reports and follow the task on their mobile phone!"
msgstr ""
"O Odoo também está integrado em um aplicativo. Talvez você queira que seu "
"funcionário preencha os relatórios e acompanhe a tarefa pelo celular!"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"O Odoo é totalmente integrado a um App. Baixe-o para transformar seu "
"telefone em um caixa adicional (e muito mais)."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "O Odoo oferece infinitas possibilidades, como:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Claro, isso é apenas uma visão geral dos recursos incluídos neste pacote. "
"Sinta-se à vontade para adicionar novos aplicativos, excluir/modificar dados"
" de demonstração e testar de tudo!"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"On your dashboard, you can see 1 order \"To Launch\" in the Core Board "
"Composing Machine. Open it to start logging your manufacturing process."
msgstr ""
"Em seu painel, você pode ver um pedido \"A ser iniciado\" na máquina de "
"composição de placa principal. Abra-a para começar a registrar seu processo "
"de fabricação."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Once all your orders are confirmed, you are ready to receive them in the "
"Inventory App. Access it and select the 4 to process button in the Receipts "
"category. You can now validate the reception of all your orders."
msgstr ""
"Depois que todos os seus pedidos forem confirmados, você estará pronto para "
"recebê-los no aplicativo Inventário. Acesse-o e selecione o botão '4 a "
"processar' em 'Recebimentos'. Agora você pode validar o recebimento de todos"
" os seus pedidos."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Once your products are correctly set up, creating perfect quotations is "
"extremely easy. Access the Sales App and click the New button to create a "
"new quotation. Select your customer and start adding your products."
msgstr ""
"Depois que seus produtos estiverem configurados corretamente, é extremamente"
" fácil criar cotações perfeitas. Acesse o aplicativo Vendas e clique no "
"botão 'Novo' para criar uma nova cotação. Selecione o cliente e comece a "
"adicionar seus produtos."

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_product_27_product_template
msgid "Orbital Sander 20x20"
msgstr "Lixadeira orbital 20x20"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""
"Organize seus eventos e conecte-se com seus clientes facilmente com "
"o<strong> <font class=\"text-o-color-1\">app Eventos</font></strong>."

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_4
msgid "Others"
msgstr "Outros"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Planning"
msgstr "Planejamento"

#. module: custom_furniture
#: model:product.template,name:custom_furniture.product_template_21
msgid "Polyurethane Finish 10L"
msgstr "Acabamento em poliuretano 10L"

#. module: custom_furniture
#: model:sale.order.template.line,name:custom_furniture.sale_order_template_line_2
msgid "Production"
msgstr "Produção"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Provide an excellent after-sales service using the <strong><font "
"class=\"text-o-color-1\">Helpdesk App</font></strong>."
msgstr ""
"Forneça um excelente serviço pós-venda usando o app <strong><font "
"class=\"text-o-color-1\">Central de Ajuda</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Purchase Orders"
msgstr "Pedidos de compra"

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_4
msgid "Purple"
msgstr "Roxo"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Reach us"
msgstr "Entre em contato"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Record your employees' time spent on each project using the Timesheets app. "
"On the employee form, remember to define an hourly cost."
msgstr ""
"Registre o tempo gasto por seus funcionários em cada projeto pelo aplicativo"
" Planilhas de Horas. No formulário do funcionário, lembre-se de definir um "
"custo por hora."

#. module: custom_furniture
#: model:product.attribute.value,name:custom_furniture.product_attribute_value_2
msgid "Red"
msgstr "Vermelho"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Redirect the emails sent to"
msgstr "Redirecionar os e-mails enviados para"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Sales Orders"
msgstr "Pedidos de venda"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Suppose you navigate to the Field Service App. You can also find service tasks like Delivery and Installation or Electrical and lighting installation. This will allow your workers to log time, pick products, and keep track of the tasks\n"
"                directly from there."
msgstr ""
"Suponha que você navegue até o aplicativo Serviço de Campo. Você também pode encontrar tarefas de serviço como Entrega e Instalação ou Instalação elétrica e de iluminação. Isso permitirá que seus funcionários registrem horas, escolham produtos e acompanhem tarefas\n"
"diretamente por lá."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Take advantage of every opportunity. The <strong> <font class=\"text-o-color-1\">CRM</font> </strong> centralizes everything and allows you to ensure the efficient follow-up of each request. If the request becomes concrete, create\n"
"                the customer in the <strong> <font class=\"text-o-color-1\">Contact</font> </strong> app."
msgstr ""
"Aproveite todas as oportunidades. O <strong> <font class=\"text-o-color-1\">CRM</font> </strong> centraliza tudo e permite que você garanta o acompanhamento de cada solicitação de forma eficiente. Se a solicitação se tornar concreta, crie o cliente\n"
"no app <strong> <font class=\"text-o-color-1\">Contatos</font> </strong>."

#. module: custom_furniture
#: model:project.project,label_tasks:custom_furniture.project_project_1
#: model:project.project,label_tasks:custom_furniture.project_project_4
#: model:project.project,label_tasks:custom_furniture.project_project_6
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Tasks"
msgstr "Tarefas"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"> <strong>CRM</strong> </font> application"
" allows you to collect requests from your potential customers by aggregating"
" several sources:"
msgstr ""
"O app <font class=\"text-o-color-1\"> <strong>CRM</strong> </font> permite "
"que você colete solicitações de seus clientes potenciais agregando várias "
"fontes:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"The Manufacture App comes with the Shop Floor app. This will provide a "
"specific view for your worker to keep track of any task in a particular Work"
" Center."
msgstr ""
"O aplicativo Fabricação vem com o aplicativo Chão de Fábrica. Isso fornecerá"
" uma visualização específica para que seu funcionário acompanhe qualquer "
"tarefa em um determinado centro de trabalho."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"There are 4 different kinds of services available in this configuration:"
msgstr ""
"Há quatro tipos diferentes de serviços disponíveis nessa configuração:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Todos são gratuitos em sua assinatura atual; sinta-se à vontade para "
"explorar! 🙃"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "This will create two purchase orders and a manufacturing order."
msgstr "Isso criará dois pedidos de compra e uma ordem de produção."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Time spent on the project."
msgstr "Tempo gasto no projeto."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Timesheets"
msgstr "Planilhas de horas"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "To WH ➡️ Products sent back to your warehouse"
msgstr "Para WH ➡️ Produtos enviados de volta ao seu depósito"

#. module: custom_furniture
#: model:project.task.type,name:custom_furniture.project_task_type_18
msgid "To do"
msgstr "A fazer"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"To manage your inventory and everything you need to complete the order, "
"navigate to the Inventory App."
msgstr ""
"Para gerenciar seu inventário e tudo o que você precisa para concluir o "
"pedido, navegue até o aplicativo Inventário."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Use case:"
msgstr "Caso de uso:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">CRM</font></strong> and the "
"<strong><font class=\"text-o-color-1\">Sales Apps </font></strong>to manage "
"your customer relations, your opportunities and create stunning quotations."
msgstr ""
"Use os apps <strong><font class=\"text-o-color-1\">CRM</font></strong> e "
"<strong><font class=\"text-o-color-1\">Vendas </font></strong>para gerenciar"
" suas relações com clientes, suas oportunidades e para criar cotações "
"impressionantes."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Website App</font></strong> "
"to create a contact form that will redirect all the requests to the <strong>"
" <font class=\"text-o-color-1\">CRM</font></strong>."
msgstr ""
"Use o app <strong><font class=\"text-o-color-1\">Site</font></strong> para "
"criar um formulário de contato que redirecionará todas as solicitações para "
"o <strong> <font class=\"text-o-color-1\">CRM</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"Use the Planning in the Field Service <strong><font class=\"text-o-color-1\">​</font> </strong> app to organize the work of your employees. Define which project and when they have to work. Easily manage shift conflicts, workload, and\n"
"                replacements in case of leave."
msgstr ""
"Use o Planejamento no app Serviço de Campo <strong><font class=\"text-o-color-1\">​</font> </strong> para organizar o trabalho de seus funcionários. Defina qual projeto e quando eles devem trabalhar. Gerencie facilmente conflitos de turnos, carga de trabalho e\n"
"substituições em caso de licença."

#. module: custom_furniture
#: model_terms:web_tour.tour,rainbow_man_message:custom_furniture.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Boas-vindas! Boa exploração."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"When you are done with your production, you can finally deliver the complete order to your customer. To do so, navigate to the Field Service Apps. From the Installation task, you can see 31 Products that require pick-up. Click\n"
"                on the smart button and validate this picking."
msgstr ""
"Quando a produção estiver concluída, você poderá finalmente entregar o pedido completo ao cliente. Para fazer isso, navegue até os app Serviço de Campo. Na tarefa Instalação, você pode ver 31 produtos que exigem retirada. Clique\n"
"no botão inteligente e valide a retirada."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Quer conversar sobre sua configuração do Odoo ou ir ainda mais além?"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Você pode acessar todos os aplicativos instalados na base de dados do Odoo "
"em seu painel principal."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can adapt your overview for each project by selecting the small "
"\"Sliders\" icon at the top right of your screen. It will allow you to add a"
" bunch of practical views to get the complete overview of your project, "
"including* :"
msgstr ""
"Você pode adaptar a visão geral de cada projeto selecionando o pequeno ícone"
" \"Sliders\" na parte superior direita da tela. Isso permitirá que você "
"adicione várias visualizações práticas para obter uma visão geral completa "
"do seu projeto, incluindo*:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can add other views with specific filters and ease your project follow-"
"up."
msgstr ""
"Você pode adicionar outras visualizações com filtros específicos e facilitar"
" o acompanhamento do seu projeto."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "You can also find 4 different kinds of goods:"
msgstr "Você também pode encontrar 4 tipos diferentes de mercadorias:"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You can see a new Manufacture order has been posted by navigating into "
"Operations &gt; Manufacture Orders."
msgstr ""
"Você pode ver que uma nova ordem de produção foi lançada navegando até "
"Operações > Ordens de produção."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Você concluiu o caso de uso de demonstração! Há milhões de outras maneiras "
"de adaptar sua configuração do Odoo para atender às suas necessidades "
"comerciais."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it if you like. Go to Apps "
"&gt; Industries &gt; Upgrade your Custom Furniture Production package and "
"check the related box."
msgstr ""
"Não importou dados de demonstração? Ainda dá para fazer isso. Vá para "
"Aplicativos > Setores > Atualize o pacote Produção de móveis sob medida e "
"marque a caixa relacionada."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You just installed the Odoo package for Custom Furniture Production "
"companies. By doing so, we have installed many necessary apps to run your "
"company efficiently."
msgstr ""
"Você acabou de instalar o pacote Odoo para empresas de produção de móveis "
"sob medida. Com isso, instalamos diversos aplicativos necessários para "
"administrar sua empresa com eficiência."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various actions you can quickly execute with this package using Odoo."
msgstr ""
"Realize os fluxos a seguir para ter uma visão geral das várias ações que é "
"possível executar rapidamente pelo Odoo com esse pacote."

#. module: custom_furniture
#: model:account.analytic.account,name:custom_furniture.account_analytic_account_6
#: model:project.project,name:custom_furniture.project_project_6
msgid "[TEMPLATE] Interior Design Project"
msgstr "[MODELO] Projeto de design de interiores"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "academy"
msgstr "academia"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "and"
msgstr "e"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "documentation"
msgstr "documentação"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "if you need help!<br/>"
msgstr "se você precisar de ajuda!<br/>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "request a demo"
msgstr "solicite uma demonstração"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "to the <strong> <font class=\"text-o-color-1\">CRM</font></strong>."
msgstr "ao <strong> <font class=\"text-o-color-1\">CRM</font></strong>."

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Activities"
msgstr "🎓 Atividades"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 Faturamento"

#. module: custom_furniture
#: model_terms:ir.ui.view,arch_db:custom_furniture.welcome_article_body
msgid "🎓 Timesheets"
msgstr "🎓 Planilhas de Horas"
