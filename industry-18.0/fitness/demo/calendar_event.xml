<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="calendar_event_1" model="calendar.event">
        <field name="name">Hit Course Appointment</field>
        <field name="appointment_type_id" ref="appointment_type_1"/>
        <field name="start" model="appointment.type" eval="
            pytz.timezone(obj().env.ref('fitness.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=9)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="stop" model="appointment.type" eval="
            pytz.timezone(obj().env.ref('fitness.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=12)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
    </record>
</odoo>
