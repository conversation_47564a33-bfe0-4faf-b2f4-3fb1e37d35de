# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fitness
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:43+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
", and feel free\n"
"                    to"
msgstr ""
", et n'hésitez pas\n"
"                    à"

#. module: fitness
#: model:product.template,name:fitness.product_product_14_product_template
#: model:project.project,name:fitness.project_project_1
msgid "20-Day Beginner Fitness Program"
msgstr "Programme de fitness pour débutants de 20 jours"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "24/7 Customer Support"
msgstr "Service Client 24/7"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "24/7 Customer Support<br/>"
msgstr "Service Client 24/7<br/>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "30-day trial, satisfaction guaranteed."
msgstr "Essai de 30 jours, satisfaction garantie."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<b><u>Add Resources</u></b>: Easily add resources like classes or coaches to"
" your system."
msgstr ""
"<b><u>Ajoutez des ressources</u></b> : ajoutez facilement des ressources "
"telles que des cours ou des entraîneurs à votre système."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<b><u>Comprehensive Scheduling</u></b>: Plan and manage schedules, allowing "
"customers to book appointments directly from your website, manage classes "
"and sessions, and track attendance."
msgstr ""
"<b><u>Une programmation complète</u></b> : Planifiez et gérez les horaires. "
"Donnez à vos clients la possibilité de prendre rendez-vous directement "
"depuis votre site web, de gérer les cours et les sessions, et de suivre la "
"fréquentation."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<b><u>Customize Availability</u></b>: Set availability for different "
"resources, ensuring that your scheduling system is always up-to-date."
msgstr ""
"<b><u>Personnalisez la disponibilité</u></b> : Définissez la disponibilité "
"des différentes ressources, en veillant à ce que votre système de "
"planification soit toujours à jour."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<b><u>Link to Courses</u></b>: Connect resources to specific courses that "
"can be either free or paid, depending on the user’s subscription status."
msgstr ""
"<b><u>Lien vers les cours</u></b> : Relier les ressources à des cours "
"spécifiques qui peuvent être gratuits ou payants, en fonction de "
"l'abonnement de l'utilisateur."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<b><u>Website Integration</u></b>: Seamlessly integrate all these features "
"into your website, providing a unified experience for your customers to book"
" appointments, view class schedules, and manage subscriptions online."
msgstr ""
"<b><u>Intégration au site web</u></b> : Intégrez toutes ces fonctionnalités "
"à votre site web, en offrant à vos clients une expérience unifiée de prise "
"de rendez-vous, d'affichage des horaires de cours et de gestion des "
"abonnements en ligne."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "<small>/ month</small>"
msgstr "<small>/ mois</small>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "<small>/month</small>"
msgstr "<small>/mois</small>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Fitness Center by Odoo</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Centre de fitness par "
"Odoo</strong></span>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<span class=\"h1-fs\">Do you want to go further?</span>"
msgstr "<span class=\"h1-fs\">Vous souhaitez aller plus loin ?</span>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>125</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>125</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>35</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>35</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>65</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                                        <span class=\"s_comparisons_price\">\n"
"                                                            <b>65</b>\n"
"                                                        </span>\n"
"                                                        <span class=\"s_comparisons_decimal\">.00</span>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Appointment booking <strong>🗓️</strong></strong>"
msgstr "<strong>Prise de rendez-vous <strong>🗓️</strong></strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Assign Personnel</strong>: Assign maintenance tasks to specific "
"employees or teams, ensuring that the right person is handling the repair."
msgstr ""
"<strong>Affectation du personnel</strong> : Attribuez des tâches d'entretien"
" à des employés ou à des équipes spécifiques, afin de vous assurer que la "
"bonne personne s'occupe de la réparation."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Automate Your Reordering Process 🚀</strong>"
msgstr "<strong>Automatisez votre processus de réapprovisionnement 🚀</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Check Stock Levels</strong>:"
msgstr "<strong>Vérifiez les niveaux de stock</strong> :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Choose Your Vendors</strong>: Select the vendors you want to reorder"
" from. You can also add bulk pricing options to save time and money."
msgstr ""
"<strong>Choisissez vos fournisseurs</strong> : Sélectionnez les fournisseurs"
" auprès desquels vous souhaitez passer commande. Vous pouvez également "
"ajouter des options de prix de gros pour gagner du temps et de l'argent."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Confirm Orders</strong>: When an order is confirmed, a delivery is automatically created in the Inventory app. This allows you to keep track of stock levels and ensure you have enough inventory for your daily\n"
"                        operations."
msgstr ""
"<strong>Confirmez les commandes</strong> : Lorsqu'une commande est confirmée, une livraison est automatiquement créée dans l'application Inventaire. Cela vous permet de suivre les niveaux de stock et de vous assurer que vous disposez d'un stock suffisant pour vos opérations \n"
"                        quotidiennes."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Create Maintenance Requests</strong>: Easily generate maintenance "
"requests when equipment needs repair or servicing. These requests can be "
"categorized and prioritized based on urgency."
msgstr ""
"<strong>Créez des demandes d'entretien</strong> : Générez facilement des "
"demandes d'entretien lorsque l'équipement a besoin d'être réparé ou "
"entretenu. Ces demandes peuvent être classées et hiérarchisées en fonction "
"de leur urgence."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Create Reordering Rules</strong>: Click on the Products menu and "
"select Reordering Rules. Here, you can set minimum and maximum quantities "
"for each product to trigger automatic reordering."
msgstr ""
"<strong>Créez des règles de réapprovisionnement</strong> : Cliquez sur le "
"menu Produits et sélectionnez Règles de réapprovisionnement. Vous pouvez y "
"définir des quantités minimales et maximales pour chaque produit afin de "
"déclencher un réapprovisionnement automatique."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Create Tailor-Made Subscriptions and Sell Products Effortlessly "
"🛒</strong>"
msgstr ""
"<strong>Créez des abonnements sur mesure et vendez des produits en toute "
"simplicité 🛒</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Define Equipment</strong>: Create detailed profiles for each piece "
"of equipment, including specifications, usage history,emplacement,  and "
"maintenance requirements."
msgstr ""
"<strong>Spécifiez l'équipement</strong> : Créez des profils détaillés pour "
"chaque équipement, comprenant les spécifications, l'historique de "
"l'utilisation, l'emplacement et les exigences en matière d'entretien."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Effortlessly manage your Point of Sale 💵</strong>"
msgstr "<strong>Gérez facilement votre Point de Vente 💵</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Effortlessly manage your eCommerce 💵</strong>"
msgstr "<strong>Gérez facilement votre eCommerce 💵</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Equipment Definition and Maintenance Requests</strong> 🛠️🔧"
msgstr ""
"<strong>Spécification de l'équipement et demandes d'entretien</strong> 🛠️🔧"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>If you want to execute a practical guided tour of this module, you "
"should Import demo data and try the features described in this "
"article.</strong>"
msgstr ""
"<strong>Pour une visite guidée pratique de ce module, nous vous conseillons "
"d'importer des données de démonstration et d'essayer les fonctionnalités "
"décrites dans cet article.</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Integration with Employee Management</strong>: Link maintenance "
"tasks to employee schedules, ensuring efficient allocation of resources."
msgstr ""
"<strong>Intégration avec la gestion des employés</strong> : Liez les tâches "
"d'entretien aux horaires des employés, afin de garantir une affectation "
"efficace des ressources.."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Navigate to the Inventory App</strong>: Go to the Inventory app in "
"Odoo."
msgstr ""
"<strong>Naviguez vers l'application Inventaire</strong> : Dans Odoo, rendez-"
"vous dans l'application Inventaire."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Resupply as Needed</strong>: If stock levels are low, create a "
"purchase order from the Purchase app. Link it to the right vendor to ensure "
"timely replenishment."
msgstr ""
"<strong>Réapprovisionnez si besoin</strong> : Si les niveaux de stock sont "
"bas, créez un bon de commande à partir de l'application Achats. Reliez-le au"
" bon fournisseur pour garantir un réassort dans les délais."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Scheduling and Resource Allocation 🏋️‍♂️</strong>"
msgstr "<strong>Planification et allocation des ressources 🏋️‍♂️</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "<strong>Set Up Automatic Reordering</strong>:"
msgstr "<strong>Configuration du réapprovisionnement automatique</strong> :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Thanks</strong> to the invoicing policy that you can modify, the "
"generated invoice is always the reflect of the reality on the field and it "
"is fair for the customer."
msgstr ""
"<strong>Grâce</strong> à la politique de facturation modifiable, la facture "
"générée correspond toujours à la réalité du terrain et est équitable pour le"
" client."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"<strong>Track Progress</strong>: Monitor the status of maintenance requests "
"in real-time, from creation to completion, ensuring nothing falls through "
"the cracks."
msgstr ""
"<strong>Suivi de la progression</strong> : Suivez l'état des demandes "
"d'entretien en temps réel, de la création à la finalisation, afin de vous "
"assurer que rien n'est laissé au hasard."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"A free, personalized, integrated <strong><font class=\"text-o-color-1\">Website</font></strong> in a few clicks. Start receiving orders directly in Odoo and go live with your \n"
"                <strong><font class=\"text-o-color-1\">E-shop</font></strong> in a few minutes."
msgstr ""
"Un <strong><font class=\"text-o-color-1\">Site Web</font></strong> gratuit, personnalisé et intégré en quelques clics. Commencez à recevoir des commandes directement dans Odoo et mettez votre \n"
"                <strong><font class=\"text-o-color-1\">eShop</font></strong> en ligne en quelques minutes seulement."

#. module: fitness
#: model:website.menu,name:fitness.website_menu_11
msgid "About Us"
msgstr "À propos"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "About us"
msgstr "À propos de nous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Adaptable"
msgstr "Adaptable"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Aline Turner, Head Trainer"
msgstr "Aline Turner, Entraineur en chef"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house trainers and looks after the community of "
"fitness enthusiasts."
msgstr ""
"Aline fait partie des personnes emblématiques qui peuvent dire qu'elles "
"aiment ce qu'elles font. Elle encadre plus de 100 entraîneurs internes et "
"gère la communauté des passionnés de fitness."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Appointment, classes &amp; courses"
msgstr "Rendez-vous, classes et cours"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"As the first invoicing step, create a down payment from the sales order. This will generate the first invoice in the\n"
"            <strong>\n"
"                <font class=\"text-o-color-1\">Invoicing</font>\n"
"            </strong>\n"
"            app. Then, schedule one or several \"Invoicing Schedule\" activities on the sales order with the aim to never forget to send an intermediary invoice in order to sustain your cash flow"
msgstr ""
"La première étape de la facturation consiste à créer un acompte à partir de la commande client. Ceci générera la première facture dans l'application\n"
"            <strong>\n"
"                <font class=\"text-o-color-1\">Facturation</font>\n"
"            </strong>\n"
"            . Ensuite, planifiez une ou plusieurs activités \"Planification de facturation\" sur la commande client dans le but de ne jamais oublier d'envoyer une facture intermédiaire et de soutenir votre flux de trésorerie."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"At any time, create customized subscriptions for your members that clearly "
"display pricing, discounts, and details of each:"
msgstr ""
"Créez à tout moment des abonnements personnalisés pour vos membres en "
"affichant clairement les prix, les remises et les détails de chacun d'entre "
"eux :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid ""
"At our fitness center, we believe that fitness is not just about working "
"out. It's about creating a lifestyle that promotes health and wellness. Let "
"us help you achieve your fitness goals and live your best life."
msgstr ""
"Dans notre centre de fitness, nous pensons que le fitness n'est pas "
"seulement une question d'entraînement. Il s'agit de créer un mode de vie qui"
" favorise la santé et le bien-être. Laissez-nous vous aider à atteindre vos "
"objectifs de remise en forme et à vivre votre meilleure vie."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"At our gym club, we offer a diverse range of classes and programs tailored "
"to meet your fitness needs. Whether you are a beginner looking to start your"
" fitness journey or a seasoned athlete aiming to enhance your performance, "
"we have something for everyone. Our classes include yoga, personal training "
"sessions, and various group fitness activities designed to keep you "
"motivated and engaged. Join us to experience a supportive community and "
"achieve your fitness goals in a fun and effective way."
msgstr ""
"Dans notre club de sport, nous proposons une gamme variée de cours et de "
"programmes conçus pour répondre à vos besoins en matière de remise en forme."
" Que vous soyez un débutant qui cherche à s'initier au fitness ou un athlète"
" chevronné qui cherche à améliorer ses performances, nous avons de quoi "
"satisfaire tout le monde. Nos cours comprennent du yoga, des séances de "
"coaching personnel et diverses activités de fitness en groupe conçues pour "
"vous motiver et vous impliquer. Rejoignez-nous et découvrez une communauté "
"qui vous soutient. Vous pourrez ainsi atteindre vos objectifs de remise en "
"forme d'une manière amusante et efficace."

#. module: fitness
#: model:project.task.type,name:fitness.project_task_type_1
msgid "Backlog"
msgstr "Tâche en retard"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Basic fitness programs &amp;amp; training for all levels"
msgstr "Programmes de fitness basiques & entraînement pour tous les niveaux"

#. module: fitness
#: model:pos.payment.method,name:fitness.pos_payment_method_1
msgid "Cash"
msgstr "Espèces"

#. module: fitness
#: model:account.journal,name:fitness.cash
msgid "Cash (Fitness Center)"
msgstr "Espèces (Centre de fitness)"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Complete Fitness Solutions for Everyone"
msgstr "Des solutions de fitness complètes pour tous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Creative Studio"
msgstr "Studio créatif"

#. module: fitness
#: model:pos.payment.method,name:fitness.pos_payment_method_2
msgid "Customer Account"
msgstr "Compte client"

#. module: fitness
#: model:product.pricelist,name:fitness.product_pricelist_1
msgid "Default Website Pricelist"
msgstr "Liste de prix par défaut du site web"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Diamond"
msgstr "Diamond"

#. module: fitness
#: model:product.template,name:fitness.product_product_9_product_template
msgid "Diamond membership"
msgstr "Adhésion Diamond"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Découvrez-en plus sur Odoo en vous plongeant dans notre"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Do you want to go further?"
msgstr "Vous souhaitez aller plus loin ?"

#. module: fitness
#: model:project.task.type,name:fitness.project_task_type_2
msgid "Done sessions"
msgstr "Sessions complétées"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Durable"
msgstr "Durable"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Ensuring your equipment is always in top-notch condition is crucial for smooth operations. Odoo's <strong><font class=\"text-o-color-1\">Maintenance</font></strong> app provides a comprehensive solution for managing all your maintenance\n"
"            needs. From defining equipment to creating maintenance requests and assigning repair tasks, Odoo simplifies every aspect of maintenance management. Let’s dive into how Odoo can transform your maintenance processes."
msgstr ""
"S'assurer que votre équipement est toujours en parfait état est crucial pour des opérations réussies. L'application <strong><font class=\"text-o-color-1\">Maintenance</font></strong> d'Odoo fournit une solution complète pour gérer tous vos besoins\n"
"            d'entretien. De la spécification de l'équipement à la création de demandes d'entretien et à l'attribution de tâches de réparation, Odoo simplifie tous les aspects de la gestion de l'entretien. Voyons comment Odoo peut transformer vos processus de maintenance."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Feel free to create all the needed products you often sell, such as "
"supplements and workout gear. This will save you time for the next "
"subscriptions."
msgstr ""
"N'hésitez pas à créer tous les produits nécessaires, que vous vendez "
"régulièrement, tels que les compléments alimentaires et le matériel "
"d'entraînement. Vous gagnerez ainsi du temps pour les prochains abonnements."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Fitness Center"
msgstr "Centre de Fitness"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Fitness Center by Odoo"
msgstr "Le centre de fitness par Odoo"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Fitness Programs <strong>🏋️‍♂️</strong>"
msgstr "Programmes de fitness <strong>🏋️‍♂️</strong>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Fitness Programs 🏋️‍♂️"
msgstr "Programmes de fitness 🏋️‍♂️"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Fitness classes and personal training sessions"
msgstr "Cours de fitness et séances d'entraînement personnel"

#. module: fitness
#: model:pos.category,name:fitness.pos_category_7
msgid "Food"
msgstr "Alimentation"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Founder and head coach, Tony is the driving force behind the fitness center."
" He loves to keep his hands full by participating in the development of "
"fitness programs, marketing, and customer experience strategies."
msgstr ""
"Fondateur et entraîneur en chef, Tony est la force motrice du centre de "
"fitness. Il aime rester très actif en participant à l'élaboration des "
"programmes de fitness et en s'occupant du marketing et des stratégies "
"d'expérience client."

#. module: fitness
#: model:product.pricelist,name:fitness.product_pricelist_2
msgid "Free courses"
msgstr "Cours gratuits"

#. module: fitness
#: model:ir.actions.server,name:fitness.update_customer_price_server_action
msgid "Free courses for subs - Server Actions"
msgstr "Cours gratuits pour les abonnés - Actions du serveur"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"From maintaining equipment and scheduling appointments to managing "
"subscriptions and running a smooth point-of-sale system with products and "
"stock management, everything is covered."
msgstr ""
"Tout est compris : de l'entretien de l'équipement à la programmation des "
"rendez-vous, en passant par la gestion des abonnements et l'exploitation "
"d'un système de point de vente fluide avec la gestion des produits et des "
"stocks."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Get access to all fitness programs"
msgstr "Accédez à tous les programmes de fitness"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Get access to all fitness programs and classes"
msgstr "Accès à tous les programmes et cours de fitness"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid ""
"Get fit and healthy with Company edu-test's new fitness center. Our "
"personalized approach ensures that every member gets the attention they need"
" to achieve their fitness goals. Join us today and start your journey to a "
"healthier you!"
msgstr ""
"Soyez en forme et en bonne santé grâce au nouveau centre de fitness Company "
"edu-test. Notre approche personnalisée garantit une attention particulière à"
" chaque membre afin qu'il atteigne ses objectifs de remise en forme. "
"Rejoignez-nous dès aujourd'hui et entreprenez votre parcours de remise en "
"forme !"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Gold"
msgstr "Or"

#. module: fitness
#: model:product.template,name:fitness.product_product_8_product_template
msgid "Gold Membership"
msgstr "Adhésion Gold"

#. module: fitness
#: model:planning.role,name:fitness.planning_role_2
msgid "Hit 45' Stars"
msgstr "Hit 45' Stars"

#. module: fitness
#: model:appointment.type,name:fitness.appointment_type_1
#: model:product.template,name:fitness.default_booking_product_product_template
msgid "Hit Course"
msgstr "Hit Course"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"If the customer replies to the email, the response is automatically logged in the chatter on the right of the screen. All discussions regarding the quotation, updates, and fine-tuning are attached, making it easy for all involved users\n"
"            to consult the history."
msgstr ""
"Si le client répond à l'e-mail, sa réponse est automatiquement enregistrée dans le chatter situé à droite de l'écran. Toutes les discussions concernant le devis, les mises à jour et les ajustements sont jointes, ce qui permet à tous les utilisateurs concernés\n"
"             de consulter facilement l'historique."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Intuitive"
msgstr "Intuitif"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Inventory 📦"
msgstr "Inventaire 📦"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Invoicing 📈"
msgstr "Facturation 📈"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Iris Joe, Head of Finance"
msgstr "Iris Joe, directeur financier"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Iris, with her international experience, helps us easily understand the "
"Fitness Center and improves our programs. She is determined to drive success"
" and delivers her professional acumen to bring the fitness center to the "
"next level."
msgstr ""
"Grâce à son expérience internationale, Iris nous aide à comprendre les "
"besoins du centre de fitness et à améliorer les programmes. Elle est "
"déterminée à réussir et met ses compétences professionnelles au service du "
"centre pour le faire progresser."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Join Now"
msgstr "Rejoignez-nous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Join Us Now"
msgstr "Rejoignez-nous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Join the Fitness Revolution"
msgstr "Joignez-vous à la révolution du fitness"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Learn more"
msgstr "En savoir plus"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_1
msgid "Lifiting machine A1"
msgstr "Machine de levage A1"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_2
msgid "Lifting Machine A2"
msgstr "Machine de levage A2"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Maintenance of your equipments"
msgstr "Entretien de vos équipements"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">Email Marketing</font></strong>, \n"
"                <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, and <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""
"Gérez tous vos canaux de communication depuis un seul endroit avec les applications Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">E-mail Marketing</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, et <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Manage in the Planning App the schedule of your teachers, the booking of "
"your rooms and equipments."
msgstr ""
"Gérez l'emploi du temps des entraîneurs, les réservations de salles et les "
"équipements dans l'application Planification."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Manage personalised fitness program using the <strong><font class=\"text-o-"
"color-1\">Project App</font></strong>. You can create templates and sell it "
"using a simple Service product."
msgstr ""
"Gérez les programmes de remise en forme personnalisés à l'aide de "
"l'application <strong><font class=\"text-o-color-1\">Projet</font></strong>."
" Vous pouvez créer des modèles et les vendre à l'aide d'un simple produit de"
" service."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Manage the prices applied by your subscription on every product using the "
"Pricelists. Assign a specific pricelist to a reccuring product "
"(subscription) and edit your pricelist rule in Sales &gt; Products &gt; "
"Pricelists."
msgstr ""
"Gérez les prix applicables à tous les produits de votre abonnement à l'aide "
"des listes de prix. Attribuez une liste de prix spécifique à un produit "
"récurrent (abonnement) et modifiez votre règle de liste de prix dans Ventes "
"> Produits > Listes de prix."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"Gérez votre comptabilité plus facilement que jamais grâce à un environnement"
" totalement intégré. (<strong><font class=\"text-o-"
"color-1\">Comptabilité</font></strong>)"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Managing appointments effectively requires a robust resource management system that can handle various elements seamlessly. Odoo offers comprehensive solutions for employee management, appointment scheduling, and resource allocation,\n"
"            ensuring your operations run smoothly. Let’s explore how Odoo manages appointment ."
msgstr ""
"La gestion efficace des rendez-vous nécessite un solide système de gestion des ressources qui peut gérer différents éléments de manière transparente. Odoo offre des solutions complètes pour la gestion des employés, la planification des rendez-vous et l'allocation des ressources,\n"
"            garantissant ainsi le bon déroulement de vos opérations. Voyons comment Odoo gère les rendez-vous."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Managing maintenance effectively starts with having a clear overview of your"
" equipment and its status. Odoo allows you to:"
msgstr ""
"La gestion efficace de la maintenance commence par une vue d'ensemble claire"
" de votre équipement et de son état. Odoo vous permet de :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Membership plans with different durations and benefits"
msgstr "Plans d'adhésion avec différentes durées et avantages"

#. module: fitness
#: model:pos.category,name:fitness.pos_category_8
msgid "Merchandise"
msgstr "Marchandises"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Mich Stark, Head of Marketing"
msgstr "Mich Stark, directeur marketing"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Mich loves taking on fitness challenges. With his multi-year experience as a"
" fitness trainer, Mich has helped countless clients achieve their fitness "
"goals. Mich is among the best trainers in the industry."
msgstr ""
"Mich adore relever des défis en matière de fitness. Grâce à son expérience "
"de plusieurs années en tant qu'entraîneur de fitness, Mich a aidé "
"d'innombrables clients à atteindre leurs objectifs sportifs. Mich fait "
"partie des meilleurs entraîneurs du secteur."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "No Commitment"
msgstr "Sans engagement"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "No Hidden Fees"
msgstr "Pas de frais cachés"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Now that we've covered inventory and vendor management, let's dive into how you can extend all of these features to your <strong><font class=\"text-o-color-1\">Point of Sale</font></strong> (POS) system and seamlessly link it to your\n"
"            website for a complete eCommerce solution."
msgstr ""
"Maintenant que nous avons abordé la gestion des stocks et des fournisseurs, voyons comment vous pouvez étendre toutes ces fonctionnalités à votre <strong><font class=\"text-o-color-1\">Point de Vente</font></strong> (PdV) et relier celui-ci de manière harmonieuse\n"
"            à votre site web pour obtenir une solution eCommerce complète."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Odoo allows you to enhance your appointment scheduling by adding various "
"resources such as classes and coaches. Whether you offer fitness classes, "
"personal training sessions, or educational courses, you can:"
msgstr ""
"Odoo permet d'améliorer la prise de rendez-vous en ajoutant diverses "
"ressources telles que des cours et des entraîneurs. Que vous proposiez des "
"cours de fitness, des séances d'entraînement personnel ou des formations, "
"vous pouvez :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Odoo can also manage your accounting entirely, integrated with every other "
"apps. Check out more about the Accounting App"
msgstr ""
"Odoo permet également de gérer entièrement votre comptabilité, en "
"l'intégrant à toutes les autres applications. Découvrez-en plus sur "
"l'application Comptabilité"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo offre une infinité de possibilités, telles que :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Of course, all of this functionality is extendable to your POS system. With Odoo's integrated <strong><font class=\"text-o-color-1\">POS</font></strong> app, you can set up a cash register, manage sales transactions, and track inventory\n"
"            in real-time. This means that as soon as a sale is made, your stock levels are updated automatically, ensuring you never run out of popular items."
msgstr ""
"Bien sûr, toutes ces fonctionnalités peuvent être étendues à votre système de point de vente. Avec l'application <strong><font class=\"text-o-color-1\">PdV</font></strong> intégrée d'Odoo, vous pouvez mettre en place une caisse enregistreuse, gérer les transactions de vente et suivre l'inventaire\n"
"            en temps réel. Cela signifie que dès qu'une vente est effectuée, vos niveaux de stock sont mis à jour automatiquement afin que les articles les plus demandés ne soient jamais en rupture de stock."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Once confirmed, the quotation becomes a sales order. All quotations and "
"sales orders are centralized in the <strong><font class=\"text-o-"
"color-1\">Sales</font></strong> app."
msgstr ""
"Une fois confirmé, le devis devient une commande client Tous les devis et "
"toutes les commandes sont centralisés dans l'application <strong><font "
"class=\"text-o-color-1\">Ventes</font></strong>."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Online Booking"
msgstr "Réservation en ligne"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Order now"
msgstr "Commander maintenant"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""
"Organisez vos événements et communiquez avec vos clients en toute simplicité"
" grâce à l'<strong> <font class=\"text-o-color-1\">application "
"Événements</font></strong>."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Our Classes and Programs"
msgstr "Nos cours et programmes"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Our Services"
msgstr "Nos services"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "Our Testimonials"
msgstr "Nos retours"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Our gym club is dedicated to providing exceptional services that cater to "
"all aspects of your fitness journey. From basic and premium gym memberships "
"to personalized training sessions and nutritional support, we ensure that "
"every member receives the attention and resources they need. Our facilities "
"are equipped with state-of-the-art equipment, and our professional trainers "
"are here to guide you every step of the way. Invest in your health and "
"wellness with us, and see the results you've always wanted.<br/>"
msgstr ""
"Notre club de sport a pour mission de fournir des services exceptionnels qui"
" répondent à tous les aspects de votre parcours de remise en forme. Qu'il "
"s'agisse d'un abonnement de base ou d'un abonnement premium, de séances "
"d'entraînement personnalisées ou d'un soutien nutritionnel, nous veillons à "
"ce que chaque membre reçoive l'attention et les ressources dont il a besoin."
" Nos installations sont dotées d'équipements de pointe et nos entraîneurs "
"professionnels sont là pour vous guider à chaque étape. Investissez dans "
"votre santé et votre bien-être avec nous et obtenez les résultats dont vous "
"avez toujours rêvé.<br/>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Personal coaching with an expert<br/>"
msgstr "Coaching personnel avec un expert<br/>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Personal diet plan"
msgstr "Plan nutritionnel personnalisé"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Plus, you may add any Odoo App such as <strong><font class=\"text-o-color-1\">Marketing Apps </font></strong>to attract new members and keep your community engaged,\n"
"            <strong><font class=\"text-o-color-1\">Planning</font></strong> to schedule your shifts efficiently, <strong><font class=\"text-o-color-1\">Events</font></strong> to manage all kind of happenings,\n"
"            <strong><font class=\"text-o-color-1\">Project</font></strong> or <strong><font class=\"text-o-color-1\">Surveys</font></strong>..."
msgstr ""
"De plus, vous pouvez ajouter n'importe quelle application Odoo. Par exemple, l'application <strong><font class=\"text-o-color-1\">Marketing</font></strong>pour attirer de nouveaux membres et maintenir l'engagement de votre communauté,\n"
"            <strong><font class=\"text-o-color-1\">Planification</font></strong> pour planifier efficacement vos équipes, <strong><font class=\"text-o-color-1\">Evénements</font></strong> pour gérer tous les types d'événements,\n"
"            <strong><font class=\"text-o-color-1\">Projet</font></strong> ou <strong><font class=\"text-o-color-1\">Sondage</font></strong>..."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Point of Sale and eCommerce 📈"
msgstr "Point de Vente et eCommerce 📈"

#. module: fitness
#: model:website.menu,name:fitness.website_menu_12
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Pricing"
msgstr "Tarification"

#. module: fitness
#: model:product.template,name:fitness.product_product_11_product_template
msgid "Protein Bar"
msgstr "Barre protéinée"

#. module: fitness
#: model:product.template,name:fitness.product_product_12_product_template
msgid "Protein Powder"
msgstr "Poudre protéinée"

#. module: fitness
#: model:product.template,name:fitness.product_product_13_product_template
msgid "Protein Powder Strawberry "
msgstr "Poudre protéinée fraise"

#. module: fitness
#: model:product.template,name:fitness.product_product_10_product_template
msgid "Protein Shaker"
msgstr "Shaker pour protéines en poudre"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Reach us"
msgstr "Contactez-nous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid ""
"Ready to take your fitness journey to the next level? Our team of experts is"
" here to help you achieve your goals."
msgstr ""
"Vous êtes prêt à franchir une nouvelle étape dans votre parcours de remise "
"en forme ? Notre équipe d'experts est là pour vous aider à atteindre vos "
"objectifs."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Sales &amp; Subscriptions 💪"
msgstr "Ventes & abonnements 💪"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Selling food and consumables at your fitness center? With Odoo, you can "
"automate your reordering process, ensuring you never run out of stock. "
"Here's how to make the most of Odoo's inventory management features:"
msgstr ""
"Vous vendez de la nourriture et des produits consommables dans votre centre "
"de fitness ? Avec Odoo, vous pouvez automatiser votre processus de "
"réapprovisionnement, afin de ne jamais être en rupture de stock. Voici "
"comment tirer le meilleur des fonctions de gestion des stocks d'Odoo :"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Setting up your POS system is a breeze. Simply navigate to the <strong><font class=\"text-o-color-1\">POS</font></strong> app, configure your cash register, and customize your product listings. The system supports various payment methods,\n"
"            making it convenient for your customers to complete their purchases. Plus, you can generate detailed sales reports to keep track of your daily, weekly, and monthly performance."
msgstr ""
"La configuration de votre système de point de vente est très simple. Il suffit de naviguer vers l'application <strong><font class=\"text-o-color-1\">PdV</font></strong>, de configurer votre caisse et de personnaliser vos listes de produits. Le système prend en charge plusieurs modes de paiement,\n"
"            ce qui permet à vos clients de finaliser leurs achats en toute simplicité. De plus, vous pouvez générer des rapports de vente détaillés pour suivre vos performances quotidiennes, hebdomadaires et mensuelles."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Setting up your online store is as easy as customizing a few settings. Go to the <strong><font class=\"text-o-color-1\">Website</font></strong> app, choose your theme, and start adding your products. You can offer various payment\n"
"            options, manage shipping methods, and even set up automated marketing campaigns to boost your sales. With real-time inventory updates, your customers will always know what's available, enhancing their shopping experience."
msgstr ""
"La création de votre boutique en ligne est aussi simple que la personnalisation de quelques paramètres. Allez dans l'application <strong><font class=\"text-o-color-1\">Site Web</font></strong> sélectionnez votre thème et ajoutez vos produits. Vous avez la possibilité de choisir plusieurs options de\n"
"            paiement, de gérer les modes de livraison et même de mettre en place des campagnes de marketing automatisées pour stimuler vos ventes. Grâce aux mises à jour de l'inventaire en temps réel, vos clients sauront toujours ce qui est disponible, améliorant ainsi leur expérience d'achat."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Silver"
msgstr "Argent"

#. module: fitness
#: model:product.template,name:fitness.product_product_7_product_template
msgid "Silver Membership"
msgstr "Adhésion Silver"

#. module: fitness
#: model:project.task.type,name:fitness.project_task_type_3
msgid "Skipped"
msgstr "Ignoré"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Start now"
msgstr "Commencer maintenant"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid ""
"Start with the customer – find out what they need and give it to them.<br/>"
msgstr ""
"Commencez par le client – déterminez ce dont ils ont besoin et donnez-le-"
"leur.<br/>"

#. module: fitness
#: model:project.project,label_tasks:fitness.project_project_1
msgid "Tasks"
msgstr "Tâches"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tous ces services sont compris dans votre abonnement actuel ; n'hésitez pas "
"à les découvrir ! 🙃"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"This industry package offers everything you need to run your fitness center."
msgstr ""
"Ce pack offre tout ce dont vous avez besoin pour gérer votre centre de "
"fitness."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.aboutus
msgid "Tony Fred, Head Coach"
msgstr "Tony Fred, Entraîneur en chef"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_3
msgid "Treadmill A1"
msgstr "Tapis de course A1"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_4
msgid "Treadmill A2"
msgstr "Tapis de course A2"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_5
msgid "Treadmill A3"
msgstr "Tapis de course A3"

#. module: fitness
#: model:maintenance.equipment,name:fitness.maintenance_equipment_6
msgid "Treadmill A4"
msgstr "Tapis de course A4"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.pricing
msgid "Unlimited customization"
msgstr "Customisation illimitée"

#. module: fitness
#: model:base.automation,name:fitness.cancelled_subscription_automation
#: model:base.automation,name:fitness.free_courses_for_sub_automation
msgid "Update Customer > Pricelist"
msgstr "Mise à jour client > Liste de prix"

#. module: fitness
#: model:ir.actions.server,name:fitness.update_pricelist_server_action
msgid "Update Pricelist for Partner"
msgstr "Mise à jour de la liste de prix pour le partenaire"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "We build modular solutions."
msgstr "Nous construisons des solutions modulaires."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid ""
"We combine smart design with rich technology to craft innovative products."
msgstr ""
"Nous combinons un design intelligent avec une technologie de pointe pour "
"créer des produits innovants."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "We craft long-lasting goods."
msgstr "Nous fabriquons des produits durables."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.homepage
msgid "We create easy-to-use products."
msgstr "Nous créons des produits faciles d'utilisation."

#. module: fitness
#: model_terms:web_tour.tour,rainbow_man_message:fitness.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Bienvenue ! Amusez-vous en explorant."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"Whether it's planning fitness programs, collaborating with vendors, keeping an eye on the budget, handling invoicing, or delivering exceptional customer service, we've got you covered. Get ready to take your fitness business to the next\n"
"            level!"
msgstr ""
"Qu'il s'agisse de planifier des programmes de remise en forme, de collaborer avec des fournisseurs, de surveiller le budget, de gérer la facturation ou d'offrir un service client exceptionnel, nous avons ce qu'il vous faut. Faites passer votre entreprise de fitness à la vitesse \n"
"            supérieure !"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Vous souhaitez discuter avec nous de l'installation d'Odoo ou approfondir la"
" question ?"

#. module: fitness
#: model:planning.role,name:fitness.planning_role_1
msgid "Yoga Beginners Class"
msgstr "Cours de yoga pour débutants"

#. module: fitness
#: model:appointment.type,name:fitness.appointment_type_2
#: model:product.template,name:fitness.product_product_6_product_template
msgid "Yoga Course"
msgstr "Cours de yoga"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"You can then use the power of the <strong><font class=\"text-o-"
"color-1\">Project App</font></strong> to ensure a great follow up, with "
"automations, feedbacks, stages..."
msgstr ""
"Utilisez ensuite la puissance de l'application <strong><font class=\"text-o-"
"color-1\">Projet</font></strong> pour assurer un excellent suivi, avec des "
"automatisations, du feedback, des étapes..."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"You completed that industry tour! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Vous venez de compléter la visite guidée de ce secteur d'activité ! Il "
"existe des millions d'autres façons d'adapter votre configuration Odoo aux "
"besoins de votre entreprise."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fitness Center package and check the related box."
msgstr ""
"Vous n'avez pas importé de données de démonstration ? Vous pouvez encore le "
"faire. Allez dans Applications > Industries > Mettre à jour votre pack "
"Centre de Fitness et cochez la case correspondante."

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "academy"
msgstr "academy"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "and"
msgstr "et"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "documentation"
msgstr "documentation"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "if you need help!<br/>"
msgstr "si vous avez besoin d'aide ! <br/>"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "request a demo"
msgstr "demander une démonstration"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 Accounting"
msgstr "🎓 Comptabilité"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 Facturation"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 Pricelists"
msgstr "🎓 Listes de prix"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 Projet"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Ventes"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓 eCommerce"
msgstr "🎓 eCommerce"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓Appointment"
msgstr "🎓 Rendez-vous"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓Inventory"
msgstr "🎓 Inventaire"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓Maintenance"
msgstr "🎓 Maintenance"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓Planning"
msgstr "🎓 Planification"

#. module: fitness
#: model_terms:ir.ui.view,arch_db:fitness.welcome_article_body
msgid "🎓Point of Sale"
msgstr "🎓 Point de Vente"
