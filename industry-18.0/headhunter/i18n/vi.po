# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* headhunter
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:43+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: headhunter
#: model:mail.template,body_html:headhunter.crm_signature_agreem
msgid ""
"\n"
"            \n"
"                <p style=\"margin:0px 0 16px 0;box-sizing:border-box;\">Hello, here is a link to sign our\n"
"                    service agreement.<br/><br/>It will provide details on our offer and what\n"
"                    to expect from our collaboration.<br/><br/>\n"
"                </p>\n"
"                <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                    <a class=\"btn btn-fill-primary btn-lg\"\n"
"                    t-att-href=\"'/sign/document/mail/' + str(env.ref('headhunter.sign_request_1').id) + '/'+ str(env.ref('headhunter.sign_request_1').request_item_ids.access_token) + '?timestamp=' + str(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_link_timestamp()) + '&exp=' + str(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_signature(env.ref('headhunter.sign_request_1').request_item_ids.id, int(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_link_timestamp())))\"\n"
"                        style=\"border-style:solid;box-sizing:border-box;transition-property:none;transition-delay:0s;transition-timing-function:ease;transition-duration:0s;transition-behavior:normal;border-left-color:#714b67;border-bottom-color:#714b67;border-right-color:#714b67;border-top-color:#714b67;border-left-width:1px;border-bottom-width:1px;border-right-width:1px;border-top-width:1px;background-color:#714b67;user-select:none;cursor:pointer;vertical-align:middle;text-align:center;line-height:1.5;font-weight:500;display:inline-block;color:#ffffff;padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 13px;\">\n"
"                        Service Agreement</a>\n"
"                </div>\n"
"                <p style=\"box-sizing:border-box;margin-bottom: 0px;\"> Have a nice day,</p>\n"
"                <p style=\"margin:0px 0 16px 0;box-sizing:border-box;\">\n"
"                    <br/>\n"
"                </p>\n"
"            \n"
"        "
msgstr ""
"\n"
"            \n"
"                <p style=\"margin:0px 0 16px 0;box-sizing:border-box;\">Xin chào, đây là liên kết để ký thoả thuận dịch vụ\n"
"                    của chúng tôi.<br/><br/>Thoả thuận này sẽ cung cấp thông tin chi tiết về đề xuất và những gì\n"
"                    bạn có thể mong đợi từ mối quan hệ hợp tác của chúng ta.<br/><br/>\n"
"                </p>\n"
"                <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                    <a class=\"btn btn-fill-primary btn-lg\"\n"
"                    t-att-href=\"'/sign/document/mail/' + str(env.ref('headhunter.sign_request_1').id) + '/'+ str(env.ref('headhunter.sign_request_1').request_item_ids.access_token) + '?timestamp=' + str(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_link_timestamp()) + '&exp=' + str(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_signature(env.ref('headhunter.sign_request_1').request_item_ids.id, int(env.ref('headhunter.sign_request_1').request_item_ids._generate_expiry_link_timestamp())))\"\n"
"                        style=\"border-style:solid;box-sizing:border-box;transition-property:none;transition-delay:0s;transition-timing-function:ease;transition-duration:0s;transition-behavior:normal;border-left-color:#714b67;border-bottom-color:#714b67;border-right-color:#714b67;border-top-color:#714b67;border-left-width:1px;border-bottom-width:1px;border-right-width:1px;border-top-width:1px;background-color:#714b67;user-select:none;cursor:pointer;vertical-align:middle;text-align:center;line-height:1.5;font-weight:500;display:inline-block;color:#ffffff;padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 13px;\">\n"
"                        Hợp đồng Dịch vụ</a>\n"
"                </div>\n"
"                <p style=\"box-sizing:border-box;margin-bottom: 0px;\">Chúc bạn một ngày tốt lành!</p>\n"
"                <p style=\"margin:0px 0 16px 0;box-sizing:border-box;\">\n"
"                    <br/>\n"
"                </p>\n"
"            \n"
"        "

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1835
msgid "+****************"
msgstr "+****************"

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "1 Onsite Interview"
msgstr "1 Phỏng vấn tại chỗ"

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "1 Phone Call"
msgstr "1 cuộc điện thoại"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"1. Managing customers, creation of a lead, qualification, agreement "
"proposition and close on a sales order."
msgstr ""
"1. Quản lý khách hàng, tạo lead, đánh giá, đề xuất thỏa thuận và chốt đơn "
"bán hàng."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"12 days / year, including <br>\n"
"                                        6 of your choice."
msgstr ""
"12 ngày/năm, bao gồm <br>\n"
"                                        6 ngày tự chọn."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"12 days / year, including <br>\n"
"                    6 of your choice."
msgstr ""
"12 ngày/năm, bao gồm <br>\n"
"                    6 ngày tự chọn."

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "2 open days"
msgstr "2 ngày mở cửa"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "2. Managing applicants, application, interview, reserve, assignment."
msgstr "2. Quản lý ứng viên, hồ sơ ứng tuyển, phỏng vấn, dự bị, phân công."

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "4 Days after Interview"
msgstr "4 ngày sau phỏng vấn"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "<small><b>READ</b></small>"
msgstr "<small><b>ĐỌC</b></small>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Tiếp</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Trước</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1832
msgid "<span class=\"fst-italic\">No address specified</span>"
msgstr "<span class=\"fst-italic\">Không xác định địa chỉ</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO của "
"MyCompany</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "<span class=\"s_number display-4\">10+</span>"
msgstr "<span class=\"s_number display-4\">10+</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "<span class=\"s_number display-4\">2000+</span>"
msgstr "<span class=\"s_number display-4\">2000+</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "<span class=\"s_number display-4\">25+</span>"
msgstr "<span class=\"s_number display-4\">25+</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "<span class=\"s_number display-4\">300+</span>"
msgstr "<span class=\"s_number display-4\">300+</span>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<span class=\"s_rating_active_icons text-primary\"> <i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>\n"
"                                        <span class=\"s_rating_inactive_icons text-primary\"> field </span>"
msgstr ""
"<span class=\"s_rating_active_icons text-primary\"> <i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>\n"
"                                        <span class=\"s_rating_inactive_icons text-primary\"> field </span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "<span class=\"s_website_form_label_content\">Job Position Wanted</span>"
msgstr "<span class=\"s_website_form_label_content\">Vị trí mong muốn</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<span class=\"s_website_form_label_content\">Phone Number</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Số điện thoại</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tiêu đề</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Công ty của bạn</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr "<span class=\"s_website_form_label_content\">Câu hỏi</span>"

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">Số ngày để nhận Đề nghị</span>"

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Quá trình</span>"

#. module: headhunter
#: model_terms:hr.job,job_details:headhunter.hr_job_10
#: model_terms:hr.job,job_details:headhunter.hr_job_6
#: model_terms:hr.job,job_details:headhunter.hr_job_7
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Thời gian để trả lời</span>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">\n"
"                                                                <font class=\"bg-o-color-4\">I can't speak highly enough about this company ! Their team of recruiters truly understands our industry and has consistently delivered top-notch talent. They take the time to understand our unique needs and culture, which has resulted in some incredible hires for our organization. Their professionalism, dedication, and ability to find the perfect fit is second to none. If you're looking for a recruitment partner that goes above and beyond, it's a match.</font>\n"
"                                                            </span>\n"
"                                                            <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">\n"
"                                                                <font class=\"bg-o-color-4\">Tôi không có đủ mỹ từ để khen ngợi về công ty này! Đội ngũ tuyển dụng của họ thực sự hiểu rõ ngành của chúng tôi và luôn cung cấp những nhân tài hàng đầu. Họ dành thời gian để nắm bắt nhu cầu và văn hóa độc đáo của chúng tôi, điều này đã giúp mang tới chúng tôi một số nhân sự tuyệt vời. Không đâu có thể sánh bằng sự chuyên nghiệp, tận tụy và khả năng tìm kiếm ứng viên phù hợp hoàn hảo của họ. Nếu bạn đang tìm kiếm một đối tác tuyển dụng vượt trội, thì đây chính là sự lựa chọn phù hợp.</font>\n"
"                                                            </span>\n"
"                                                            <br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">\n"
"                                                <font class=\"bg-o-color-4\">Our mission is to bridge the gap between outstanding professionals and forward-thinking companies. Our team of dedicated executive recruiters utilizes their industry expertise and vast network to identify, engage, and secure the best-fit candidates for your organization. We cater to a wide range of industries, including ...</font>\n"
"                                            </span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">\n"
"                                                <font class=\"bg-o-color-4\">Sứ mệnh của chúng tôi là thu hẹp khoảng cách giữa những ứng viên xuất sắc và các doanh nghiệp cấp tiến. Đội ngũ tuyển dụng tận tụy của chúng tôi sử dụng chuyên môn trong ngành và mạng lưới rộng lớn của họ để xác định, thu hút và đảm bảo các ứng viên phù hợp nhất cho tổ chức của bạn. Chúng tôi phục vụ nhiều ngành nghề, bao gồm...</font>\n"
"                                            </span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">From software engineers to IT leaders, we have the tech talent you need to drive innovation.</span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">Từ kỹ sư phần mềm đến các lãnh đạo IT, chúng tôi có nhân tài công nghệ mà bạn cần để thúc đẩy sự đổi mới.</span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">Welcome to your premier destination for top-tier talent acquisition and executive search services. We specialize in connecting organizations with exceptional candidates who are not only qualified but also aligned with your company's vision and values.</span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-3\">Chào mừng bạn đến với điểm đến hàng đầu về dịch vụ tìm kiếm nhân tài và quản lý xuất chúng. Chúng tôi chuyên kết nối các tổ chức với những ứng viên xuất sắc vừa đáp ứng tiêu chuẩn vừa phù hợp với tầm nhìn và giá trị của công ty bạn.</span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                                                <font class=\"bg-o-color-3\">Elevate your brand and revenue with our sales and marketing professionals.</font>\n"
"                                            </span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                                                <font class=\"bg-o-color-3\">Nâng cao thương hiệu và doanh thu của bạn với các chuyên viên sales và marketing của chúng tôi.</font>\n"
"                                            </span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:hr.job,description:headhunter.hr_job_6
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                                As a Junior Developer at Sales &amp; More, you will have the opportunity to work on various aspects of software development, from coding and testing to debugging and deployment.\n"
"                                </span>\n"
"                                <span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400; background-color: rgb(247, 247, 248);\"> </span> <br>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                                Là một Lập trình viên mới tại Sales & More, bạn sẽ có cơ hội làm việc ở nhiều khía cạnh khác nhau của quá trình phát triển phần mềm, từ viết mã và kiểm thử đến gỡ lỗi và triển khai.\n"
"                                </span>\n"
"                                <span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400; background-color: rgb(247, 247, 248);\"> </span> <br>"

#. module: headhunter
#: model_terms:hr.job,description:headhunter.hr_job_7
msgid ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                        As a Data Analyst at My Dream Company, you will play a crucial role in collecting, analyzing, and interpreting data to inform strategic decisions and improve business operations.\n"
"                        </span>\n"
"                        <br>"
msgstr ""
"<span style=\"color: rgb(55, 65, 81); font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-4\">\n"
"                        Là một Chuyên viên Phân tích Dữ liệu tại My Dream Company, bạn sẽ đóng vai trò quan trọng trong việc thu thập, phân tích và diễn giải dữ liệu để đưa ra các quyết định chiến lược và cải thiện hoạt động kinh doanh.\n"
"                        </span>\n"
"                        <br>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-2 text-o-color-3\">Our financial experts can help you find top-notch finance and accounting professionals.</span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"font-size: 16px; font-style: normal; font-weight: 400;\" class=\"bg-o-color-2 text-o-color-3\">Các chuyên gia tài chính của chúng tôi có thể giúp bạn tìm được nhân lực tài chính và kế toán hàng đầu.</span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"<span style=\"font-size: 16px; font-style: normal; font-weight: 400;\" class=\"text-o-color-1 bg-o-color-1\">\n"
"                                                <font class=\"text-o-color-3\">Connect with skilled healthcare practitioners and executives to enhance patient care.</font>\n"
"                                            </span>\n"
"                                            <br/>"
msgstr ""
"<span style=\"font-size: 16px; font-style: normal; font-weight: 400;\" class=\"text-o-color-1 bg-o-color-1\">\n"
"                                                <font class=\"text-o-color-3\">Kết nối với các chuyên gia quản lý và bác sĩ chăm sóc sức khỏe có tay nghề cao để nâng cao chất lượng chăm sóc bệnh nhân.</font>\n"
"                                            </span>\n"
"                                            <br/>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-"
"bold);\">Coding:</strong> Collaborate with senior developers to write clean,"
" maintainable, and efficient code for software applications and features."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Viết "
"mã:</strong> Hợp tác với các lập trình viên kinh nghiệm để viết mã sạch, dễ "
"bảo trì và hiệu quả cho các tính năng và ứng dụng phần mềm."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Collaboration:</strong> Work closely with cross-functional teams, including business analysts, engineers, and decision-makers, to understand data\n"
"                            requirements and deliver actionable insights."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Cộng "
"tác:</strong> Hợp tác chặt chẽ với các bộ phận đa chức năng, bao gồm các "
"chuyên viên phân tích nghiệp vụ, kỹ sư và người ra quyết định, để hiểu các "
"yêu cầu về dữ liệu và cung cấp thông tin hữu ích."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Collaboration:</strong> Work closely with cross-functional teams, including designers, product managers, and quality assurance, to ensure project\n"
"                                        success."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Cộng tác:</strong> Hợp tác chặt chẽ với các bộ phận đa chức năng, bao gồm nhà thiết kế, quản lý sản phẩm và đảm bảo chất lượng để đảm bảo dự án\n"
"thành công."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Data "
"Analysis:</strong> Utilize statistical analysis and data visualization "
"techniques to identify trends, patterns, and insights within datasets."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Phân tích "
"dữ liệu:</strong> Sử dụng các kỹ thuật phân tích thống kê và trực quan hóa "
"dữ liệu để xác định xu hướng, mô hình và thông tin trong các tập dữ liệu."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Data "
"Collection:</strong> Gather and organize data from various sources, ensuring"
" data accuracy and completeness."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Thu thập dữ"
" liệu:</strong> Thu thập và sắp xếp dữ liệu từ nhiều nguồn khác nhau, đảm "
"bảo tính chính xác và đầy đủ của dữ liệu."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Data "
"Quality Assurance:</strong> Develop and implement data quality checks and "
"processes to maintain data integrity."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Đảm bảo "
"chất lượng dữ liệu:</strong> Phát triển và triển khai các quy trình, đồng "
"thời kiểm tra chất lượng dữ liệu để duy trì tính toàn vẹn của dữ liệu."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-"
"bold);\">Documentation:</strong> Create and maintain technical "
"documentation, including code comments and user guides."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Tài "
"liệu:</strong> Tạo và duy trì tài liệu kỹ thuật, bao gồm chú thích mã và "
"hướng dẫn sử dụng."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Key "
"Responsibilities:</strong>"
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Nhiệm vụ "
"chính:</strong>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Learning and Growth:</strong> Actively seek opportunities to expand your knowledge and skills in software development, staying up-to-date with industry\n"
"                                        trends and best practices."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Học hỏi và phát triển:</strong> Chủ động tìm kiếm cơ hội để mở rộng kiến ​​thức và kỹ năng của bạn trong phát triển phần mềm, cập nhật các xu hướng\n"
"và phương pháp tối ưu nhất trong ngành."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-"
"bold);\">Reporting:</strong> Prepare and present clear and concise reports, "
"dashboards, and visualizations to communicate findings to stakeholders."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Báo "
"cáo:</strong> Chuẩn bị và trình bày các báo cáo, trang tổng quan cũng như "
"hình ảnh trực quan rõ ràng và ngắn gọn để truyền đạt thông tin cho các bên "
"liên quan."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-"
"bold);\">Testing:</strong> Assist in writing and executing test cases, "
"debugging issues, and ensuring the quality and reliability of the software."
msgstr ""
"<strong style=\"font-weight: 600; color: var(--tw-prose-bold);\">Kiểm "
"thử:</strong> Hỗ trợ viết và tiến hành các trường hợp kiểm thử, gỡ lỗi và "
"đảm bảo chất lượng cũng như độ tin cậy của phần mềm."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"A full-time position <br>\n"
"                                        Attractive salary package."
msgstr ""
"Công việc toàn thời gian <br>\n"
"                                        Lương thưởng hấp dẫn."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"A full-time position <br>\n"
"                    Attractive salary package."
msgstr ""
"Công việc toàn thời gian <br>\n"
"                      Lương thưởng hấp dẫn."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Ability to work independently and as part of a team"
msgstr "Khả năng làm việc độc lập và làm việc nhóm"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Achieve monthly sales objectives"
msgstr "Đạt được mục tiêu kinh doanh hằng tháng"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Additional languages"
msgstr "Biết ngôn ngữ khác"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Administrative Work"
msgstr "Công việc quản lý"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Administrative and Support Services"
msgstr "Dịch vụ Hành chính và Hỗ trợ"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"After the qualification, applicant will be moved to third stage \"First "
"Interview\", he'll receive a mail template with a link for an Interview "
"(managed by Appointment) where he can select slots at his convenience."
msgstr ""
"Sau khi đủ điều kiện, ứng viên sẽ được chuyển đến vòng thứ ba \"Phỏng vấn "
"lần đầu\", ứng viên sẽ nhận được mẫu email có liên kết đến buổi Phỏng vấn "
"(được quản lý trong Lịch hẹn), tại đó ứng viên có thể chọn khung giờ theo ý "
"muốn."

#. module: headhunter
#: model:crm.stage,name:headhunter.crm_stage_7
msgid "Agreement Signed"
msgstr "Thoả thuận đã ký"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1831
msgid "All Countries"
msgstr "Tất cả quốc gia"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1833
msgid "All Departments"
msgstr "Tất cả phòng ban"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1832
msgid "All Offices"
msgstr "Tất cả nhân viên"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1834
msgid "All Types"
msgstr "Tất cả các loại"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Analysis"
msgstr "Phân tích"

#. module: headhunter
#: model:hr.job,name:headhunter.hr_job_7
msgid "Analyst (Functional/Business)"
msgstr "Chuyên viên phân tích (Chức năng/Nghiệp vụ)"

#. module: headhunter
#: model:crm.tag,name:headhunter.crm_tag_2
msgid "Applicant Hired"
msgstr "Ứng viên đã được tuyển"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.crm_lead_customization_form_view
msgid "Applicants"
msgstr "Các ứng viên"

#. module: headhunter
#: model_terms:ir.actions.act_window,help:headhunter.headhunter_crm_case_categ0_act_job
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""
"Ứng viên và CV đính kèm của họ sẽ được tạo tự động khi một email được gửi đến.\n"
"                Nếu bạn cài đặt phân hệ quản lý tài liệu, tất cả các hồ sẽ sẽ được đánh chỉ mục tự động\n"
"                để bạn có thể dễ dàng tìm kiếm nội dung của các tài liệu này."

#. module: headhunter
#: model:ir.actions.act_window,name:headhunter.headhunter_crm_case_categ0_act_job
msgid "Applications"
msgstr "Đơn ứng tuyển"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"As a Developer, you will have the opportunity to work on various aspects of "
"software development, from coding and testing to debugging and deployment. "
"Your responsibilities will include:"
msgstr ""
"Là một Lập trình viên, bạn sẽ có cơ hội làm việc ở nhiều khía cạnh khác nhau"
" của quá trình phát triển phần mềm, từ viết mã và kiểm thử đến gỡ lỗi và "
"triển khai. Nhiệm vụ chính của bạn sẽ bao gồm:"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Autonomy"
msgstr "Tính tự chủ"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Bachelor Degree or Higher"
msgstr "Tốt nghiệp đại học hoặc cao hơn"

#. module: headhunter
#: model:hr.recruitment.stage,legend_blocked:headhunter.hr_recruitment_stage_9
msgid "Blocked"
msgstr "Đã bị chặn"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Business Flows"
msgstr "Chu trình kinh doanh"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "CRM : Manage the business"
msgstr "CRM: Quản lý doanh nghiệp"

#. module: headhunter
#: model:mail.template,name:headhunter.crm_signature_agreem
msgid "CRM : Signature Agreement"
msgstr "CRM: Ký thoả thuận"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Candidates"
msgstr "Ứng viên"

#. module: headhunter
#: model:product.template,name:headhunter.product_product_2_product_template
msgid "Candidates Proposal"
msgstr "Đề xuất ứng viên"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Check our jobs"
msgstr "Xem công việc của chúng tôi"

#. module: headhunter
#: model:product.template,name:headhunter.product_product_6_product_template
msgid "Company Analysis"
msgstr "Phân tích doanh nghiệp"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Confidentiality"
msgstr "Bảo mật"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1835
msgid "Contact us"
msgstr "Liên hệ"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Countries"
msgstr "Quốc gia"

#. module: headhunter
#: model_terms:product.template,description:headhunter.product_product_1_product_template
msgid ""
"Create a new job position on the career webpage for the customer <br>\n"
"                <br>\n"
"                This includes 1 applicant proposal."
msgstr ""
"Tạo một vị trí công việc mới trên trang web nghề nghiệp cho khách hàng<br>\n"
"<br>\n"
"Bao gồm 1 đề xuất ứng viên."

#. module: headhunter
#: model:ir.actions.server,name:headhunter.contact_activity_actions
#: model:ir.actions.server,name:headhunter.contact_applicant_actions
msgid "Create activity: Call"
msgstr "Tạo hoạt động: Cuộc gọi"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Create content that will help our users on a daily basis"
msgstr "Tạo nội dung hỗ trợ người dùng hằng ngày"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Customer Relationship"
msgstr "Quan hệ khách hàng"

#. module: headhunter
#: model:mail.template,subject:headhunter.crm_signature_agreem
msgid "Dear customer, please sign this service agreement."
msgstr "Kính gửi quý khách hàng, vui lòng ký thoả thuận dịch vụ này."

#. module: headhunter
#: model:hr.job,name:headhunter.hr_job_6
msgid "Developer"
msgstr "Lập trình viên"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Discover our products."
msgstr "Khám phá sản phẩm của chúng tôi."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid ""
"Do you want to work with us ? If you have any job position that you'll like "
"to be filled. Just fill the form and we'll come back to you "
"quickly.&amp;nbsp;"
msgstr ""
"Bạn có muốn làm việc với chúng tôi không? Nếu bạn đang muốn tuyển dụng bất "
"kỳ vị trí công việc nào, chỉ cần điền vào biểu mẫu này và chúng tôi sẽ phản "
"hồi cho bạn nhanh chóng.&amp;nbsp;"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"Each employee has a chance to see the impact of his work. You can make a real contribution to the success of the company.\n"
"                                <br>\n"
"                                Several activities are often organized all over the year, such as weekly sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Mỗi nhân viên đều có cơ hội thấy được tầm ảnh hưởng từ công việc của mình. Bạn có thể tạo ra những đóng góp thiết thực cho sự thành công của công ty.\n"
"                        <br>\n"
"                        Nhiều hoạt động được thường xuyên tổ chức trong năm, ví dụ như chơi thể thao hàng tuần, sự kiện team building, đồ uống hàng tháng, và hơn thế nữa."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"Each employee has a chance to see the impact of his work. You can make a real contribution to the success of the company.\n"
"            <br>\n"
"            Several activities are often organized all over the year, such as weekly sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Mỗi nhân viên đều có cơ hội thấy được tầm ảnh hưởng từ công việc của mình. Bạn có thể tạo ra những đóng góp thiết thực cho sự thành công của công ty.\n"
"                        <br>\n"
"                        Nhiều hoạt động được thường xuyên tổ chức trong năm, ví dụ như chơi thể thao hàng tuần, sự kiện team building, đồ uống hàng tháng, và hơn thế nữa."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Eat &amp; Drink"
msgstr "Ăn uống"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Engineering"
msgstr "Kỹ thuật"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"Every company need to find the perfect worker.<br/>\n"
"                                            Every worker deserves the best opportunities.&amp;nbsp;"
msgstr ""
"Mọi công ty đều cần tìm ra nhân lực hoàn hảo. <br/>\n"
"                                            Mọi nhân lực đều xứng đáng có được những cơ hội tốt nhất.&amp;nbsp;"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Exceptional interpersonal and communication skills<br>"
msgstr "Kỹ năng giao tiếp và ứng xử xuất sắc<br>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Expand your knowledge of various business industries"
msgstr "Mở rộng vốn kiến thức về các ngành kinh doanh khác nhau"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Experience in writing online content"
msgstr "Có kinh nghiệm viết nội dung online"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"Finally, when Recruitment team fulfills the job, the Sales create a quotation, based on the service agreement and the salary of the hired applicant. The quotation will lead to a Sales Order, and an Invoice will be sent to the\n"
"            customer."
msgstr ""
"Cuối cùng, khi bộ phận Tuyển dụng hoàn thành công việc, bộ phận Sales sẽ tạo báo giá dựa trên thỏa thuận dịch vụ và mức lương của ứng viên được tuyển. Báo giá sẽ tạo ra Đơn bán hàng và Hóa đơn sẽ được gửi đến\n"
"khách hàng."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Finance"
msgstr "Tài chính"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Finance and Accounting"
msgstr "Tài chính và Kế toán"

#. module: headhunter
#: model:appointment.type,name:headhunter.appointment_type_1
msgid "First Interview"
msgstr "Phỏng vấn lần đầu"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Flow 1 : Website --&gt; CRM --&gt; Invoice"
msgstr "Chu trình 1: Trang web --&gt; CRM --&gt; Hoá đơn"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Flow 2 : Apply for a job and manage applicants"
msgstr "Chu trình 2: Nộp đơn ứng tuyển và quản lý ứng viên"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1835
msgid "Follow us"
msgstr "Theo dõi chúng tôi"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"From the CRM, an activity is created for the salesperson to contact the "
"opportunity and qualify the lead (Studio : Automated Action)"
msgstr ""
"Từ CRM, một hoạt động được tạo ra để chuyên viên sales liên hệ với cơ hội và"
" đánh giá lead (Studio: Tác vụ tự động)"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"From the Website, go to the Career page and find the right job, from the "
"\"apply now\" page, Applicants can submit their Resume or a LinkedIn profile"
" link."
msgstr ""
"Từ Trang web, đi đến trang Nghề nghiệp và tìm công việc phù hợp, từ trang "
"\"Ứng tuyển ngay\", Ứng viên có thể gửi CV hoặc liên kết hồ sơ LinkedIn."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "From the Website, go to the Contact Us page and fill the contact form."
msgstr ""
"Từ Trang web, đi đến trang Liên hệ với chúng tôi và điền vào biểu mẫu liên "
"hệ."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"Fruit, coffee and <br>\n"
"                                        snacks provided."
msgstr ""
"Trái cây, cà phê và <br>\n"
"                                        đồ ăn vặt miễn phí."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"Fruit, coffee and <br>\n"
"                    snacks provided."
msgstr ""
"Trái cây, cà phê và <br>\n"
"                     đồ ăn vặt miễn phí."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Get Started"
msgstr "Get Started"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Global Reach"
msgstr "Tìm kiếm trên toàn cầu"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Google Adwords experience"
msgstr "Kinh nghiệm với Google Adwords"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Đội ngũ tuyệt vời gồm những người thông minh, trong một nền văn hóa thân "
"thiện và cởi mở"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Healthcare"
msgstr "Chăm sóc sức khỏe"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Highly creative and autonomous"
msgstr "Tính sáng tạo và tự chủ cao"

#. module: headhunter
#: model:product.template,name:headhunter.product_product_8_product_template
msgid "Hired Candidate Plan"
msgstr "Kế hoạch ứng viên đã tuyển"

#. module: headhunter
#: model:hr.department,name:headhunter.hr_department_2
msgid "IT"
msgstr "IT"

#. module: headhunter
#: model_terms:hr.job,description:headhunter.hr_job_10
msgid ""
"If you don't find any job that matches your skills, don't hesitate to apply "
"and we'll come back to you to talk about how we could work together. <br>"
msgstr ""
"Nếu bạn không tìm được công việc nào phù hợp với kỹ năng của mình, đừng ngần"
" ngại nộp đơn và chúng tôi sẽ liên hệ lại để trao đổi về cách chúng ta có "
"thể hợp tác. <br>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Improve your efficiency"
msgstr "Improve your efficiency"

#. module: headhunter
#: model:hr.recruitment.stage,legend_normal:headhunter.hr_recruitment_stage_9
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: headhunter
#: model:crm.stage,name:headhunter.crm_stage_6
msgid "In Recruitment"
msgstr "Đang tuyển dụng"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"In the Interview, his skills will be assessed and added to his record. He'll"
" be put in the Reserve stage. He can also receive the \"Reserve\" tag from "
"any moment in the process."
msgstr ""
"Trong buổi phỏng vấn, các kỹ năng của ứng viên sẽ được đánh giá và thêm vào "
"hồ sơ của họ. Ứng viên sẽ được chuyển sang giai đoạn Dự bị và có thể nhận "
"được thẻ \"Dự bị\" từ bất kỳ thời điểm nào trong quá trình này."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"In the database, you have two kinds of product.<br/>\n"
"            Hired Candidate Plan is an undefined price product, the amount of money will be based on the applicant's salary. <br/>"
msgstr ""
"Trong cơ sở dữ liệu, bạn có hai loại sản phẩm.<br/>\n"
"Kế hoạch ứng viên đã tuyển là sản phẩm có giá không xác định, số tiền sẽ dựa trên mức lương của ứng viên.<br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Industry Specialists"
msgstr "Chuyên gia trong ngành"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Information Technology"
msgstr "Công nghệ thông tin"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Invoicing :"
msgstr "Lập hoá đơn"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Invoicing : Close the deal"
msgstr "Lập hoá đơn: Chốt giao dịch"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "It's a match"
msgstr "Nơi cầu gặp cung"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"It's always possible that after his mission, he'll come back to the reserve "
"for another incoming opportunity."
msgstr ""
"Sau khi hoàn thành hợp đồng, ứng viên luôn có thể được đưa trở lại giai đoạn"
" dự bị để có cơ hội khác."

#. module: headhunter
#: model:product.template,name:headhunter.product_product_1_product_template
msgid "Job Onboarding"
msgstr "Onboarding công việc"

#. module: headhunter
#: model:website.menu,name:headhunter.website_menu_10
msgid "Jobs"
msgstr "Tuyển dụng"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Jobs offers"
msgstr "Đề xuất nhận việc"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Lead the entire sales cycle"
msgstr "Dẫn dắt toàn bộ chu trình kinh doanh"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"Linking Applicants to Opportunities, will allow you to filter data more "
"efficiently and always work in the same pipe, for all missions."
msgstr ""
"Việc liên kết Ứng viên với Cơ hội sẽ cho phép bạn lọc dữ liệu hiệu quả hơn "
"và luôn làm việc trong cùng một chu trình cho tất cả các nhiệm vụ."

#. module: headhunter
#: model:product.template,name:headhunter.product_product_7_product_template
msgid "Market deep dive"
msgstr "Nghiên cứu thị trường"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Marketing and Sales"
msgstr "Marketing và Bán hàng"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Master demos of our software"
msgstr "Nắm vững cách demo phần mềm"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Must Have"
msgstr "Yêu cầu"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "My Company"
msgstr "My Company"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Negotiate and contract"
msgstr "Thương thảo và lập hợp đồng"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Next"
msgstr "Tiếp theo"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Nice to have"
msgstr "Nếu có là lợi thế"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1832
msgid "No address specified"
msgstr "Không xác định địa chỉ"

#. module: headhunter
#: model_terms:ir.actions.act_window,help:headhunter.headhunter_crm_case_categ0_act_job
msgid "No applications yet"
msgstr "Chưa có đơn ứng tuyển nào"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Không hề có quản lý ngớ ngẩn, công cụ nhảm nhí hay giờ làm việc cứng nhắc"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Không tốn thời gian vào quy trình doanh nghiệp, tập trung vào trách nhiệm và"
" tự chủ thực sự"

#. module: headhunter
#: model_terms:ir.actions.act_window,help:headhunter.headhunter_crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Hệ thống giúp bạn theo dõi người nộp đơn trong tuyển dụng\n"
"                 xử lý và theo dõi tất cả các hoạt động: cuộc họp, phỏng vấn, v.v."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "On payment, the Opportunity is moved to \"Done\"."
msgstr "Sau khi thanh toán, Cơ hội sẽ được chuyển đến \"Hoàn tất\"."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"Once assigned, Applicant can move to second interview and finally to "
"Contract Signed."
msgstr ""
"Sau khi được phân công, Ứng viên có thể chuyển sang buổi phỏng vấn thứ hai "
"và cuối cùng là Ký hợp đồng."

#. module: headhunter
#: model:hr.job,name:headhunter.hr_job_10
msgid "Open Application"
msgstr "Vị trí tuyển dụng mở"

#. module: headhunter
#: model:sign.template,redirect_url_text:headhunter.sign_template_3
#: model:sign.template,redirect_url_text:headhunter.sign_template_5
msgid "Open Link"
msgstr "Mở liên kết"

#. module: headhunter
#: model:ir.model.fields,field_description:headhunter.x_opportunities
#: model_terms:ir.ui.view,arch_db:headhunter.hr_applicant_category_customization_search_view
msgid "Opportunities"
msgstr "Cơ hội"

#. module: headhunter
#: model:ir.model.fields,field_description:headhunter.x_opportunities_count
msgid "Opportunities count"
msgstr "Số cơ hội"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1833
msgid "Others"
msgstr "Khác"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Our Product"
msgstr "Sản phẩm"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Our References"
msgstr "Đối tác của chúng tôi"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid ""
"Our external recruitment agency is constantly expanding, and we are always "
"on the lookout for exceptional talent to join our workforce. If you are "
"passionate, we encourage you to apply now."
msgstr ""
"Cơ quan tuyển dụng ngoài của chúng tôi liên tục mở rộng và chúng tôi luôn "
"tìm kiếm những nhân tài xuất chúng gia nhập lực lượng lao động của mình. Nếu"
" bạn đam mê, chúng tôi khuyến khích bạn nộp đơn ngay bây giờ."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"Our recruiters are experts in their respective fields, ensuring you get the "
"right talent for your industry."
msgstr ""
"Đội ngũ tuyển dụng của chúng tôi là chuyên gia trong lĩnh vực của họ, đảm "
"bảo bạn có được nhân tài phù hợp với ngành của mình."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Passion for software products"
msgstr "Yêu thích các sản phẩm phần mềm"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Perfect written English"
msgstr "Kỹ năng viết tiếng Anh xuất sắc"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Perks"
msgstr "Quyền lợi"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Personal Evolution"
msgstr "Phát triển cá nhân"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Personalized Approach"
msgstr "Giải pháp riêng"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
msgid ""
"Play any sport with colleagues, <br>\n"
"                                        the bill is covered."
msgstr ""
"Chơi bất kỳ môn thể thao nào với đồng nghiệp <br>\n"
"                                        mà không hề tốn tiền."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid ""
"Play any sport with colleagues, <br>\n"
"                    the bill is covered."
msgstr ""
"Chơi bất kỳ môn thể thao nào với đồng nghiệp <br>\n"
"                      mà không hề tốn tiền."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Previous"
msgstr "Trước đó"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Proven Track Record"
msgstr "Thành tích đã được chứng minh"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Qualify the customer needs"
msgstr "Đáp ứng được nhu cầu khách hàng"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Quote-Sales Order - Invoice flow. <br/>"
msgstr "Chu trình Báo giá - Đơn bán hàng - Hoá đơn. <br/>"

#. module: headhunter
#: model:hr.recruitment.stage,legend_done:headhunter.hr_recruitment_stage_9
msgid "Ready for Next Stage"
msgstr "Sẵn sàng cho bước kế tiếp"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"Ready to find the perfect candidate for your company? Contact us today to "
"discuss your talent acquisition needs. Our team is committed to delivering "
"results, and we look forward to partnering with you to build a stronger, "
"more successful future."
msgstr ""
"Bạn đã sẵn sàng tìm ứng viên hoàn hảo cho doanh nghiệp mình? Hãy liên hệ với"
" chúng tôi ngay hôm nay để thảo luận về nhu cầu tuyển dụng nhân tài của bạn."
" Đội ngũ của chúng tôi cam kết mang lại kết quả và mong muốn được hợp tác "
"với bạn để xây dựng một tương lai vững mạnh và thành công hơn."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "Trách nhiệm và thách thức thực sự trong một công ty phát triển nhanh"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Recruitment :"
msgstr "Tuyển dụng:"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Recruitment :  Manage your applicants"
msgstr "Tuyển dụng: Quản lý ứng viên của bạn"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1831
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1832
msgid "Remote"
msgstr "Từ xa"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Requirements"
msgstr "Yêu cầu"

#. module: headhunter
#: model:hr.recruitment.stage,name:headhunter.hr_recruitment_stage_9
msgid "Reserve"
msgstr "Dự trữ"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Responsibilities"
msgstr "Chịu trách nhiệm"

#. module: headhunter
#: model:hr.department,name:headhunter.hr_department_3
msgid "Sales"
msgstr "Bán hàng"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Sales and Marketing"
msgstr "Bán hàng và Marketing"

#. module: headhunter
#: model:ir.actions.server,name:headhunter.agreement_signature_actions
msgid "Send email: CRM : Signature Agreement"
msgstr "Gửi email: CRM: Ký thoả thuận"

#. module: headhunter
#: model:hr.department,name:headhunter.hr_department_4
#: model:website.menu,name:headhunter.website_menu_9
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Services"
msgstr "Dịch vụ"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Sport Activity"
msgstr "Thể thao"

#. module: headhunter
#: model:ir.model.fields,field_description:headhunter.x_stage_name_field
msgid "Stage Name"
msgstr "Tên giai đoạn"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Strong analytical skills"
msgstr "Kỹ năng phân tích sắc sảo"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid "Strong expertise in your chosen field<br>"
msgstr "Có chuyên môn vững vàng trong lĩnh vực bạn đã chọn<br>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "Submit"
msgstr "Gửi"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Talent Acquisition Agency"
msgstr "Công Ty Thu Hút Nhân Tài"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "Talents Profiling"
msgstr "Tìm kiếm nhân tài"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Technical Expertise"
msgstr "Chuyên môn kỹ thuật"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Technology"
msgstr "Công nghệ"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"The Opportunity will be qualified and when the need is understood move the Opportunity to Proposition. It will automatically send an email with a Signature Request (Studio : Automated Action). That Signature Request hold an already\n"
"            signed, from Company, Service Agreement with default terms and filled values."
msgstr ""
"Cơ hội sẽ được đánh giá và khi xác nhận nhu cầu, hãy chuyển Cơ hội sang Đề "
"xuất. Hệ thống sẽ tự động gửi email có Yêu cầu chữ ký (Studio: Tác vụ tự "
"động). Yêu cầu chữ ký đó liên kết với một Thỏa thuận dịch vụ đã ký từ Công "
"ty với các điều khoản mặc định và giá trị đã điền."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "The business is based on two flows :"
msgstr "Hoạt động kinh doanh này dựa trên hai chu trình:"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"This customized form will create an opportunity in the CRM, with the right "
"information and in option, the jobs they're interested in."
msgstr ""
"Biểu mẫu tùy chỉnh này sẽ tạo ra cơ hội trong CRM, với thông tin và tùy chọn"
" phù hợp về công việc mà họ muốn tuyển dụng."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "This form will create an applicant in the Recruitment app."
msgstr "Biểu mẫu này sẽ tạo ứng viên trong ứng dụng Tuyển dụng."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"This setup is for Talent Acquisition Agency / Recruitment Agency / Staffing Agency. Those agencies work on two levels and with two different audiences. The customers will comes through their website, contact them and will be managed\n"
"            under market opportunities. The consultants or freelances will apply through the career pages and be managed by the company to match with the market opportunities."
msgstr ""
"Thiết lập ngành này dành cho Công ty thu hút nhân tài/Công ty tuyển dụng/Công ty cung cấp nhân sự. Các doanh nghiệp này hoạt động ở hai cấp độ và với hai đối tượng khác nhau. Khách hàng sẽ đến thông qua trang web của họ, liên hệ với họ và sẽ được quản lý theo\n"
"các cơ hội thị trường. Chuyên viên hoặc người làm việc tự do sẽ nộp đơn thông qua trang nghề nghiệp và được doanh nghiệp quản lý để đáp ứng các cơ hội thị trường."

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Trainings"
msgstr "Đào tạo"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid ""
"Trust us&amp;nbsp;to be your strategic partner in talent acquisition. "
"Elevate your organization with exceptional professionals today!<br/>"
msgstr ""
"Hãy tin tưởng lựa chọn chúng tôi là đối tác chiến lược trong việc thu hút "
"nhân tài. Nâng tầm tổ chức của bạn với nguồn nhân lực xuất sắc ngay hôm "
"nay!<br/>"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1834
msgid "Unspecified type"
msgstr "Loại hình không xác định"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "Valid work permit for Belgium"
msgstr "Giấy phép lao động hợp lệ tại Bỉ"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_10
msgid ""
"We are currently accepting spontaneous applications from skilled professionals in various fields. Our agency specializes in finding top-notch candidates for our clients across multiple industries, including (but not\n"
"                                                limited to):"
msgstr ""
"Hiện tại chúng tôi đang chấp nhận hồ sơ ứng tuyển từ các chuyên gia lành "
"nghề trong nhiều lĩnh vực khác nhau. Chúng tôi chuyên tìm kiếm những ứng "
"viên hàng đầu cho khách hàng trong nhiều ngành, bao gồm (nhưng không giới "
"hạn ở):"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid "We are in good company."
msgstr "Đây là một đơn vị uy tín."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"We have a history of successfully placing candidates in key positions across"
" various industries.&amp;nbsp;"
msgstr ""
"Chúng tôi có bề dày thành công trong việc tuyển dụng ứng viên vào các vị trí"
" quan trọng trong nhiều ngành nghề khác nhau."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"We help you acquire new talents from the market, based on your needs and "
"specifications.&amp;nbsp;"
msgstr ""
"Chúng tôi giúp bạn có được những nhân tài mới từ thị trường, dựa trên nhu "
"cầu và điều kiện của bạn."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"We prioritize discretion in all our dealings, providing a secure and "
"confidential recruitment process.&amp;nbsp;"
msgstr ""
"Chúng tôi ưu tiên sự kín đáo trong mọi giao dịch, cung cấp quy trình tuyển "
"dụng an toàn và bảo mật."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"We understand that every organization is unique, so we tailor our search "
"strategy to meet your specific needs.&amp;nbsp;"
msgstr ""
"Chúng tôi hiểu rằng mỗi tổ chức đều có đặc điểm riêng, vì vậy chúng tôi điều"
" chỉnh chiến lược tìm kiếm của mình để đáp ứng nhu cầu cụ thể của bạn."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Website : Apply for a job"
msgstr "Trang web: Nộp đơn ứng tuyển"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid "Website : Create an Opportunity"
msgstr "Trang web: Tạo cơ hội"

#. module: headhunter
#: model_terms:web_tour.tour,rainbow_man_message:headhunter.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "What We Offer"
msgstr "Ưu đãi của công ty"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_6
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "What's great in the job?"
msgstr "Điều gì tuyệt vời trong công việc?"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"When Opportunities will open, and the applicant is matching requirements, "
"you can link the Opportunity/ies to his Applicant record with a Many-to-Many"
" object linked to the CRM (Studio : Customization of the Applicant view)."
msgstr ""
"Khi Cơ hội mở và ứng viên đáp ứng các yêu cầu, bạn có thể liên kết Cơ hội "
"với hồ sơ Ứng viên đó bằng đối tượng Many-to-Many được liên kết với CRM "
"(Studio: Tùy chỉnh chế độ xem Ứng viên)."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"When Service Agreement is signed, the opportunity is manually moved to "
"\"Agreement Signed\", the Sales can open a discussion with the Recruitment "
"team, and Opportunity will go, and stay, in Recruitment stage, for a while."
msgstr ""
"Khi Thỏa thuận dịch vụ được ký, cơ hội sẽ được chuyển thủ công đến \"Thỏa "
"thuận đã ký\", bộ phận Sales có thể thảo luận với bộ phận Tuyển dụng và Cơ "
"hội sẽ chuyển sang giai đoạn Tuyển dụng trong một thời gian."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.welcome_article_body
msgid ""
"When the applicant is created, an activity for a call will be automatically "
"created and assigned to the Recruiter (Studio : Automated Action). The "
"applicant will get a confirmation email from Recruitment Stage "
"configuration."
msgstr ""
"Khi ứng viên được tạo, một hoạt động sẽ được tự động tạo ra cho cuộc gọi và "
"phân công cho Chuyên viên tuyển dụng (Studio: Tác vụ tự động). Ứng viên sẽ "
"nhận được email xác nhận từ cấu hình Giai đoạn tuyển dụng."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"With an extensive international network, we can source talent from around "
"the world"
msgstr ""
"Với mạng lưới quốc tế rộng lớn, chúng tôi có thể tìm kiếm nhân tài từ khắp "
"nơi trên thế giới"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.services
msgid ""
"With our experience, we'll help you analyse your company and the vacancies "
"that you should propose, or the market and the vibes in the Recruitment "
"area.&amp;nbsp;"
msgstr ""
"Bằng kinh nghiệm của mình, chúng tôi sẽ giúp bạn phân tích công ty và các vị"
" trí tuyển dụng mà bạn đề xuất, hoặc thị trường và tình hình trong lĩnh vực "
"Tuyển dụng."

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.homepage
msgid "Years of experience"
msgstr "Năm kinh nghiệm"

#. module: headhunter
#: model:base.automation,name:headhunter.agreement_signature_automation
msgid "agreement_signature_actions"
msgstr "agreement_signature_actions"

#. module: headhunter
#: model:base.automation,name:headhunter.contact_activity_automation
msgid "contact_activity_actions"
msgstr "contact_activity_actions"

#. module: headhunter
#: model:base.automation,name:headhunter.contact_applicant_automation
msgid "contact_applicant_actions"
msgstr "contact_applicant_actions"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.contact_us
msgid "eg. Python Developer, Sales Manager, Data Scientist, Accountant"
msgstr ""
"VD: Lập trình viên Python, Quản lý Sales, Kỹ sư Khoa học Dữ liệu, Kế toán"

#. module: headhunter
#: model_terms:ir.ui.view,arch_db:headhunter.ir_ui_view_1835
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "laptop computer on glass-top table"
msgstr "laptop computer on glass-top table"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "low-angle photography of metal structure"
msgstr "low-angle photography of metal structure"

#. module: headhunter
#: model_terms:hr.job,website_description:headhunter.hr_job_7
msgid "turned on monitoring screen"
msgstr "turned on monitoring screen"
