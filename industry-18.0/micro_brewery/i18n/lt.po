# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* micro_brewery
# 
# Translators:
# Anatolij, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-29 14:10+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.ir_ui_view_2543
msgid "+****************"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_17
msgid "+16"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_18
msgid "+18"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ", and feel free to"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.ir_ui_view_2543
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "4. Deposit Management 🍻"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid ""
"<font class=\"text-o-color-4\">\n"
"                                    Taste our&amp;nbsp;new refreshing beer now !\n"
"                                </font>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid ""
"<font class=\"text-o-color-4\">Discover our new Lager</font>\n"
"                                <br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "<i class=\"fa fa-credit-card me-2\"/>Buy now"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<span class=\"h1-fs\">Do you want to go further?</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<span class=\"h3-fs\"><strong>Analyse your sales.</strong></span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<span class=\"h3-fs\"><strong>Purchase and receive "
"components</strong></span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<span class=\"h3-fs\"><strong>Take an order at the Bar</strong></span> <br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<span class=\"h3-fs\"><strong>Visit the website and make an "
"order.</strong></span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Tel. nr.</span>"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong>1. Crafting a beer</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong>2. Selling the beer</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong>3. Visiting the Brewery </strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong><font class=\"text-o-color-1\">Ecommerce</font></strong> (B2C)"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong><span class=\"h3-fs\">Create a Recipe </span></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong><span class=\"h3-fs\">Rental</span></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong><span class=\"h3-fs\">Schedule a visit from the "
"website.</span></strong> <br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong><span class=\"h3-fs\">Sell your products and deliver it to a "
"bar</span></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong><span class=\"h3-fs\">Start brewing</span></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong>Basics</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "<strong>Blond</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "<strong>Brown</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong>Create and add the By-product</strong>s, for example:Spent Grain"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong>Create the operations</strong> of your recipe with the right work "
"center, for example:"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong>Indicate if there are instructions</strong> the operator should do "
"during some operation, for example:"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"<strong>Indicate what will be the finished product </strong>at the end of "
"your recipe and the quantity that should be produced with the recipe. for "
"example:"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "<strong>Lager</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "<strong>Use case</strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Add products in your cart and pay using the payment method \"Demo\" which is"
" a fake payment method you can use to test flows."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"After (or during) the meeting, you can use the button \"New Quotation\" to "
"create an offer for the customer. Once it is done, send it to the customer."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"After the visit, it is always great for visitors to be able to share a "
"moment around the good beer. Go into the \"Point Of Sales\" application and "
"create a product to sell in your bar/restaurant (e.g. Draft beer, snack, "
"...)."
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.beverage_product_attribute_15
msgid "Age Limit"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "Alcohol"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_5
msgid "Amber"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"At any time you can select a table and register the payment to close the "
"table."
msgstr ""

#. module: micro_brewery
#: model:base.automation,name:micro_brewery.auto_production
msgid "Auto Production"
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_aut_production
msgid "Auto-production"
msgstr ""

#. module: micro_brewery
#: model:base.automation,name:micro_brewery.bom_automation
msgid "Automated BoM"
msgstr ""

#. module: micro_brewery
#: model:product.pricelist,name:micro_brewery.product_pricelist_2
msgid "B2B"
msgstr "B2B"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Bar &amp; Shop at the Brewery (B2C) - Using the <strong><font class=\"text-"
"o-color-1\">Point of Sales App</font></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Beer bottle (33cl), 3000 units (+- 10hL)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_90_product_template
msgid "Beer cooler with tap - for 1 x 50 litre keg "
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.beverage_product_attribute_10
msgid "Beer type"
msgstr ""

#. module: micro_brewery
#: model:pos.category,name:micro_brewery.pos_category_4
#: model:product.public.category,name:micro_brewery.product_public_category_3
msgid "Beers"
msgstr ""

#. module: micro_brewery
#: model:uom.uom,name:micro_brewery.uom_uom_32
msgid "Bin"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "Bitterness"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "Bitterness<br/>"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_5
msgid "Black"
msgstr "Juoda"

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_2
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_7
msgid "Blond"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_20
msgid "Blond Beer"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_30_product_template
msgid "Blond Beer (25cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_29
msgid "Blond Beer (50cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_2
msgid "Blond Beer - Bottle"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_3
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_4
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_8
msgid "Blond Beer - Box"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_17
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_9
msgid "Blond Beer - Keg 20L"
msgstr ""

#. module: micro_brewery
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_1
msgid "Blond Beer - Keg 20L Contains 24 x 33cl"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_6
msgid "Blue"
msgstr "Mėlyna"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Boiling - Work center: Kettle"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "Book a visit now"
msgstr ""

#. module: micro_brewery
#: model_terms:appointment.type,message_intro:micro_brewery.appointment_type_1
msgid ""
"Book your spot now and raise a glass to a memorable brewery experience!"
msgstr ""

#. module: micro_brewery
#: model:product.public.category,name:micro_brewery.product_public_category_6
#: model:uom.uom,name:micro_brewery.uom_uom_29
msgid "Bottle"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Bottling - Work center: Bottler"
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.beverage_product_attribute_16
msgid "Brand"
msgstr "Prekinis ženklas"

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_3
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_10
msgid "Brown"
msgstr "Ruda"

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_21
msgid "Brown Beer"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_35_product_template
msgid "Brown Beer (25cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_36_product_template
msgid "Brown Beer (50cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_14
msgid "Brown Beer - Bottle"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_16
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_11
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_5
msgid "Brown Beer - Box"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_18
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_12
msgid "Brown Beer - Keg 20L"
msgstr ""

#. module: micro_brewery
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_3
msgid "Brown Beer - Keg 20L Contains 24 x 33cl"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_24
msgid "Brussels beer project"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"By default the \"Contact us\" page of the website will send you an email, "
"but you can edit the page and change the behavior of the submit button to "
"create a lead into the CRM application instead."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"bunch of interesting presets to ensure you can easily execute the following "
"use case."
msgstr ""

#. module: micro_brewery
#: model:product.public.category,name:micro_brewery.product_public_category_5
msgid "Case"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_21
msgid "Chaudfontaine"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"        reflected on his invoice or issue a credit note."
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.product_attribute_3
msgid "Color"
msgstr "Spalva"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "Come visit our brewery&amp;nbsp;!"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid "Contact us"
msgstr "Susisiekite su mumis"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                            We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_quantity_by_deposit_product
msgid "Contains"
msgstr "Turi"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Crafting Beer - Using mainly the <font class=\"text-o-"
"color-1\"><strong>Manufacture App</strong></font>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_01_purchase
#: model:account.tax,name:micro_brewery.account_tax_01_sale
msgid "DEP 0.1"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_21_sale
msgid "DEP 2.1"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_24_purchase
#: model:account.tax,name:micro_brewery.account_tax_24_sale
msgid "DEP 2.4"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_45_purchase
#: model:account.tax,name:micro_brewery.account_tax_45_sale
msgid "DEP 4.5"
msgstr ""

#. module: micro_brewery
#: model:base.automation,name:micro_brewery.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr ""

#. module: micro_brewery
#: model:account.tax.group,name:micro_brewery.deposit_tax_group
#: model:pos.category,name:micro_brewery.pos_category_5
msgid "Deposit"
msgstr "Depositas"

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_90
msgid "Deposit 0.1"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_56
msgid "Deposit 2.1"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_53
msgid "Deposit 2.4"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_55
msgid "Deposit 4.5"
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_deposit_product_1
msgid "Deposit product"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid ""
"Don't hesitate and come visit us!&amp;nbsp;We will show you how we produce "
"our amazing beers"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr ""

#. module: micro_brewery
#: model:account.tax,name:micro_brewery.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Ecommerce can be used to create a brand that is more than a beer. You can "
"use it to do \"cross-selling\" by adding some merchandising products "
"(t-shirt, sweet shirt, beer glass, ...) that will be bought by your fans "
"that will become your ambassadors."
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_39_product_template
msgid "Empty Keg"
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_empty_deposit
msgid "Empty deposit product"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""

#. module: micro_brewery
#: model:account.tax.group,name:micro_brewery.excises_tax_group
msgid "Excises"
msgstr ""

#. module: micro_brewery
#: model:ir.actions.server,name:micro_brewery.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:micro_brewery.bom_server_action
#: model:ir.actions.server,name:micro_brewery.update_sales_taxes_server_action
msgid "Execute Code"
msgstr ""

#. module: micro_brewery
#: model:ir.actions.server,name:micro_brewery.update_state
msgid "Execute Existing Actions"
msgstr ""

#. module: micro_brewery
#: model_terms:appointment.type,message_intro:micro_brewery.appointment_type_1
msgid ""
"Explore the art of craft beer with our 90 minutes guided brewery visit. Get "
"a behind-the-scenes look at our brewing process, sample our best brews, and "
"learn from our experts."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Fermenting - Work center: Fermentation Vessel"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Filtering - Work center: Fermentation Vessel"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "Flavor intensity"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid "Flavor intensity<br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.ir_ui_view_2543
msgid "Follow us"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "For example:"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid ""
"From inventory management and production planning to sales and customer relationship, Odoo empowers O-Beer with the tools to optimise\n"
"                                    their processes, enhance production quality, and ensure a smooth and successful brewing journey."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"From the Sales application, you can create standardized offer to make the "
"creation of offers more efficient. For example you can have a template with "
"all your beers already in it. You can also create pricelists that you can "
"assign on your customers too;"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"From the website application, you can access the Navigate on the page "
"\"Shop\". You will see that your products may not be published yet. Click on"
" a product to land on the product page, customize it then publish it so "
"visitors can start ordering it."
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_25_product_template
msgid "Glass"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Go into the purchase application and create a new purchase order with all "
"the components you need to execute your recipe. Make sure you buy enough "
"quantity of each components and validate the order."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Go on the delivery order and indicate the quantity you received and create "
"the lot numbers for each components."
msgstr ""

#. module: micro_brewery
#: model:product.public.category,name:micro_brewery.product_public_category_2
msgid "Goodies"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_7
msgid "Green"
msgstr "Žalia"

#. module: micro_brewery
#: model:uom.uom,name:micro_brewery.uom_uom_31
msgid "Half-Pint"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Hello there!"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_5_product_template
msgid "Hops"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Hops - Goods, inventory tracking by lot."
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.beverage_product_attribute_14
msgid "IPA"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"If you need to declare your <strong>periodic sales in a consolidated "
"volume</strong>, ensure your unit of measure and products are correctly "
"configured and use the Pivot Table in Sales App &gt; Reporting &gt; Sales."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Use Case."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"If you want to rent Beer Taps to your customer, you can explore the rental "
"App and discover how you can manage your rental schedule easily and provide "
"a great extended service to your customers."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"In the Appointment app, you can create an agenda for the visits of the "
"brewery. You can mention which employee will do the visit and you can ask an"
" upcoming payment to book and link the product \"Visit of the brewery\". "
"Once everything is fine, you can publish it and then hit the button \"Go to "
"Website\" to see the website page where future visitors can book online a "
"visit of the brewery."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"In the manufacturing application, you can create a bill of materials to "
"define how the beer should be crafted."
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.x_is_a_deposit
msgid "Is a deposit"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"It could also be interesting for your customer to buy souvenirs, gifts or "
"simply some pack of beers after the visit. You can have both a "
"bar/restaurant and a shop or you can manage both from the same"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"It is interesting to mention that a lot of microbreweries do not have the "
"machines for the kegging but it is possible to create a separate bill of "
"Materials with the type subcontracting, if the bottling is made externally."
msgstr ""

#. module: micro_brewery
#: model:product.public.category,name:micro_brewery.product_public_category_4
#: model:uom.uom,name:micro_brewery.uom_uom_28
msgid "Keg"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_22
msgid "Lager Beer"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_38_product_template
msgid "Lager Beer (25cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_37_product_template
msgid "Lager Beer (50cl)"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_13
msgid "Lager Beer - Bottle"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_15
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_14
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_6
msgid "Lager Beer - Box"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_19
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_15
msgid "Lager Beer - Keg 20L"
msgstr ""

#. module: micro_brewery
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_2
msgid "Lager Beer - Keg 20L Contains 24 x 33cl"
msgstr ""

#. module: micro_brewery
#: model:sale.order.template.line,name:micro_brewery.sale_order_template_line_13
msgid "Lagger"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Lautering - Work center: Lauter tun"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "Learn more"
msgstr "Sužinoti daugiau"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr ""

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.beverage_product_attribute_13
msgid "Local Brand"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_4_product_template
msgid "Malt"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Malt - Goods, inventory tracking by lot. <br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Manage Internal Projects and research and development in the <strong><font "
"class=\"text-o-color-1\">Project App</font></strong>."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font "
"class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font"
" class=\"text-o-color-1\">Email Marketing</font></strong>, <strong><font "
"class=\"text-o-color-1\">Social Media Marketing</font></strong>, and "
"<strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Mashing - Work center: Mash tun"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_12
msgid "Mobius"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_7
msgid "Mobius Blanche 33cl"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_60
msgid "Mobius IPA 33cl"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Mobius Triple - 24x33cl is your main product"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Mobius Triple - 33cl is the unit sale product"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_62
msgid "Mobius Triple 33cl"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid "My Company"
msgstr "Mano įmonė"

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.x_volume_sold
msgid "New Decimal"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_16
msgid "No"
msgstr "Ne"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Now go back in the \"Point of Sales\" dashboard and start a Session of the "
"bar (or restaurant), indicate how much cash you have when opening the bar. "
"An now you are ready to take an order."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Now if you go back on the quotation in the Sales application, you will see "
"that it is now a confirmed order and a delivery order has been created. Go "
"on that delivery order and validate it."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Now that the recipe is ready, you can create a manufacturing order but you "
"will see that if you don't have the components to create your beer, you will"
" need to purchase the required components."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Now that you have all the components required to craft the beer, let's start"
" brewing beer. In the manufacturing application, create a new manufacturing "
"order for the bill of materials you made for your recipe."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Now you can generate a new lead by submitting the form and go into the CRM "
"application to find this new lead. As the salesperson in charge of this "
"lead, you can exchange email with the customer directly with the chatter and"
" finally you can schedule a meeting with him."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Odoo for Microbrewery"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"On top of all the reporting you can find in the different applications, you "
"can consult the dashboard application to follow the volume of Sales you have"
" made."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Once it is confirmed a delivery order should have been created meaning you "
"are waiting for the vendor to deliver the goods."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Once you finished all the operations, you should now be ready to sell your "
"crafted beer."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""

#. module: micro_brewery
#: model:website.menu,name:micro_brewery.website_menu_11
msgid "Our Beers"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid ""
"Our Blond beer is a radiant and approachable brew that radiates sunshine in every sip. With its light, straw-colored appearance and a hint of citrus and floral notes, this beer is the embodiment of a carefree, laid-back lifestyle. It's a splendid choice for those who appreciate a touch of sweetness and a gentle, well-balanced finish. Embrace the joy of our Blond and let your taste buds dance with delight.\n"
"\n"
"<br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid ""
"Our Brown beer is a rich and flavorful masterpiece that exudes warmth and comfort. With its deep mahogany hue and a complex blend of roasted malt and caramel flavors, this beer offers a robust and satisfying experience. Perfect for those who crave depth and character in their brew, our Brown beer invites you to savor each sip, revealing layers of nutty, toasty goodness. Discover the comforting embrace of Brown beer today.\n"
"<br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.our-beers
msgid ""
"Our Lager is the epitome of refreshment. Crisp, clean, and delightfully "
"balanced, it's the perfect choice for those seeking a classic and timeless "
"beer experience. With a golden hue and a mild hop aroma, this lager offers a"
" smooth, easy-drinking profile that's great for any occasion. Raise a glass "
"to tradition and savor the pure, uncomplicated taste of Lager."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Outside Sales (B2B - Bar &amp; Retail) - Using the <strong><font "
"class=\"text-o-color-1\">Sales App</font></strong>"
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_parent_product_bom
#: model:ir.model.fields,field_description:micro_brewery.field_parent_product_rr
msgid "Parent product"
msgstr ""

#. module: micro_brewery
#: model:uom.uom,name:micro_brewery.uom_uom_30
msgid "Pint"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_8
msgid "Red"
msgstr "Raudona"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Register the quantity of spent grain produced at the end of the lautering."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.micro_brewery_custom_appointment_heading
msgid "Schedule <span style=\"color:#714b67;\">Visit of Brewery</span>"
msgstr ""

#. module: micro_brewery
#: model_terms:appointment.type,message_confirmation:micro_brewery.appointment_type_1
msgid "See you soon"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Select a table (inside or outside), then select the ordered products and "
"confirm the order."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr ""

#. module: micro_brewery
#: model:pos.category,name:micro_brewery.pos_category_3
msgid "Shop"
msgstr "Parduotuvė"

#. module: micro_brewery
#: model:product.attribute,name:micro_brewery.product_attribute_4
msgid "Size"
msgstr "Dydis"

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_10_product_template
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Spent Grain"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_4
msgid "Stout"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.contactus
msgid "Submit"
msgstr "Pateikti"

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_7_product_template
msgid "Sugar"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_24
msgid "Sweat Shirt"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_template_23
msgid "T-Shirt"
msgstr ""

#. module: micro_brewery
#: model_terms:appointment.type,message_confirmation:micro_brewery.appointment_type_1
msgid ""
"Thank you for visiting us, we are waiting for you now and hope you will "
"enjoy the visit and our beers!"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"The appointment app can also be used to share your availability with a "
"customer or a vendor if you want him to book a meeting with you."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "The perfect beverage to share with your friends."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Then <strong>create and add the components </strong>you need, the quantity "
"required and you can also indicate in which operation it will be consumed, "
"for example:"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Then, from the \"Shop Floor\" application, execute the different operations "
"and follow the instruction as you were the operator."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"This article will guide you through the business flows of a Microbrewery to "
"see how Odoo can support all these critical activities for a microbrewery to"
" help people so they can be efficient and spend more time on what matters."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""

#. module: micro_brewery
#: model:ir.model.fields,field_description:micro_brewery.field_unit_sale_product
msgid "Unit sale product"
msgstr ""

#. module: micro_brewery
#: model:base.automation,name:micro_brewery.update_sales_taxes
msgid "Update Taxes"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Validate the delivery and check your inventory, now you have everything"
msgstr ""

#. module: micro_brewery
#: model:appointment.type,name:micro_brewery.appointment_type_1
msgid "Visit of Brewery"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"Visit of the Brewery (B2C) - Managed via the <strong><font class=\"text-o-"
"color-1\">Appointment App</font></strong>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Want to know more about unit of measures ?"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_9_product_template
#: model:product.template,name:micro_brewery.product_template_9
msgid "Water"
msgstr "Vanduo"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Water - Good, no inventory tracking - Mashing"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "We Brew beer with Odoo"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid ""
"Welcome to o-beer, where craft meets flavor! Our brewery is dedicated to creating exceptional beers that ignite the senses and captivate the palate.\n"
"                                    With our diverse selection and nationwide shipping, enjoying our handcrafted beers has never been easier.\n"
"                                    Cheers to unforgettable brews and memorable moments!"
msgstr ""

#. module: micro_brewery
#: model_terms:web_tour.tour,rainbow_man_message:micro_brewery.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_4
msgid "White"
msgstr "Baltas"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_13
msgid "XL"
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.product_attribute_value_9
msgid "XS"
msgstr ""

#. module: micro_brewery
#: model:product.template,name:micro_brewery.product_product_6_product_template
msgid "Yeast"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "Yeast - Goods, inventory tracking by lot."
msgstr ""

#. module: micro_brewery
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_15
#: model:product.attribute.value,name:micro_brewery.beverage_product_attribute_value_25
msgid "Yes"
msgstr "Taip"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You can configure a monthly sales overview using the \"Volume\" Measure, "
"selecting \"Product categories\" in X axis and \"Order date &gt; Month\" in "
"Y axis."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "You can now finalize the flow by creating the invoice."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "You can pick a time and pay to finalize the booking."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You can then use the \"Preview\" button on the offer to see the result and "
"accept it as if you were the customer!"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Microbrewery package and check the related box."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.homepage
msgid "You don't know what to do next weekend?!"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You just installed the Odoo for Microbrewery package. By doing so, we have "
"installed a bunch of necessary apps to run your Microbrewery efficiently."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "academy"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "and"
msgstr "ir"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "documentation"
msgstr "dokumentacija"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr ""

#. module: micro_brewery
#: model:uom.uom,name:micro_brewery.uom_uom_27
msgid "hL"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "if you need help! <br/>"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.ir_ui_view_2543
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "request a demo"
msgstr ""

#. module: micro_brewery
#: model_terms:ir.ui.view,arch_db:micro_brewery.welcome_article_body
msgid "🎓 Units of measure"
msgstr ""
