# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bike_leasing
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Halil, 2024
# emre <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:09+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: bike_leasing
#: model:product.template,description_sale:bike_leasing.product_template_10
msgid "24/7 assistance: <EMAIL>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid ""
"<b class=\"o_default_snippet_text\">Our Partner</b>\n"
"                                            <br/>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "<b>A greener lifestyle</b>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "<b>The world is yours</b>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.product
msgid "<i class=\"fa fa-shopping-cart me-2\"/> Get a quote"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.ir_ui_view_3209
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp;nbsp;QFR Bike "
"Leasing</span>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.homepage
msgid ""
"<strong class=\"o_default_snippet_text\">The best cargo bike</strong>\n"
"                                            <br/>"
msgstr ""

#. module: bike_leasing
#: model:product.attribute,name:bike_leasing.product_attribute_3
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Accessories"
msgstr "Aksesuarlar"

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_1
msgid "Anthracite"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid ""
"Around 80% of the value of the bicycles comes from components made in "
"Europe. The frames are meticulously hand-welded in Eastern Europe using "
"high-quality chromoly steel. The engines are sourced from Shimano and Bosch,"
" while the spoking of the bicycle wheels is handled in Brittany."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"As soon as the quotation is confirmed, a delivery is automatically created in the <strong><font class=\"text-o-color-1\">Inventory</font></strong> app. This is essential to track which bike is rent. During the delivery process, you will\n"
"                have to fill the serial number of the bike."
msgstr ""

#. module: bike_leasing
#: model:product.template,name:bike_leasing.product_template_10
msgid "Assistance"
msgstr "Yardım"

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_7
#: model:product.template,name:bike_leasing.product_template_7
msgid "Bench"
msgstr ""

#. module: bike_leasing
#: model:product.template,name:bike_leasing.product_template_6
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid "Bike 43"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid ""
"Bike 43 proudly assembles its bicycles in Brussels. Since starting "
"production in 2016, the company has collaborated with an adapted workplace "
"in Brussels, providing employment and reintegration opportunities for "
"handicapped and socially impaired individuals. Guided by Etienne, the team "
"primarily composed of hearing-impaired individuals—has developed an "
"exceptional mechanic skills."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Bike Leasing"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Bike Leasing Industry"
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_6
msgid "Bosh Carho Line (85NM)"
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_3
msgid "Broom Yellow"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Business Flows"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Catalogue"
msgstr ""

#. module: bike_leasing
#: model:product.attribute,name:bike_leasing.product_attribute_1
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Color"
msgstr "Renk"

#. module: bike_leasing
#: model:base.automation,name:bike_leasing.base_automation_2
msgid "Create Return Activity on Delivery"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Customer"
msgstr "Müşteri"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Deliver the order in time 🚚"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Discover more"
msgstr "Daha fazlasını keşfet"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Display the different combinations of bikes on your <strong><font "
"class=\"text-o-color-1\">eCommerce</font></strong>, such as:"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"During the leasing period, provide a five star customer service by answering the questions and providing assistance to your customer through the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Helpdesk</font>\n"
"                </strong>\n"
"                app. All the emails sent to"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Electric Driving"
msgstr ""

#. module: bike_leasing
#: model:ir.actions.server,name:bike_leasing.ir_act_server_601
#: model:ir.actions.server,name:bike_leasing.ir_act_server_730
#: model:ir.actions.server,name:bike_leasing.ir_act_server_731
msgid "Execute Code"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Extra services such as assistance, warranty extension, etc."
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_9
#: model:product.template,name:bike_leasing.product_template_9
msgid "Front Basket"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.homepage
msgid "Get it from 90€/month&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"If the stock levels are too low, resupply the materials from the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Purchase</font>\n"
"                </strong>\n"
"                app by creating a purchase order linked to the right vendor. In the\n"
"                <strong>\n"
"                        <font class=\"text-o-color-1\">Contact</font>\n"
"                </strong>\n"
"                app, you can easily manage all your preferred vendors and don't hesitate to set a warning to remind if there is a minimum amount to get free transport costs."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Learn more"
msgstr "Daha fazla bilgi edin"

#. module: bike_leasing
#: model:ir.model.fields,field_description:bike_leasing.x_mrr_monetary_field
msgid "MRR"
msgstr "MRR"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Make your customers happy 🆘"
msgstr ""

#. module: bike_leasing
#: model:product.pricelist,name:bike_leasing.product_pricelist_2
msgid "Monthly"
msgstr "Aylık"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Moreover, an activity is automatically created to remind you the return of "
"the product. This is planned at the same date as the deadline of the rental "
"contract."
msgstr ""

#. module: bike_leasing
#: model:product.attribute,name:bike_leasing.product_attribute_2
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Motor"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "No compromise"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Odoo empowers you to effortlessly and efficiently manage every aspect of "
"your rentals, ensuring unparalleled success and client satisfaction thanks "
"its suite of integrated applications.<br/>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Of course, you can also create a beautiful and tailor-made quotation in the "
"<strong><font class=\"text-o-color-1\">Sales</font></strong> app by setting "
"the following fields:"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.homepage
msgid ""
"Our bikes are designed in-house. Our frames are hand built in Europe, by "
"specialized and experienced craftsmen. Our bikes are then assembled with "
"care in Brussels according to the best quality standard. Local production is"
" aimed at limiting the impact on mother earth. We also believe in an "
"inclusive approach with adapted workers in our Brussels-based assembly "
"line.<br/>"
msgstr ""

#. module: bike_leasing
#: model:website.menu,name:bike_leasing.website_menu_13
msgid "Partnership"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.sale_order_custom_form_view
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Pricelist / Recurring"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Products"
msgstr "Ürünler"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Provide accurate online quotations 📝"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Purchase to face the demand 🚲"
msgstr ""

#. module: bike_leasing
#: model:product.pricelist,name:bike_leasing.product_pricelist_3
msgid "Quarterly"
msgstr "Üç Aylık"

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_8
#: model:product.template,name:bike_leasing.product_template_8
msgid "Rain Canopy"
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_2
msgid "Raspberry"
msgstr ""

#. module: bike_leasing
#: model:ir.model.fields,field_description:bike_leasing.x_auto_post_pricelist_field
#: model_terms:ir.ui.view,arch_db:bike_leasing.account_move_custom_form_view
msgid "Recurring"
msgstr "Yinelenen"

#. module: bike_leasing
#: model:ir.model.fields,field_description:bike_leasing.x_so_id_field
msgid "Sales Order"
msgstr "Satış Siparişi"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Send effortless recurring invoices 🔁"
msgstr ""

#. module: bike_leasing
#: model:base.automation,name:bike_leasing.base_automation_1
msgid "Set Recurring Plan from SO"
msgstr ""

#. module: bike_leasing
#: model:base.automation,name:bike_leasing.base_automation_3
msgid "Set Source SO on Recurring Invoice"
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_5
msgid "Shimano E6100 (65NM)"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Size"
msgstr "Boyut"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Müşteri ile başlayın - ne istediklerini bulun ve onlara verin."

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.sale_order_custom_tree_view
msgid "Sum of MRR"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid "Take advantage of a free track trial to discover it."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Thanks to the auto-post feature, the recurring invoices will be "
"automatically generated from the sales order. So, you just need to send them"
" to the customer according to your preferred method: email, snail mail, "
"e-invoicing, etc."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid ""
"The vision for local production was a founding principle of Bike 43, setting"
" it apart in an industry predominantly based in Asia. This strategic choice "
"allows for rapid iteration and continuous enhancement of product designs. "
"The company has developed over 30 versions of its frames and continuously "
"innovated its ecosystem of accessories, focusing on safety for children, "
"convenience for families, and ease of assembly for mechanics."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Thereby, the customer can configure its bike then get an accurate quote. On your side, this quote is created in the <strong><font class=\"text-o-color-1\">Sales</font></strong> app where you can discuss with the customer using the\n"
"                chatter. It is really useful to define the terms of the renting contract such as the delivery date, the rent duration, etc."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"This can be sent by email to your customer who will be able to confirm it "
"with a signature."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"This industry is tailor-made for bike rental businesses that focus on long "
"term rental contracts. It covers all the crucial stages of the renting "
"process, from lead acquisition to recurring invoicing."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid ""
"Today, we are recognized as a visionary in the industry, having been at the "
"forefront of the movement to repatriate manufacturing, spurred by global "
"logistical challenges and rising costs.<br/>"
msgstr ""

#. module: bike_leasing
#: model:product.attribute.value,name:bike_leasing.product_attribute_value_4
msgid "Turquoise"
msgstr "Turkuaz"

#. module: bike_leasing
#: model:ir.model.fields,field_description:bike_leasing.x_auto_post_until_field
#: model_terms:ir.ui.view,arch_db:bike_leasing.account_move_custom_form_view
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "Until"
msgstr "Bitiş"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid ""
"Use the variants and the optional products features to make sure that your customer will find the most suitable bike. Moreover, the customer can choose the recurring plan, i.e. Monthly, Quarterly or Yearly. The corresponding prices must\n"
"                be defined in the three predefined \"selectable\" pricelists."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.partnership
msgid "Visit their website"
msgstr ""

#. module: bike_leasing
#: model_terms:web_tour.tour,rainbow_man_message:bike_leasing.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.catalogue
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Ürün veya hizmetlerinizi açıklayan bir veya iki paragraf yazın. Başarılı "
"olmak için içeriğinizin okuyucularınız için yararlı olması gerekir."

#. module: bike_leasing
#: model:product.pricelist,name:bike_leasing.product_pricelist_4
msgid "Yearly"
msgstr "Yıllık"

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "<EMAIL>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "will create a ticket that can be assigned to ensure the follow-up."
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "🎓 <font class=\"text-o-color-1\">eCommerce</font>"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "🎓 Helpdesk"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "🎓 Inventory"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "🎓 Purchase"
msgstr ""

#. module: bike_leasing
#: model_terms:ir.ui.view,arch_db:bike_leasing.welcome_article_body
msgid "🎓 Sales"
msgstr ""
