# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hair_salon
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-22 14:07+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hair_salon
#: model:loyalty.reward,description:hair_salon.loyalty_reward_4
msgid "$ 20 on your order"
msgstr "$20 en su orden"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"<font class=\"text-o-color-3\">Schedule an\n"
"                                                                                    appointment\n"
"                                                                            </font>"
msgstr ""
"<font class=\"text-o-color-3\">Programe una\n"
"                                                                                    cita\n"
"                                                                            </font>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "<span class=\"display-2-fs\">Step into our salon</span>"
msgstr "<span class=\"display-2-fs\">Visite nuestra peluquería</span>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<span class=\"display-4-fs\"><strong>Odoo for Hair Salons</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo para peluquerías</strong></span>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<span class=\"h1-fs\">Basics:&amp;nbsp;</span>"
msgstr "<span class=\"h1-fs\">Lo esencial:&amp;nbsp;</span>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "<span class=\"h1-fs\">Do you want to go further?&amp;nbsp;🚀</span>"
msgstr "<span class=\"h1-fs\">¿Quiere llegar más lejos?&amp;nbsp;🚀</span>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Allow clients to book appointments online"
msgstr "Deje que sus clientes hagan citas en línea"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Appointment Scheduling &amp;amp; calendar 📆&amp;nbsp;"
msgstr "Programación de citas y calendario 📆&amp;nbsp;"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Basics:&amp;nbsp;"
msgstr "Lo esencial:&amp;nbsp;"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_6
msgid "Beard Shaping"
msgstr "Arreglo de barba"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_8
msgid "Beard Shaping JP"
msgstr "Arreglo de barba JP"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"Because nothing is impossible, and hair transformations are the salon's\n"
"                                                                            specialty, at Hair Salon Industry, every customer is treated as an\n"
"                                                                            individual.&amp;nbsp;"
msgstr ""
"Porque nada es imposible y transformar el estilo de tu cabello es la\n"
"                                                                            especialidad de nuestro salón, en el Sector de la Peluquería tratamos a cada cliente\n"
"                                                                            de la mejor manera.&amp;nbsp;"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Book your appointment"
msgstr "Agende su cita"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.x_break
msgid "Break"
msgstr "Descanso"

#. module: hair_salon
#: model:base.automation,name:hair_salon.delete_subpart_calendar_event
msgid "Break Appointment"
msgstr "Cita en descanso"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.calendar_x_break_end
#: model:ir.model.fields,field_description:hair_salon.x_break_end_appointment_model
msgid "Break End"
msgstr "Fin del descanso"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.calendar_x_break_start
#: model:ir.model.fields,field_description:hair_salon.x_break_start_appointment_model
msgid "Break Start"
msgstr "Inicio del descanso"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_3
#: model:product.template,name:hair_salon.product_product_27_product_template
msgid "Brushing"
msgstr "Peinado"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_7
msgid "Brushing JP"
msgstr "Peinado JP"

#. module: hair_salon
#: model:pos.category,name:hair_salon.pos_category_4
msgid "Cares"
msgstr "Cuidados"

#. module: hair_salon
#: model:product.pricelist,name:hair_salon.product_pricelist_2
msgid "Color & Haircut & Brushing"
msgstr "Color, corte de cabello y peinado"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_20
msgid "Conditioner - 500ml"
msgstr "Acondicionador - 500 ml"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Configure Appointment Types: In the Appointment app, create types that match"
" your services."
msgstr ""
"Configurar los tipos de citas: en la aplicación Cita, cree los tipos que "
"vayan con sus servicios."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Configure loyalty programs or promotions within your Point of Sale."
msgstr ""
"Configure los programas de lealtad o las promociones en su Punto de venta."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Create an attractive website to display your services"
msgstr "Cree un sitio web atractivo para mostrar sus servicios"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Create and manage your salon services and products:"
msgstr "Cree y gestione los servicios y productos de su salón:"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Create loyalty programs and gift cards."
msgstr "Cree programas de lealtad y tarjetas de regalo."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Define pricing for each service and product."
msgstr "Defina el precio de cada servicio y producto."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Define your stylists' availability"
msgstr "Defina la disponibilidad de sus estilistas"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Design Your Website: Use the Website app to create an attractive online "
"presence for your salon."
msgstr ""
"Diseñe su sitio web: use la aplicación de Sitio web para crear una presencia"
" en línea atractiva para su sitio web."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Discover more"
msgstr "Más información"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Discover your best look at our innovative hair salon."
msgstr "Descubre cuál es tu mejor look en nuestro innovador salón."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Do you want to go further?&amp;nbsp;🚀"
msgstr "¿Quiere conocer más?&amp;nbsp;🚀"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Elevate your hair game."
msgstr "Reinvente su peinado."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Encourage clients to book through your online Appointment page to reduce "
"your efforts.&amp;nbsp;"
msgstr ""
"Incentive a sus clientes a hacer sus citas desde la página Citas para "
"reducir su trabajo."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using Email Marketing and Social "
"Marketing."
msgstr ""
"Interactúe con los clientes mediante campañas enfocadas desde Marketing por "
"correo electrónico y Marketing social."

#. module: hair_salon
#: model:ir.actions.server,name:hair_salon.break_subpart_calendar_event_server_action
msgid "Execute Code"
msgstr "Ejecutar código"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"Experience the ultimate transformation at our hair salon, where style meets\n"
"                                                                            innovation, and every strand tells a story of beauty.\n"
"                                                                            <br/>"
msgstr ""
"Disfrute la mejor transformación de su peinado en nuestro salón, donde el estilo y la innovación \n"
"                                                                            se encuentran y cada mechón cuenta una historia de belleza.\n"
"                                                                            <br/>"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Experienced hairstylists"
msgstr "Estilistas con experiencia"

#. module: hair_salon
#: model:loyalty.program,name:hair_salon.loyalty_program_4
msgid "Fidelity Cards - 20€ off after 5 visits of 50€"
msgstr "Tarjetas de lealtad - €20 de descuento después de 5 visitas de €50"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Gather client feedback and preferences using the Survey app."
msgstr ""
"Con la aplicación Encuestas podrá pedir retroalimentación de los clientes-"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Getting Started"
msgstr "Primeros pasos"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_product_26_product_template
msgid "Gift Card"
msgstr "Tarjeta de regalo"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_5
msgid "Hair Coloring"
msgstr "Teñido de cabello"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_23
msgid "Hair Dye Kit"
msgstr "Kit de tinte para el cabello"

#. module: hair_salon
#: model:product.attribute,name:hair_salon.product_attribute_1
msgid "Hair Length"
msgstr "Longitud del cabello"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_22
msgid "Hair Spray - 250ml"
msgstr "Aerosol para el cabello - 250 ml"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_21
msgid "Hair Styling Gel - 250ml"
msgstr "Gel para el cabello - 250 ml"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_1
msgid "Haircut & Brushing"
msgstr "Corte de cabello y peinado"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_2
msgid "Haircut & Color & Brushing"
msgstr "Corte de cabello, color y peinado"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Handle walk-ins and product sales efficiently:"
msgstr ""
"Gestiona clientes de la tienda y ventas de productos de manera eficiente:"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"Here, more than anywhere else, we take the time to listen and advise, in\n"
"                                                                            order to offer a moment of relaxation and complete satisfaction at the end\n"
"                                                                            of the service."
msgstr ""
"Aquí nuestra prioridad es escuchar y dar consejos para ofrecerle\n"
"                                                                            un momento de relajación y completa satisfacción al final\n"
"                                                                            del servicio."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"If you want to completely avoid no-shows, you can set a pre-payment for "
"online booking, by linking each appointment type with a specific product and"
" defining a payment method."
msgstr ""
"Si quiere evitar por completo que los clientes no se presenten, puede "
"configurar un pago previo al momento de hacer la cita. Solo debe vincular "
"cada tipo de cita a un producto específico y definir un método de pago."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"If you want to optimize your planning, you can also set breaks in "
"appointment."
msgstr ""
"Si quiere optimizar su planeación también puede configurar descansos en la "
"aplicación Citas."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Implement a loyalty program to reward repeat customers using the eCommerce "
"features."
msgstr ""
"Con las funciones de Comercio electrónico puede configurar programas de "
"lealtad para recompensar a clientes frecuentes."

#. module: hair_salon
#: model:ir.model.fields,help:hair_salon.x_break
msgid "Insert a free time in your booking."
msgstr "Inserte un tiempo libre en su reservación."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Learn more"
msgstr "Más información"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr ""
"Deje que los clientes hagan una cita directamente desde su página de citas"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.x_parent_id
msgid "Linked to"
msgstr "Vinculado a"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.calendar_event_view_form_gantt_booking_inherit
msgid "Linked to:"
msgstr "Vinculado a:"

#. module: hair_salon
#: model:product.attribute.value,name:hair_salon.product_attribute_value_1
msgid "Long hair"
msgstr "Cabello largo"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Manage Your Inventory: Input your hair care products and stock levels in the"
" Inventory app."
msgstr ""
"Gestione su inventario: ingrese sus productos para el cuidado del cabello y "
"el nivel de stock en la aplicación Inventario."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Manage cash and card payments"
msgstr "Gestione pagos con efectivo o tarjeta"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Manage inventory for hair care products and replenish with the Purchase App."
msgstr ""
"Gestione el inventario de sus productos de cuidado para el cabello y el "
"reabastecimiento desde la aplicación Compra."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Manage your staff's booking directly the Appointment App."
msgstr ""
"Gestione las citas de su personal directamente en la aplicación Citas."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Manage your team of stylists efficiently with the Employees and Planning "
"apps."
msgstr ""
"Gestione a los estilistas que conforman su equipo con las aplicaciones "
"Empleados y Planeación."

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_4
#: model:product.template,name:hair_salon.product_product_30_product_template
msgid "Men's Haircut"
msgstr "Corte de cabello para caballero "

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_5
msgid "Men's Haircut + Beard Shaping"
msgstr "Corte de cabello para caballero + Arreglo de barba"

#. module: hair_salon
#: model:appointment.type,name:hair_salon.appointment_type_9
msgid "Men's Haircut JP"
msgstr "Corte de cabello para caballero JP"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Monitor your inventory levels closely to ensure you have popular products in"
" stock."
msgstr ""
"Controle sus niveles de inventario para asegurarse de que dispone de "
"productos populares en stock."

#. module: hair_salon
#: model:product.template,name:hair_salon.product_product_7_product_template
msgid "Mustache and beard shaping"
msgstr "Diseño de bigote y barba"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Natural products"
msgstr "Productos naturales"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Odoo offers you infinite possibilities to enhance your hair salon business, "
"such as:"
msgstr ""
"Odoo le ofrece infinitas posibilidades para llevar su peluquería al "
"siguiente nivel, como:"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Organize hair styling workshops or product demonstrations with the Events "
"app."
msgstr ""
"Con la aplicación Eventos podrá organizar talleres de corte de cabello o "
"demostraciones de productos."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Our Mission"
msgstr "Nuestra misión"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"Our hairstylists are experienced and able to give\n"
"                                                                                            you the best advices and haircuts."
msgstr ""
"Nuestros estilistas tienen mucha experiencia y pueden ofrecerle\n"
"                                                                                            los mejores consejos y cortes de cabello."

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.x_appointment_part_1
msgid "Part 1"
msgstr "Parte 1"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.x_appointment_part_2
msgid "Part 2"
msgstr "Parte 2"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Point of Sale 💳&amp;nbsp;"
msgstr "Punto de venta 💳&amp;nbsp;"

#. module: hair_salon
#: model:loyalty.program,portal_point_name:hair_salon.loyalty_program_4
msgid "Points"
msgstr "Puntos"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Process quick transactions for services and products"
msgstr "Procese transacciones rápidas para servicios y productos"

#. module: hair_salon
#: model:pos.category,name:hair_salon.pos_category_3
msgid "Products"
msgstr "Productos"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_25
msgid "Professional Conditionner 2L"
msgstr "Acondicionador profesional 2 L"

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_24
msgid "Professional Shampoo 2L"
msgstr "Shampoo profesional 2 L"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Reach us"
msgstr "Contáctenos"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific salon needs. Don't"
" hesitate to explore and customize as your business grows!"
msgstr ""
"Recuerde que Odoo es flexible y se puede adaptar a las necesidades de su "
"salón. ¡No dude en explorar y personalizar la plataforma conforme su empresa"
" crece!"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.header_call_to_action
msgid "Schedule an appointment"
msgstr "Agendar cita"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Services and Product Management 💇‍♀️&amp;nbsp;"
msgstr "Gestión de servicios y productos 💇‍♀️&amp;nbsp;"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Set Up Your Point of Sale: Configure your POS system for efficient in-salon "
"transactions."
msgstr ""
"Configure su Punto de venta: configure su sistema de PdV para transacciones "
"eficientes dentro del salón."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Set Up Your Services: Navigate to Sales &amp;gt; Products and set up your "
"hair salon services and products."
msgstr ""
"Configure sus servicios: vaya a Ventas &amp;gt; Productos y configure los "
"servicios y productos de su salón."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Set up Reminders on appointments to reduce no-show rate."
msgstr ""
"Configure recordatorios en sus citas para reducir la tasa de clientes que no"
" se presentan."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Set up appointment types (e.g., \"Women's Haircut\", \"Hair Coloring\")"
msgstr ""
"Configure tipos de citas (por ejemplo, \"Corte de cabello para dama\", "
"\"Teñido de cabello\")."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Set up services like \"Women's Haircut\" or \"Hair Coloring\""
msgstr ""
"Configure servicios como \"Corte de cabello para dama\" o \"Teñido de "
"cabello\""

#. module: hair_salon
#: model:product.template,name:hair_salon.product_template_6
msgid "Shampoo - 500ml"
msgstr "Shampoo - 500 ml"

#. module: hair_salon
#: model:product.attribute.value,name:hair_salon.product_attribute_value_2
msgid "Short hair"
msgstr "Cabello corto"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Showcase your salon and enable online bookings:"
msgstr "Exponga su salón y active las reservas en línea:"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Showcase your stylists and their specialties"
msgstr "Demuestra las habilidades y especialidades de sus estilistas"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"Start Booking Appointments: Begin using the Appointment app to schedule "
"client services."
msgstr ""
"Comience a recibir reservas de citas: comience a usar las aplicaciones Citas"
" para programar los servicios de los clientes."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "The Hair Salon&amp;nbsp;Concept"
msgstr "El concepto de nuestro salón"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"These additional features are available in your current subscription; feel "
"free to explore and expand your salon's capabilities! 🙃"
msgstr ""
"Estas funciones adicionales están disponibles con su suscripción actual. "
"Explore y expanda las capacidades de su salón. 😊"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"This guide will help you navigate the key features and get your salon "
"business up and running efficiently. From&amp;nbsp;managing appointments, "
"selling services and products, managing your inventory, to&amp;nbsp;building"
" your online presence, this package has everything you need to streamline "
"your operations and delight your clients."
msgstr ""
"Esta guía le ayudará a descubrir las funciones principales y configurar su "
"salón de inmediato. Desde&amp;nbsp;la gestión de citas, venta de servicios y"
" productos, gestión de inventario, hasta&amp;nbsp;la construcción de su "
"presencia en línea, este paquete cuenta con todo lo que necesitará para "
"agilizar sus operaciones y deleitar a sus clientes."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid ""
"This will split an appointment to free the planning during the break. This "
"way, you can start a Coloring, take another client, and return&amp;nbsp;to "
"your first client right after."
msgstr ""
"Esto hará que divida una cita para liberar la planeación durante el "
"descanso. Así podrá comenzar un Teñido, aceptar a otro cliente y "
"regresar&amp;nbsp;al primer cliente después."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Tips for Success"
msgstr "Consejos para el éxito"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Track daily sales and reconcile at closing"
msgstr "Monitorear ventas diarias y conciliar al momento del cierre"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "Unisex"
msgstr "Unisex"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Use the Appointment app to allow clients to book hair services easily:"
msgstr ""
"Use la aplicación Citas para que sus clientes puedan reservar sus servicios "
"de peluquería sin problemas:"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Use the POS system to upsell hair care products during checkout."
msgstr ""
"Use el sistema de PdV para hacer ventas adicionales en productos de cuidado "
"para cabello durante el proceso de pago."

#. module: hair_salon
#: model:pos.category,name:hair_salon.pos_category_5
msgid "Various"
msgstr "Varios"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"We offer services for long and short hair. Everyone\n"
"                                                                                            is welcome whatever their gender."
msgstr ""
"Ofrecemos servicios para cabello largo y corto. Todos\n"
"                                                                                            son bienvenidos, no importa el género."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid ""
"We work with eco-friendly natural products to\n"
"                                                                                            protect your hair and the environment."
msgstr ""
"Trabajamos con productos naturales y ecológicos\n"
"                                                                                            para proteger su cabello y el medio ambiente."

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Website and Online Booking 🌐&amp;nbsp;"
msgstr "Reservas en el sitio web y en línea 🌐&amp;nbsp;"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Welcome to your new Odoo Hair Salon package!&amp;nbsp;"
msgstr "¡Bienvenido a su nuevo paquete de Peluquería de Odoo!&amp;nbsp;"

#. module: hair_salon
#: model_terms:web_tour.tour,rainbow_man_message:hair_salon.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Le damos la bienvenida! Disfrute del sitio."

#. module: hair_salon
#: model:product.template,name:hair_salon.product_product_28_product_template
msgid "Women's Haircut"
msgstr "Corte de cabello para dama"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "¿Le gustaría que le ayudemos con su configuración de Odoo?"

#. module: hair_salon
#: model:ir.model.fields,field_description:hair_salon.x_visibility
msgid "invisibility in gantt"
msgstr "invisible en gantt"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.homepage
msgid "woman arranging the hair of woman sitting on chair"
msgstr ""
"mujer arreglando el cabello de otra mujer que está sentada en una silla"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "🎓 Point of Sale"
msgstr "🎓 Punto de venta"

#. module: hair_salon
#: model_terms:ir.ui.view,arch_db:hair_salon.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Sitio web"
