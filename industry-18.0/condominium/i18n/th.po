# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* condominium
# 
# Translators:
# <PERSON>, 2024
# Ped, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-23 11:55+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
".\n"
"            As soon as a bill is booked, you can use the button \"Distribute Cost\" to automatically reflect the cost per property according to the distribution key."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "<span class=\"fw-bold\">Address</span>"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"A condominium works like a company. It gets revenues from the owners. These "
"are collected in two bank accounts that can be directly integrated with the "
"bank:"
msgstr ""
"คอนโดมิเนียมทำงานเหมือนบริษัท โดยรับรายได้จากเจ้าของ "
"โดยรายได้จะรวบรวมไว้ในบัญชีธนาคาร 2 "
"บัญชีที่สามารถเชื่อมต่อกับธนาคารได้โดยตรง"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"A name to identify the property, for example the post box or the "
"identification code."
msgstr "ชื่อเพื่อระบุทรัพย์สิน เช่น ตู้ไปรษณีย์ หรือรหัสประจำตัว"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Accounting opening/handover<br/>"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Act analysis"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.active_buildings
#: model:ir.model.fields,field_description:condominium.active_properties_type
#: model:ir.model.fields,field_description:condominium.active_ratios_field
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Add the corresponding product(s): \"Working Fund\", \"Reserve Fund\", "
"\"Service Fees\", etc."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Again, feel free to edit and/or create new tasks to never forget a step "
"during the kick-off process."
msgstr ""
"คุณสามารถแก้ไขและ/หรือสร้างงานใหม่ได้อย่างอิสระเพื่อไม่ให้ลืมขั้นตอนใดขั้นตอนหนึ่งระหว่างกระบวนการเริ่มต้น"

#. module: condominium
#: model:product.template,name:condominium.product_product_714_product_template
msgid "Air and smoke extractors"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_713_product_template
msgid "Air conditioning and air treatment"
msgstr ""

#. module: condominium
#: model:product.template,description_sale:condominium.product_product_7_product_template
msgid ""
"All other extra services will be invoiced at the hourly rate, See Terms & "
"Condition for further information about the hourly rate"
msgstr ""
"บริการเสริมอื่นๆ ทั้งหมดจะเรียกเก็บเงินตามอัตราต่อชั่วโมง "
"ดูข้อกำหนดและเงื่อนไขสำหรับข้อมูลเพิ่มเติมเกี่ยวกับอัตราต่อชั่วโมง"

#. module: condominium
#: model:product.template,name:condominium.product_product_698_product_template
msgid "Allocations (provisions)"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_696_product_template
msgid "Amortization on intangible assets"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_697_product_template
msgid "Amortization on tangible assets"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_1
msgid "Apartment"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_12
msgid "Apartment A0"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_14
msgid "Apartment A1"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_15
msgid "Apartment A2"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_17
msgid "Apartment B0"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_19
msgid "Apartment B1.1"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_18
msgid "Apartment B1.2"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.analytic_account_custom_form_view
msgid "Apartment..."
msgstr ""

#. module: condominium
#: model:product.template,description_sale:condominium.product_product_15_product_template
#: model:product.template,description_sale:condominium.product_product_3_product_template
msgid ""
"Approximately $80 per property per month, See Terms & Condition for further "
"information about the coverage of the Service Fees"
msgstr ""
"ประมาณ 80 เหรียญสหรัฐต่อทรัพย์สินต่อเดือน "
"ดูข้อกำหนดและเงื่อนไขเพื่อดูข้อมูลเพิ่มเติมเกี่ยวกับความครอบคลุมของค่าธรรมเนียมบริการ"

#. module: condominium
#: model:product.template,name:condominium.product_product_784_product_template
msgid "Architect/engineer fees"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_search_view_building
#: model_terms:ir.ui.view,arch_db:condominium.default_search_view_for_x_ratios
#: model_terms:ir.ui.view,arch_db:condominium.default_search_view_property_type
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: condominium
#: model:product.template,name:condominium.product_product_808_product_template
msgid "Archives fees"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_area_field
#: model:ir.model.fields,field_description:condominium.x_area_related_field
msgid "Area"
msgstr "พื้นที่"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.account_move_custom_form_view
#: model_terms:ir.ui.view,arch_db:condominium.sale_order_form_custom
msgid "Area as distribution key"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Artificial Intelligence"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"As soon as a condominium reaches out to you to get a quote, quickly send them an overview of your services and fees by creating\n"
"            <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Quotations</font> in the Sales application where you can directly use the \"Kick-Off Quote\" quotation template. This will automatically fill the quote by adding the kick-off fees\n"
"            and also the price of all the extra services such as the recurring contribution, the hourly rate, etc. Don't forget to attach the terms &amp; conditions as well. Of course, the price of each service and the content of the\n"
"            <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Quotation Templates</font> can be adapted accordingly."
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.meter_image
msgid "Attachment"
msgstr "การแนบ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Audit"
msgstr "ตรวจสอบบัญชี"

#. module: condominium
#: model:product.template,name:condominium.product_product_712_product_template
msgid "Auxiliary groups"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_667_product_template
msgid "Bank fees and debt charges"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_5
msgid "Basement"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_16
msgid "Basement AB"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_753_product_template
msgid "Basement maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_710_product_template
msgid "Bell and intercom maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_811_product_template
msgid "Boiler maintenance contracts"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Book the right numbers 🧮"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_building_field
#: model:ir.model.fields,field_description:condominium.x_building_related_field
msgid "Building"
msgstr "อาคาร"

#. module: condominium
#: model:x_buildings,x_name:condominium.x_buildings_6
msgid "Building 1"
msgstr ""

#. module: condominium
#: model:x_buildings,x_name:condominium.x_buildings_7
msgid "Building 2"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_condominium_account_analytic_account_count
msgid "Building Condominium count"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_732_product_template
msgid "Building cleaning under contract"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_x_building_account_analytic_account_count_field
msgid "Building count"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.bu_act_window
#: model:ir.actions.act_window,name:condominium.buildings_act_window
#: model:ir.model,name:condominium.model_buildings
#: model:ir.ui.menu,name:condominium.building_menu
#: model:ir.ui.menu,name:condominium.conf_building_menu
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Buildings"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.buildings_tags_act_window
#: model:ir.model,name:condominium.model_buildings_tags
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building_tags
msgid "Buildings Tags"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Business Flows"
msgstr "กระแสธุรกิจ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"By the end of the period, easily generate the legal reporting such as "
"Balance Sheet and Profit &amp; Loss for each condominium."
msgstr ""
"จัดทำรายงานทางกฎหมาย เช่น งบดุล งบกำไรขาดทุน "
"ของแต่ละคอนโดมิเนียมได้อย่างง่ายดายภายในสิ้นงวด"

#. module: condominium
#: model:product.template,name:condominium.product_product_781_product_template
msgid "Cable distribution"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Call for working and reserve funds"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_664_product_template
msgid "Capital losses on the realization of receivables"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_726_product_template
msgid "Carpentry maintenance contracts"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Check if everything is correct. If yes, select all the quotations and, under"
" the \"Actions\" button, click on \"Confirm\"."
msgstr ""
"ตรวจสอบว่าทุกอย่างถูกต้องหรือไม่ หากใช่ ให้เลือกใบเสนอราคาทั้งหมด "
"และภายใต้ปุ่ม \"การดำเนินการ\" ให้คลิก \"ยืนยัน\""

#. module: condominium
#: model:product.template,name:condominium.product_product_752_product_template
msgid "Chimney and ventilation duct maintenance"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.city
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "City"
msgstr "เมือง"

#. module: condominium
#: model:product.template,name:condominium.product_product_678_product_template
msgid "Civil liability"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_735_product_template
msgid "Cleaning equipment"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_734_product_template
msgid "Cleaning products"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Click on \"Split per Property\" button: this will cancel the current "
"quotation and will create as many quotations as there are properties in the "
"condominium for which the costs are shared."
msgstr ""
"คลิกปุ่ม \"แยกตามทรัพย์สิน\" "
"ซึ่งจะยกเลิกใบเสนอราคาปัจจุบันและจะสร้างใบเสนอราคาจำนวนเท่ากับจำนวนทรัพย์สินในคอนโดมิเนียมที่แบ่งปันต้นทุน"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Click on the \"Sales Orders\" buttons to access all the generated "
"quotations."
msgstr "คลิกที่ปุ่ม \"ใบสั่งขาย\" เพื่อเข้าถึงใบเสนอราคาที่สร้างขึ้นทั้งหมด"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.color_buildings_tags
#: model:ir.model.fields,field_description:condominium.color_properties_tag
msgid "Color"
msgstr "สี"

#. module: condominium
#: model:product.template,name:condominium.product_product_9_product_template
msgid "Communication of information to the notary"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.company_buildings
#: model:ir.model.fields,field_description:condominium.company_ratios_field
msgid "Company"
msgstr "บริษัท"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_company_partner_id
msgid "Company partner"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_788_product_template
msgid "Compensation for members of the internal council and internal auditor"
msgstr ""

#. module: condominium
#: model:ir.actions.server,name:condominium.ir_actions_server_compute_ratio
#: model_terms:ir.ui.view,arch_db:condominium.product_pricelist_custom_view
msgid "Compute Ratios"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_693_product_template
msgid "Concierge charges"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_694_product_template
msgid "Concierge maintenance"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.condo_act_window
#: model:ir.model.fields,field_description:condominium.building_condominium
#: model:ir.model.fields,field_description:condominium.x_condominium_field
#: model:ir.ui.menu,name:condominium.condo_menu
#: model:ir.ui.menu,name:condominium.infra_condo_menu
msgid "Condominium"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.condominium_property_ids
msgid "Condominium Properties"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Condominium creation in Odoo (see <em>Quickly Set Up a Condominium 🚀 "
"</em>section)"
msgstr ""
"การสร้างคอนโดมิเนียมใน Odoo (ดูส่วน<em>การตั้งค่าคอนโดมิเนียมอย่างรวดเร็ว "
"🚀</em>)"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_select_custom
msgid "Condominiums"
msgstr ""

#. module: condominium
#: model:ir.ui.menu,name:condominium.config_menu
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: condominium
#: model:ir.actions.server,name:condominium.ir_act_server_confirm_action
msgid "Confirm"
msgstr "ยืนยัน"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "Contacts"
msgstr "การติดต่อ"

#. module: condominium
#: model:product.template,description_sale:condominium.product_product_4_product_template
msgid ""
"Contracts establishment\n"
"    Co-ownership creation in the software\n"
"    Act analysis\n"
"    Insurance analysis\n"
"    Identification forms creation\n"
"    Audit\n"
"    Opening/handover bank account(s) and company number\n"
"    Meters reading\n"
"    Call for funds and provisions\n"
"    Organization of general meeting"
msgstr ""
"การจัดทำสัญญา\n"
"   การสร้างความเป็นเจ้าของร่วมกันในซอฟต์แวร์\n"
"   การวิเคราะห์พระราชบัญญัติ\n"
"   การวิเคราะห์การประกันภัย\n"
"   การสร้างแบบฟอร์มระบุตัวตน\n"
"   การตรวจสอบบัญชี\n"
"   การเปิด/ส่งมอบบัญชีธนาคารและหมายเลขบริษัท\n"
"   การอ่านมิเตอร์\n"
"   การขอเงินทุนและเงินสำรอง\n"
"   การจัดประชุมสามัญ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Contracts establishment<br/>"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.country
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Country"
msgstr "ประเทศ"

#. module: condominium
#: model_terms:ir.actions.act_window,help:condominium.buildings_act_window
msgid "Create and manage the buildings that compose this condominium<br>"
msgstr ""

#. module: condominium
#: model_terms:ir.actions.act_window,help:condominium.properties_act_window
msgid "Create and manage the properties that compose this condominium"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Create the buildings that compounds the condominium."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Create the properties that compounds the buildings: apartment, studio, flat,"
" penthouse, garage, etc."
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meters_currency
msgid "Currency"
msgstr "สกุลเงิน"

#. module: condominium
#: model:product.template,name:condominium.product_product_686_product_template
#: model:product.template,name:condominium.product_product_810_product_template
msgid "Damages or expenses not covered by insurance"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meter_reading_date
msgid "Date"
msgstr "วันที่"

#. module: condominium
#: model:product.template,name:condominium.product_product_684_product_template
msgid "Deductible (insurance)"
msgstr ""

#. module: condominium
#: model:product.pricelist,name:condominium.product_pricelist_1
msgid "Default"
msgstr "เริ่มต้น"

#. module: condominium
#: model:product.template,name:condominium.product_product_670_product_template
msgid "Deferred values"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Define a recurring plan, monthly or yearly."
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.description_building
#: model:ir.model.fields,field_description:condominium.description_property_types
#: model:ir.model.fields,field_description:condominium.description_ratios_field
#: model:ir.model.fields,field_description:condominium.field_meters_name
#: model:ir.model.fields,field_description:condominium.field_meters_reading_description
msgid "Description"
msgstr "คำอธิบาย"

#. module: condominium
#: model:ir.actions.server,name:condominium.ir_actions_server_distribute_costs
#: model_terms:ir.ui.view,arch_db:condominium.account_move_custom_form_view
msgid "Distribute Costs"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_distribution_key_field
#: model_terms:ir.ui.view,arch_db:condominium.product_pricelist_list_custom_view
#: model_terms:ir.ui.view,arch_db:condominium.sale_order_form_custom
msgid "Distribution Key"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.distribution_keys_act_window
#: model:ir.ui.menu,name:condominium.infra_distribution_menu
msgid "Distribution Keys"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_799_product_template
msgid "Document translation"
msgstr ""

#. module: condominium
#: model:ir.ui.menu,name:condominium.owners_docs_menu
msgid "Documents"
msgstr "เอกสาร"

#. module: condominium
#: model:helpdesk.team,name:condominium.helpdesk_team_4
msgid "Dominican Condominium"
msgstr ""

#. module: condominium
#: model:project.task.type,name:condominium.project_task_type_18
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: condominium
#: model:product.template,name:condominium.product_product_723_product_template
msgid "Drainage, sewers, and pumping installations maintenance"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"During the general meetings, counting the votes depending on the ownership percentage could be painful. From the properties screen, select all the properties of the corresponding condominium and insert them in a spreadsheet by using the\n"
"            said feature under the Actions menu."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Each owner has to pay recurring fees:"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Each property is characterized by:"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Easily convert the condominiums 🖋️"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Efficiently count the votes 🙋‍♂️"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_707_product_template
msgid "Electrical installations inspections"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_708_product_template
msgid "Electrical installations maintenance contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_711_product_template
msgid "Electrical installations renovation"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_772_product_template
msgid "Electricity for common areas"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_688_product_template
msgid "Electricity for concierge"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_705_product_template
msgid "Elevator adaptation work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_773_product_template
msgid "Elevator electricity"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_701_product_template
msgid "Elevator inspections"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_703_product_template
msgid "Elevator maintenance and repairs not covered by contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_702_product_template
msgid "Elevator maintenance contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_704_product_template
msgid "Emergency elevator phone"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.employees_act_window
#: model:ir.ui.menu,name:condominium.purchase_employee_menu
msgid "Employees"
msgstr "พนักงาน"

#. module: condominium
#: model:product.template,name:condominium.product_product_660_product_template
msgid "Employer contributions for extra-legal insurance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_659_product_template
msgid "Employer contributions for social insurance"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Every year, a general meeting is scheduled to talk about the financial statements, to take important decisions and so on. This event can easily be scheduled in the Calendar app by creating\n"
"            <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Meetings</font>. To invite all the owners of a condominium, use the \"Search More\" on \"Attendees\" and type the name of the condominium in... \"Condominiums\"."
msgstr ""

#. module: condominium
#: model:ir.actions.server,name:condominium.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "ดำเนินการรหัส"

#. module: condominium
#: model:product.template,name:condominium.product_product_785_product_template
msgid "Expert fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_674_product_template
msgid "Extension work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_786_product_template
msgid "External auditor fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_728_product_template
msgid "External carpentry maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_8_product_template
msgid "Extraordinary General Meeting"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_669_product_template
msgid "Extraordinary work expenses"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Fairly split the costs ⚖️"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_749_product_template
msgid "Façade maintenance"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Finally, you can also define the distribution keys per property. The goal is"
" to assign a ratio for each property that will be used for splitting the "
"fees per owner and for distributing the costs."
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_677_product_template
msgid "Fire insurance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_10_product_template
msgid "First Reminder"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_776_product_template
msgid "Fixed fee and gas consumption"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_766_product_template
msgid "Fixed fee and water consumption"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_floor_field
#: model:ir.model.fields,field_description:condominium.x_floor_related_field
msgid "Floor"
msgstr "ชั้น"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"For example, to collect the cotisations, the selected company must be the condominium and the customer must be the condominium too. If you want to invoice your services, the selected company must be yours and the customer must be the\n"
"            condominium to be invoiced."
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_748_product_template
msgid "Foundation maintenance"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"From the Settings, create a new company in <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Companies</font>. Once done, it will appear on the top right of all screens with the aim to quickly switch between your company and the\n"
"            different condominiums."
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_780_product_template
msgid "Fuel oil deliveries"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_4
msgid "Garage"
msgstr ""

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_13
msgid "Garage AG"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_727_product_template
msgid "Garage door maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_743_product_template
msgid "Garden and immediate surroundings layout"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_741_product_template
msgid "Garden and immediate surroundings maintenance under contract"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_690_product_template
msgid "Gas for concierge"
msgstr ""

#. module: condominium
#: model:helpdesk.team,name:condominium.helpdesk_team_1
msgid "Green Island Property"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_754_product_template
msgid "Hall, stairwell, and corridor maintenance work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_774_product_template
msgid "Heating and sanitary electricity"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_691_product_template
msgid "Heating for concierge"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_716_product_template
msgid "Heating installations inspections"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_717_product_template
msgid "Heating maintenance contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_721_product_template
msgid "Heating pipes maintenance"
msgstr ""

#. module: condominium
#: model:ir.ui.menu,name:condominium.owners_help_menu
msgid "Helpdesk"
msgstr "ดูแลช่วยเหลือ"

#. module: condominium
#: model:product.template,name:condominium.product_product_7_product_template
msgid "Hourly Rate"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_2
msgid "House"
msgstr "บ้าน"

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_20
msgid "House C1"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_738_product_template
msgid "Household waste / PMC / paper and glass products"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_736_product_template
msgid "Household waste / PMC / paper and glass under contract"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"If there is no distribution keys set, the area will be used as default key."
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.image_buildings
msgid "Image"
msgstr "รูปภาพ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"In case of recurring fees, the invoices will be automatically generated by "
"Odoo at the right date. If not, you can manually post them in mass. Then "
"just send them using your preferred way!"
msgstr ""
"ในกรณีที่มีค่าธรรมเนียมที่เกิดขึ้นซ้ำ Odoo "
"จะสร้างใบแจ้งหนี้โดยอัตโนมัติในวันที่กำหนด ถ้าไม่เช่นนั้น "
"คุณสามารถส่งใบแจ้งหนี้เป็นกลุ่มได้ด้วยตนเอง "
"จากนั้นส่งโดยใช้ช่องทางที่คุณต้องการ!"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "In case of recurring fees:"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"In the Sales application, create a quotation and set the condominium as "
"customer."
msgstr ""
"ในแอปพลิเคชันการขาย ให้สร้างใบเสนอราคาและตั้งค่าคอนโดมิเนียมเป็นลูกค้า"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"In their day to day, the owners and the tenants can face some issues. In order to collect and centralize them, use the <strong><font class=\"text-o-color-1\">Helpdesk</font></strong> application. Thereby, each member can send an email\n"
"            that will be caught in the right helpdesk team. This eases the follow-up and the invoicing in case the time spent is not covered by the service fees."
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.condominium_act_window
#: model:ir.ui.menu,name:condominium.infra_menu
msgid "Infrastructure"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_681_product_template
msgid "Insurance for members of the internal council and auditor"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Insurances analysis"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_729_product_template
msgid "Internal carpentry maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_806_product_template
msgid "Internet information communication"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_is_a_property
msgid "Is a property"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_4_product_template
msgid "Kick-Off Fees"
msgstr ""

#. module: condominium
#: model:project.project,name:condominium.project_project_4
msgid "Kick-Off Template"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Kick-off meeting"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_763_product_template
msgid "Laundry charges"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_783_product_template
msgid "Lawyer fees"
msgstr "ค่าธรรมเนียมทนายความ"

#. module: condominium
#: model:product.template,name:condominium.product_product_683_product_template
msgid "Legal assistance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_800_product_template
msgid "Legal fees for recovering co-owners' arrears"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_700_product_template
msgid "Maintenance contracts"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Make sure that the products added are recurring."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "Manage Companies"
msgstr "จัดการบริษัท"

#. module: condominium
#: model_terms:ir.actions.act_window,help:condominium.employees_act_window
msgid "Manage the employees working for this condominium<br>"
msgstr ""

#. module: condominium
#: model_terms:ir.actions.act_window,help:condominium.owners_act_window
msgid "Manage the owners of this condominium<br>"
msgstr ""

#. module: condominium
#: model_terms:ir.actions.act_window,help:condominium.vendors_act_window
msgid "Manage the vendors related to this condominium<br>"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_796_product_template
msgid "Meeting consumables"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_795_product_template
msgid "Meeting room usage"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meter_reading_meter_id
msgid "Meter"
msgstr ""

#. module: condominium
#: model:ir.model,name:condominium.model_meter_reading
msgid "Meter Reading"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_property_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:condominium.analytic_account_custom_form_view
msgid "Meter Readings"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_724_product_template
msgid "Meter readings"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.action_configuration_meters
#: model:ir.model,name:condominium.model_meters
#: model:ir.ui.menu,name:condominium.condo_meters
msgid "Meters"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Meters reading"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_665_product_template
msgid "Miscellaneous expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_15_product_template
msgid "Monthly Service Fees"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.name_buildings_tags
#: model:ir.model.fields,field_description:condominium.name_properties_tags
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "Name"
msgstr "ชื่อ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Name or reference..."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building_tags
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_property_type
msgid "Name..."
msgstr "ชื่อ..."

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Never forget a task 📑"
msgstr ""

#. module: condominium
#: model:project.task.type,name:condominium.project_task_type_26
msgid "New"
msgstr "ใหม่"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "New condominiums should be created as companies from the settings"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_682_product_template
msgid "Non-professional trustee liability"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.notes_buildings
msgid "Notes"
msgstr "โน้ต"

#. module: condominium
#: model:product.template,name:condominium.product_product_5_product_template
msgid "Offboarding"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"On the one hand, the funds requests are sent by the condominium to the "
"owners. On the other hand, the service fees can be invoiced by \"My "
"Condominium Management\" to the owners. Here are the steps to follow:"
msgstr ""
"ในทางกลับกัน คำขอเงินทุนจะถูกส่งโดยคอนโดมิเนียมไปยังเจ้าของ ในทางกลับกัน "
"ค่าธรรมเนียมบริการสามารถเรียกเก็บโดย \"การบริหารจัดการคอนโดมิเนียมของฉัน\" "
"ไปยังเจ้าของได้ ขั้นตอนต่อไปนี้คือขั้นตอนที่ต้องปฏิบัติตาม:"

#. module: condominium
#: model:sale.order.template.line,name:condominium.sale_order_template_line_7
msgid "On-Demand Fees"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Once done, it is time to create the composition of the condominium by "
"selecting the right one on the top right of the screen. Then, open the "
"<strong><font class=\"text-o-color-1\">Condominium</font></strong> app where"
" you can:"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Once the quotation is confirmed, it automatically creates <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Projects</font> including several tasks to properly start the collaboration. This is based on the\n"
"            <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">Kick-Off Template</font> Template project:"
msgstr ""
"เมื่อยืนยันใบเสนอราคาแล้ว ระบบจะสร้าง<font class=\"text-o-color-1\" style=\"font-weight: bolder;\">โปรเจ็กต์</font>โดยอัตโนมัติซึ่งประกอบด้วยงานต่างๆ เพื่อเริ่มการทำงานร่วมกันอย่างเหมาะสม โดยอิงตาม\n"
"            เทมเพลตโปรเจ็กต์ <font class=\"text-o-color-1\" style=\"font-weight: bolder;\">เทมเพลต Kick-Off</font>:"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Once the quotation is confirmed, the condominium should be created in Odoo. "
"Basically, a<strong> </strong>condominium is like a company that is funded "
"by the owners and that has to pay for operating costs."
msgstr ""
"เมื่อได้รับการยืนยันใบเสนอราคาแล้ว คอนโดมิเนียมควรได้รับการสร้างใน Odoo "
"โดยพื้นฐาน "
"<strong></strong>คอนโดมิเนียมก็เหมือนกับบริษัทที่ได้รับเงินทุนจากเจ้าของและต้องจ่ายค่าใช้จ่ายในการดำเนินการ"

#. module: condominium
#: model:sale.order.template.line,name:condominium.sale_order_template_line_4
msgid "One-Time Fees"
msgstr ""

#. module: condominium
#: model:ir.actions.server,name:condominium.action_view_company_partner
msgid "Open company partner form view"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_7
msgid "Other"
msgstr "อื่นๆ"

#. module: condominium
#: model:product.template,name:condominium.product_product_733_product_template
msgid "Other building cleaning"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_731_product_template
msgid "Other carpentry work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_695_product_template
msgid "Other concierge expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_790_product_template
msgid "Other condominium council expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_709_product_template
msgid "Other electrical installations maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_775_product_template
msgid "Other electricity expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_706_product_template
msgid "Other elevator charges"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_787_product_template
msgid "Other fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_742_product_template
msgid "Other garden and immediate surroundings maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_777_product_template
msgid "Other gas expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_718_product_template
msgid "Other heating maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_737_product_template
msgid "Other household waste / PMC / paper and glass"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_685_product_template
msgid "Other insurance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_801_product_template
msgid "Other legal fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_809_product_template
msgid "Other management fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_797_product_template
msgid "Other meeting expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_745_product_template
msgid "Other parking, paths, and surroundings maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_661_product_template
msgid "Other personnel expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_805_product_template
msgid "Other property transfer fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_725_product_template
msgid "Other sanitary and heating maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_676_product_template
msgid "Other supplies"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_769_product_template
msgid "Other supplies for water softener"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_761_product_template
msgid "Other swimming pool expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_757_product_template
msgid "Other swimming pool/technical installations maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_794_product_template
msgid "Other trustee expenses"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_740_product_template
msgid "Other waste cleaning and treatment"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_771_product_template
msgid "Other water expenses"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_owner_field
#: model_terms:ir.ui.view,arch_db:condominium.analytic_account_custom_form_view
msgid "Owner"
msgstr "เจ้าของ"

#. module: condominium
#: model:ir.actions.act_window,name:condominium.owners_act_window
#: model:ir.ui.menu,name:condominium.owners_menu
#: model:ir.ui.menu,name:condominium.owners_owners_menu
msgid "Owners"
msgstr "เจ้าของ"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_parent_field
msgid "Parent"
msgstr "หลัก"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_parent_account_analytic_account_count
msgid "Parent count"
msgstr ""

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_6
msgid "Parking"
msgstr "ที่จอดรถ"

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_21
msgid "Parking CP"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_744_product_template
msgid "Parking, paths, and surroundings maintenance under contract"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_746_product_template
msgid "Parking, paths, and surroundings renovation works"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.partner_company_ids
msgid "Partner's Companies"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meters_price
msgid "Price"
msgstr "ราคา"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_pricelist_field
msgid "Pricelist"
msgstr "รายการราคา"

#. module: condominium
#: model:product.template,name:condominium.product_product_666_product_template
msgid "Private expenses"
msgstr ""

#. module: condominium
#: model:account.analytic.plan,name:condominium.account_analytic_plan_2
#: model:ir.actions.act_window,name:condominium.prop_condo_act_window
#: model:ir.actions.act_window,name:condominium.prop_own_act_window
#: model:ir.actions.act_window,name:condominium.properties_act_window
#: model:ir.actions.act_window,name:condominium.properties_act_window_view
#: model:ir.actions.act_window,name:condominium.properties_action_window
#: model:ir.actions.act_window,name:condominium.properties_building_act_window
#: model:ir.actions.act_window,name:condominium.properties_parent_act_window
#: model:ir.actions.act_window,name:condominium.props_act_window
#: model:ir.actions.act_window,name:condominium.props_build_act_window
#: model:ir.model.fields,field_description:condominium.x_properties_ids
#: model:ir.ui.menu,name:condominium.conf_prop_menu
#: model:ir.ui.menu,name:condominium.infra_properties_menu
#: model_terms:ir.ui.view,arch_db:condominium.analytic_account_custom_form_view
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "Properties"
msgstr "คุณสมบัติ"

#. module: condominium
#: model:ir.actions.act_window,name:condominium.properties_tags_act_window
#: model:ir.model,name:condominium.model_properties_tags
msgid "Properties Tags"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.property_types_act_window
#: model:ir.model,name:condominium.model_properties_types
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_property_type
msgid "Properties Types"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Properties creation in Odoo (see <em>Know the stakeholders 🔎</em> section)"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meter_reading_account_analytic_account
#: model:ir.model.fields,field_description:condominium.x_property_field
msgid "Property"
msgstr "คุณสมบัติ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Property Owner Association"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_807_product_template
msgid "Property accession fees"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Provide the best customer service 🆘"
msgstr ""

#. module: condominium
#: model:ir.ui.menu,name:condominium.purchase_menu
msgid "Purchase"
msgstr "สั่งซื้อ"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meter_reading_quantity
msgid "Quantity"
msgstr "ปริมาณ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Quickly set up a condominium 🚀"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_730_product_template
msgid "Railings and terrace separation partitions"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_ratio_field
msgid "Ratio"
msgstr "อัตราส่วน"

#. module: condominium
#: model:ir.actions.act_window,name:condominium.ratios_act_window
#: model:ir.model,name:condominium.ratios_model
#: model:ir.model.fields,field_description:condominium.x_ratios_field
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_for_x_ratios
#: model_terms:ir.ui.view,arch_db:condominium.product_pricelist_custom_view
msgid "Ratios"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_764_product_template
msgid "Recreational charges (playgrounds, tennis, fitness, billiards, etc.)"
msgstr ""

#. module: condominium
#: model:sale.order.template.line,name:condominium.sale_order_template_line_5
msgid "Recurring Fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_672_product_template
msgid "Renovation work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_14_product_template
msgid "Reserve Fund"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Reserve Fund: to collect all the money that will be used in case of "
"exceptional expense."
msgstr ""
"กองทุนสำรอง: เพื่อรวบรวมเงินทั้งหมดที่จะนำมาใช้ในกรณีที่มีค่าใช้จ่ายพิเศษ"

#. module: condominium
#: model:product.template,name:condominium.product_product_662_product_template
msgid "Retirement and survivor pensions"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_699_product_template
msgid "Reversals (provisions)"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_751_product_template
msgid "Roof maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_658_product_template
msgid "Salaries and direct social benefits"
msgstr ""

#. module: condominium
#: model:ir.ui.menu,name:condominium.owners_sales_menu
msgid "Sales"
msgstr "การขาย"

#. module: condominium
#: model:ir.actions.act_window,name:condominium.sale_order_button_action
#: model_terms:ir.ui.view,arch_db:condominium.sale_order_form_custom
msgid "Sales Orders"
msgstr "คำสั่งขาย"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Schedule the meetings 📅"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_11_product_template
msgid "Second Reminder"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"See the information of the condominium and fill the vendors to build an "
"address book."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Select the right company from which the fees will be invoiced (see "
"<em>Quickly set up a condominium 🚀</em> section to see how to switch the "
"company)."
msgstr ""
"เลือกบริษัทที่ถูกต้องที่จะเรียกเก็บค่าธรรมเนียม (ดูส่วน "
"<em>ตั้งค่าคอนโดมิเนียมอย่างรวดเร็ว 🚀</em> เพื่อดูวิธีการเปลี่ยนบริษัท)"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meters_sequence
#: model:ir.model.fields,field_description:condominium.sequence_buildings
#: model:ir.model.fields,field_description:condominium.sequence_field
#: model:ir.model.fields,field_description:condominium.sequence_properties_types
msgid "Sequence"
msgstr "ลำดับ"

#. module: condominium
#: model:product.template,name:condominium.product_product_3_product_template
msgid "Service Fees"
msgstr ""

#. module: condominium
#: model:base.automation,name:condominium.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Set the recurring plan start date."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Set the right (recurring) price."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Set the right distribution key."
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_798_product_template
msgid "Simultaneous translation for meetings"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "Some tags to add extra visual information."
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.source_sales_order_field
msgid "Source Sales Order"
msgstr "แหล่งคำสั่งขาย"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.source_sales_order
msgid "Source Sales Order count"
msgstr ""

#. module: condominium
#: model:ir.actions.server,name:condominium.ir_act_server_split_per_property
#: model_terms:ir.ui.view,arch_db:condominium.sale_order_form_custom
msgid "Split per Owner"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.country_state
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "State"
msgstr "จังหวัด"

#. module: condominium
#: model:product.template,name:condominium.product_product_803_product_template
msgid "Statute modification"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.street
msgid "Street"
msgstr "ถนน"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Street 2..."
msgstr "ถนน 2..."

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Street..."
msgstr "ถนน..."

#. module: condominium
#: model:ir.model.fields,field_description:condominium.street2
msgid "Street2"
msgstr "ถนน2"

#. module: condominium
#: model:x_properties_types,x_name:condominium.x_properties_types_3
msgid "Studio"
msgstr "สตูดิโอ"

#. module: condominium
#: model:account.analytic.account,name:condominium.account_analytic_account_22
msgid "Studio CS"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_671_product_template
msgid "Study and supervision fees"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.product_pricelist_custom_view
msgid "Sum of Area"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.product_pricelist_custom_view
msgid "Sum of Ratio"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_675_product_template
msgid "Supplies to the association"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_755_product_template
msgid "Swimming pool inspections"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_756_product_template
msgid "Swimming pool maintenance contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_758_product_template
msgid "Swimming pool product supplies"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_759_product_template
msgid "Swimming pool supervision"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_760_product_template
msgid "Swimming pool/technical installations repairs"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_692_product_template
msgid "TV distribution for concierge"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.tags_buildings
#: model:ir.model.fields,field_description:condominium.x_tags_field
#: model:ir.ui.menu,name:condominium.conf_prop_tag_menu
#: model:ir.ui.menu,name:condominium.conf_tags_menu
msgid "Tags"
msgstr "แท็ก"

#. module: condominium
#: model:product.template,name:condominium.product_product_778_product_template
msgid "Tank inspections"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_779_product_template
msgid "Tank maintenance"
msgstr ""

#. module: condominium
#: model:project.project,label_tasks:condominium.project_project_4
msgid "Tasks"
msgstr "งาน"

#. module: condominium
#: model:product.template,name:condominium.product_product_663_product_template
msgid "Taxes and fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_687_product_template
msgid "Telephone - Internet - concierge cell phone"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_tenant_field
msgid "Tenant"
msgstr "ผู้เช่า"

#. module: condominium
#: model:product.template,name:condominium.product_product_750_product_template
msgid "Terrace maintenance"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The area."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The building to which the property belongs."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The floor."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The owner of the property."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"The parent property, for example to indicate that a garage is linked with an"
" apartment."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"The reserve fund that is a savings account used in case of exceptional "
"expenses such as a severe repair, an investment in equipment, etc."
msgstr ""
"กองทุนสำรองที่เป็นบัญชีออมทรัพย์ที่ใช้ในกรณีมีค่าใช้จ่ายพิเศษ เช่น "
"การซ่อมแซมครั้งใหญ่ การลงทุนในอุปกรณ์ ฯลฯ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The services provided by the condominium management company."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The tenant if relevant."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "The type to easily classify it."
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"The working fund that is used to pay the common charges such as monthly "
"expenses (water, electricity, gas, etc.), insurances, maintenance, repairs, "
"cleaning of common areas, etc."
msgstr ""
"กองทุนทำงานที่ใช้เพื่อชำระค่าใช้จ่ายส่วนกลาง เช่น ค่าใช้จ่ายประจำเดือน (น้ำ,"
" ไฟ, แก๊ส, ฯลฯ), ประกันภัย, ค่าบำรุงรักษา, ซ่อมแซม, "
"ทำความสะอาดพื้นที่ส่วนกลาง ฯลฯ"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"These will be used to pay the <strong><font class=\"text-o-color-1\">Vendor "
"Bills</font></strong> that can be uploaded and automatically filled thanks "
"to"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_12_product_template
msgid "Third Reminder"
msgstr ""

#. module: condominium
#: model:product.template,description_sale:condominium.product_product_11_product_template
msgid "This does not include the fees from the ushers"
msgstr ""

#. module: condominium
#: model:product.template,description_sale:condominium.product_product_12_product_template
msgid "This does not include the fees from the ushers, lawyers, experts, etc."
msgstr "ทั้งนี้ไม่รวมค่าธรรมเนียมจากพนักงานต้อนรับ ทนายความ ผู้เชี่ยวชาญ ฯลฯ"

#. module: condominium
#: model:project.task.type,name:condominium.project_task_type_17
msgid "To Do"
msgstr "ที่จะทำ"

#. module: condominium
#: model:product.template,name:condominium.product_product_673_product_template
msgid "Transformation work"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_789_product_template
msgid ""
"Travel expenses for members of the internal council and internal auditor"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_782_product_template
msgid "Trustee fees"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_802_product_template
msgid "Trustee nomination publication"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_791_product_template
msgid "Trustee office and office supplies"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_793_product_template
msgid "Trustee phone"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_792_product_template
msgid "Trustee stamps"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.x_type_field
#: model:ir.model.fields,field_description:condominium.x_type_related_field
#: model:ir.ui.menu,name:condominium.conf_prop_type
msgid "Type"
msgstr "ประเภท"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "Type down your notes here..."
msgstr "พิมพ์หมายเหตุของคุณที่นี่..."

#. module: condominium
#: model:ir.model.fields,field_description:condominium.field_meter_reading_usage
msgid "Usage"
msgstr "การใช้งาน"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "VAT"
msgstr "ภาษีมูลค่าเพิ่ม (VAT)"

#. module: condominium
#: model:product.template,name:condominium.product_product_765_product_template
msgid "Various controls and monitoring"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_668_product_template
msgid "Various financial charges"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_762_product_template
msgid "Various maintenance contracts"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_747_product_template
msgid "Various works"
msgstr ""

#. module: condominium
#: model:ir.actions.act_window,name:condominium.vendors_act_window
#: model:ir.model.fields,field_description:condominium.vendor_ids
#: model:ir.ui.menu,name:condominium.purchase_vendor_menu
#: model_terms:ir.ui.view,arch_db:condominium.res_partner_form_view
msgid "Vendors"
msgstr "ผู้ขาย"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.vendor_company_ids
msgid "Vendors Companies"
msgstr ""

#. module: condominium
#: model:ir.model.fields,field_description:condominium.vendor_condominium_ids
msgid "Vendors Condominiums"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_715_product_template
msgid "Video surveillance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_680_product_template
msgid "Volunteer insurance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_739_product_template
msgid "Waste cleaning and treatment controls"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_719_product_template
msgid "Water booster maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_767_product_template
msgid "Water for common areas"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_689_product_template
msgid "Water for concierge"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_722_product_template
msgid "Water pipes maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_720_product_template
msgid "Water softener and water treatment maintenance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_768_product_template
msgid "Water softener salt"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_770_product_template
msgid "Water treatment supplies"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Welcome to your Odoo Property Owner Association package! This guide will "
"help you manage the condominiums efficiently, from setting up to general "
"meetings."
msgstr ""

#. module: condominium
#: model_terms:web_tour.tour,rainbow_man_message:condominium.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ยินดีต้อนรับ! ขอให้สนุกกับการค้นพบ"

#. module: condominium
#: model:x_buildings,x_name:condominium.x_buildings_1
msgid "Wing A"
msgstr ""

#. module: condominium
#: model:x_buildings,x_name:condominium.x_buildings_4
msgid "Wing B"
msgstr ""

#. module: condominium
#: model:x_buildings,x_name:condominium.x_buildings_5
msgid "Wing C"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_679_product_template
msgid "Work accident insurance"
msgstr ""

#. module: condominium
#: model:product.template,name:condominium.product_product_13_product_template
msgid "Working Fund"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid ""
"Working Fund: to collect all the money that will be used to pay the "
"recurring fees."
msgstr "กองทุนทำงาน: เพื่อรวบรวมเงินทั้งหมดที่จะใช้ในการจ่ายค่าธรรมเนียมประจำ"

#. module: condominium
#: model:ir.model.fields,field_description:condominium.zip
#: model_terms:ir.ui.view,arch_db:condominium.default_form_view_building
msgid "ZIP"
msgstr "รหัสไปรษณีย์"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Accounting"
msgstr "🎓 ระบบบัญชี"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Companies"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Helpdesk"
msgstr "🎓 Helpdesk"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Quotation Template"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 การขาย"

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Spreadsheet"
msgstr ""

#. module: condominium
#: model_terms:ir.ui.view,arch_db:condominium.welcome_article_body
msgid "🎓 Subscription"
msgstr ""
