<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="documents_folder_11" model="documents.document">
        <field name="name">Kick-Off Template</field>
        <field name="folder_id" search="[('id', '=', ref('documents_project.document_project_folder', raise_if_not_found=False))]"/>
        <field name="type">folder</field>
    </record>
    <record id="documents_folder_12" model="documents.document">
        <field name="name">Property</field>
        <field name="folder_id" search="[('id', '=', ref('documents_project.document_project_folder', raise_if_not_found=False))]"/>
        <field name="type">folder</field>
    </record>
</odoo>
