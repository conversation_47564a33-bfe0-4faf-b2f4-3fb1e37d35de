<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="condo_menu" model="ir.ui.menu">
        <field name="action" ref="properties_act_window"/>
        <field name="name">Condominium</field>
        <field name="web_icon_data" type="base64" file="condominium/static/description/icon.png"/>
    </record>
    <record id="infra_menu" model="ir.ui.menu">
        <field name="action" ref="condominium_act_window"/>
        <field name="name">Infrastructure</field>
        <field name="parent_id" ref="condo_menu"/>
    </record>
    <record id="owners_menu" model="ir.ui.menu">
        <field name="action" ref="base.action_open_website"/>
        <field name="name">Owners</field>
        <field name="parent_id" ref="condo_menu"/>
        <field name="sequence">11</field>
    </record>
    <record id="purchase_menu" model="ir.ui.menu">
        <field name="action" ref="base.action_open_website"/>
        <field name="name">Purchase</field>
        <field name="parent_id" ref="condo_menu"/>
        <field name="sequence">12</field>
    </record>
    <record id="config_menu" model="ir.ui.menu">
        <field name="name">Configuration</field>
        <field name="parent_id" ref="condo_menu"/>
        <field name="sequence">1000</field>
    </record>
    <record id="building_menu" model="ir.ui.menu">
        <field name="action" ref="buildings_act_window"/>
        <field name="name">Buildings</field>
        <field name="parent_id" ref="infra_menu"/>
        <field name="sequence">2</field>
    </record>
    <record id="infra_properties_menu" model="ir.ui.menu">
        <field name="action" ref="properties_act_window"/>
        <field name="name">Properties</field>
        <field name="parent_id" ref="infra_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="infra_condo_menu" model="ir.ui.menu">
        <field name="action" ref="action_view_company_partner"/>
        <field name="name">Condominium</field>
        <field name="parent_id" ref="infra_menu"/>
        <field name="sequence">3</field>
    </record>
    <record id="infra_distribution_menu" model="ir.ui.menu">
        <field name="action" ref="distribution_keys_act_window"/>
        <field name="name">Distribution Keys</field>
        <field name="parent_id" ref="infra_menu"/>
        <field name="sequence">5</field>
      </record>
    <record id="conf_building_menu" model="ir.ui.menu">
        <field name="action" ref="base.action_open_website"/>
        <field name="name">Buildings</field>
        <field name="parent_id" ref="config_menu"/>
        <field name="sequence">4</field>
    </record>
    <record id="conf_tags_menu" model="ir.ui.menu">
        <field name="action" ref="buildings_tags_act_window"/>
        <field name="name">Tags</field>
        <field name="parent_id" ref="conf_building_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="conf_prop_menu" model="ir.ui.menu">
        <field name="action" ref="base.action_open_website"/>
        <field name="name">Properties</field>
        <field name="parent_id" ref="config_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="conf_prop_tag_menu" model="ir.ui.menu">
        <field name="action" ref="properties_tags_act_window"/>
        <field name="name">Tags</field>
        <field name="parent_id" ref="conf_prop_menu"/>
        <field name="sequence">2</field>
    </record>
    <record id="conf_prop_type" model="ir.ui.menu">
        <field name="action" ref="property_types_act_window"/>
        <field name="name">Type</field>
        <field name="parent_id" ref="conf_prop_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="condo_meters" model="ir.ui.menu">
        <field name="name">Meters</field>
        <field name="action" ref="action_configuration_meters"/>
        <field name="parent_id" ref="conf_prop_menu"/>
        <field name="sequence">3</field>
    </record>
    <record id="purchase_vendor_menu" model="ir.ui.menu">
        <field name="action" ref="vendors_act_window"/>
        <field name="name">Vendors</field>
        <field name="parent_id" ref="purchase_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="purchase_employee_menu" model="ir.ui.menu">
        <field name="action" ref="employees_act_window"/>
        <field name="name">Employees</field>
        <field name="parent_id" ref="purchase_menu"/>
        <field name="sequence">2</field>
    </record>
    <record id="owners_owners_menu" model="ir.ui.menu">
        <field name="action" ref="owners_act_window"/>
        <field name="name">Owners</field>
        <field name="parent_id" ref="owners_menu"/>
        <field name="sequence">1</field>
    </record>
    <record id="owners_help_menu" model="ir.ui.menu">
        <field name="action" ref="helpdesk.helpdesk_team_dashboard_action_main"/>
        <field name="name">Helpdesk</field>
        <field name="parent_id" ref="owners_menu"/>
        <field name="sequence">2</field>
    </record>
    <record id="owners_docs_menu" model="ir.ui.menu">
        <field name="action" ref="documents.document_action"/>
        <field name="name">Documents</field>
        <field name="parent_id" ref="owners_menu"/>
        <field name="sequence">3</field>
    </record>
    <record id="owners_sales_menu" model="ir.ui.menu">
        <field name="action" ref="sale.action_quotations_with_onboarding"/>
        <field name="name">Sales</field>
        <field name="parent_id" ref="owners_menu"/>
        <field name="sequence">4</field>
    </record>
</odoo>
