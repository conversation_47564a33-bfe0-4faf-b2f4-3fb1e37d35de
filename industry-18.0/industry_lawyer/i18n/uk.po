# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_lawyer
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:44+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"<b>Book a consultation online</b>\n"
"                                            <br/>"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"<b>Lawyers &amp;amp; associates</b>\n"
"                                    <br/>"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Add a new line to register a new Timesheet on the Administration Task of the"
" project manually."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Add another Sale Order line using the Administration fees product."
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_6_product_template
msgid "Administration fees"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid "Aline Turner, Immigration lawyer"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid ""
"Aline is one of the iconic people in life who can say they love what\n"
"                                                    they do. She acquired experience in immigration law and human rights\n"
"                                                    law thanks to several experiences in the non-profit sector. She\n"
"                                                    worked as a legal adviser in nationality law with the Objectif\n"
"                                                    non-profit."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Assign the responsible user."
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_8_product_template
msgid "Audience with a colleague"
msgstr ""

#. module: industry_lawyer
#: model:project.project,name:industry_lawyer.project_project_2
msgid "Base project"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Business Flows"
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_2_product_template
#: model:sale.order.template.line,name:industry_lawyer.sale_order_template_line_1
msgid "Case opening"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Check the created appointment."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Check the user agenda, a meeting is created."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Complete the Customer information."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Confirm the Sale Order which will create the related Project and Tasks"
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_5_product_template
msgid "Consultation"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Contact us and find out why."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Create a Regular Invoice."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Create a Sale Order using the Standard Lawyer service Quotation template."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Domains"
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_7_product_template
msgid "Down payment"
msgstr "Аванс"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Excellence since 2008"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"Excellence, privacy and reliability are the corner\n"
"                                                    stones\n"
"                                                    of our services."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Expropriation law"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Family reunification"
msgstr ""

#. module: industry_lawyer
#: model_terms:appointment.type,message_intro:industry_lawyer.appointment_type_1
msgid "Fee: 100€<br>Check our domains of competences on our website."
msgstr ""

#. module: industry_lawyer
#: model_terms:appointment.type,message_intro:industry_lawyer.appointment_type_2
msgid "Fee: 100€<br>The fee includes the opening of the case."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Flow 1: Get an appointment on the website"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Flow 2: Open a new file, start registering Timesheets."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Flow 3: Create an invoice of"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid ""
"Founder and chief visionary, Tony is the driving force behind the\n"
"                                                    cabinet."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "From the Sales app:"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Go in the Timesheets tab, register a new Timesheet manually."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"Help you throughout your procedure and provide you\n"
"                                                    our\n"
"                                                    experience at an affordable price."
msgstr ""

#. module: industry_lawyer
#: model:appointment.type,name:industry_lawyer.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Immigration"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "In the Backend:"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Input the amount of delivered dactylographied pages and travel expenses "
"incurred."
msgstr ""

#. module: industry_lawyer
#: model:project.project,name:industry_lawyer.project_project_1
msgid "Internal"
msgstr "Внутрішнє"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid "Iris Joe, Public Rights lawyer"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid ""
"Is it possible to obtain a residence permit for my parents, for a person\n"
"                                                with whom I am not married, for a minor under guardianship, for a\n"
"                                                homosexual couple, for a dependent…? Every individual has, in principle,\n"
"                                                the right to live with their family. However this right is not absolute."
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_1_product_template
#: model:sale.order.template.line,name:industry_lawyer.sale_order_template_line_2
msgid "Lawyer fees"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Lawyer firms"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Lawyers have open / public consultations for people to reach out and get "
"advice on their situation."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Learn more"
msgstr "Дізнатися більше"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Look at the available tasks, select the Lawyer Services task."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid "Mich Stark, Founder"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid ""
"Mich loves taking on challenges. With his multi-year experience as\n"
"                                                    lawyer in Immigration, Mich has helped the cabinet to get where it\n"
"                                                    is today. Mich is among the best minds."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "On the Website:"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Open the Timesheet app:"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Open the related Project:"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"Our cabinet is born in 2008 at the initiative of our founders, Lawyers &amp;amp;\n"
"                                            Associates, from a desire to provide a new center of excellence in our\n"
"                                            domains,\n"
"                                            Immigration and Civil Rights. Since then, we have helped over 1500 clients\n"
"                                            in\n"
"                                            their\n"
"                                            procedure."
msgstr ""

#. module: industry_lawyer
#: model:website.menu,name:industry_lawyer.website_menu_9
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid "Our lawyers"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Our mission"
msgstr "Наша місія"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Our team"
msgstr "Наша команда"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Our values"
msgstr "Наші цінності"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Pharmaceutical law"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid ""
"Pharmaceutics are not perfect, and in some extreme cases, they can have a\n"
"                                                tremendous impact on the life of people. We guide you through your\n"
"                                                procedure and help us clair your rights."
msgstr ""

#. module: industry_lawyer
#: model:appointment.type,name:industry_lawyer.appointment_type_2
msgid "Public Right"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Public right"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Put it In Progress, open it."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Researcher"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "Schedule an appointment"
msgstr "Запланувати зустріч"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Select the domain, then the lawyer, and select an available slot."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid "Select the project SO."
msgstr ""

#. module: industry_lawyer
#: model:website.menu,name:industry_lawyer.website_menu_10
msgid "Services"
msgstr "Послуги"

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid ""
"Some people have difficulties obtaining the equivalence of their diploma\n"
"                                                or don’t know whether the solicited higher education institution allows\n"
"                                                to be granted a visa or residence permit in Belgium."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid ""
"Strict legal conditions must be met in order to be considered a <strong>\n"
"                                                researcher\n"
"                                            </strong> and to be eligible for a work and residence permit.\n"
"                                                If you are a foreigner and wish to obtain a residence permit in the <strong>\n"
"                                                United States\n"
"                                            </strong> as part of a study, research, or academical service, carefully\n"
"                                                prepare your file and ask for advice from specialized lawyers."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid "Students"
msgstr "Студенти"

#. module: industry_lawyer
#: model:project.project,label_tasks:industry_lawyer.project_project_1
#: model:project.project,label_tasks:industry_lawyer.project_project_2
msgid "Tasks"
msgstr "Завдання"

#. module: industry_lawyer
#: model_terms:appointment.type,message_confirmation:industry_lawyer.appointment_type_1
msgid "Thank you for your trust !"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"This setup is for lawyers firms. They represent their clients and defend "
"their interest during juridical procedures."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid "Tony Fred, Founder"
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_4_product_template
#: model:sale.order.template.line,name:industry_lawyer.sale_order_template_line_4
msgid "Travelling expenses"
msgstr ""

#. module: industry_lawyer
#: model:product.template,name:industry_lawyer.product_product_3_product_template
#: model:sale.order.template.line,name:industry_lawyer.sale_order_template_line_3
msgid "Typed pages"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Use the quick Timesheet functionality to start counting time on the Lawyer "
"Services Task of the project."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"Visit the Appointment page where the open consultations slots for all the "
"lawyers are accessible."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "We advocate for your rights"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid ""
"We have built a strong team of lawyers with\n"
"                                                    renowned\n"
"                                                    experience in our domains."
msgstr ""

#. module: industry_lawyer
#: model_terms:web_tour.tour,rainbow_man_message:industry_lawyer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.welcome_article_body
msgid ""
"When a case he opened and a contract is signed between them and their "
"clients, they will typically execute a certain amount of tasks related to "
"the case, and invoice their services on a given rate."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.our_services
msgid ""
"Whether you are landlord that want to execute an expropriation, or a\n"
"                                                tenant that needs to be defended against an abusive expropriation, our\n"
"                                                experienced lawyers can help."
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.about_us
msgid ""
"With a Master’s degree in public and administrative law and a\n"
"                                                    Bachelor’s degree in criminology, Iris Joe decided to take the next\n"
"                                                    step in her career. In January 2017, she joined the cabinet for her\n"
"                                                    internship to become legal counsel.\n"
"\n"
"                                                    <br/>"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "brown wooden smoking pipe on white surface"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "low angle photography of beige building"
msgstr ""

#. module: industry_lawyer
#: model_terms:ir.ui.view,arch_db:industry_lawyer.homepage
msgid "three women sitting beside table"
msgstr ""
