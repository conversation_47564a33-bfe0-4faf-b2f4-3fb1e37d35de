# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* architects
# 
# Translators:
# <PERSON><PERSON>, 2024
# KeyVillage, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:18+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: architects
#: model:product.template,name:architects.product_product_36_product_template
msgid "3D Images"
msgstr "3D изображения"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "<b>Architectural Office</b>"
msgstr "<b>Архитектурно бюро</b>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "<b>Near You.</b>"
msgstr "<b>Близо до вас.</b>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Телефонен номер</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Тема</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Вашата компания</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Вашият имейл</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Вашето име</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Вашият въпрос</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                Automated actions have been created to create activities at appropriated times (an action to call the lead is created when there's a new opportunity, a to-do action is generated when the opportunity is at the \"needs analysis\n"
"                stage\", a reminder and activity to cancel opportunity are created when the quotation is sent)\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"              Създадени са автоматизирани действия за създаване на дейности в подходящи моменти (действие за обаждане до потенциалния клиент се създава, когато има нова възможност, действие за изпълнение се генерира, когато възможността е на етап \"анализа на нуждите\", напомняне и дейност за отмяна на възможността се създават, когато офертата е изпратена)\n"
"\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                From the opportunity, a quote can simply be created using the \"create quote\" button, and using one of the pre-created quotation template (services and quantities can be suited to the client's request)\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"              Оферта може лесно да бъде създадена чрез бутона „Създай оферта“ от възможност и използвайки една от предварително създадените шаблони за оферти (услугите и количествата могат да бъдат съобразени с изискванията на клиента).\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">A set of architectural services have "
"already been created and can be edited to match the architects' "
"services.</span>"
msgstr ""
"<span style=\"font-size: 14px;\"> Вече е създаден набор от архитектурни "
"услуги, които могат да бъдат редактирани, за да отговарят на услугите на "
"архитектите.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Leads get created in the CRM by filling the"
" \"Contact us\" form of the website or manually by the salesperson.</span>"
msgstr ""
"<span style=\"font-size: 14px;\"> Лийдовете се създават в CRM чрез попълване"
" на формата „Свържете се с нас“ на уебсайта или ръчно от търговския "
"представител.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Thanks to the services configuration, a "
"project gets created automatically once the quote is confirmed by the "
"customer. A set of tasks are created in this project, based on the sale "
"order lines.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Благодарение на конфигурацията на услугите,"
" проект се създава автоматично, след като офертата бъде потвърдена от "
"клиента. В този проект се създава набор от задачи, базирани на редовете в "
"продажбената поръчка.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">There is also a map view for the projects "
"(projects are assigned a partner based on the projects location, either with"
" the address or with the geolocation if there's no assigned address "
"yet)</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Има и изглед на карта за проектите "
"(проектите се присвояват на партньор въз основа на местоположението на "
"проекта, било то с адрес или с геолокация, ако все още няма определен "
"адрес).</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 16px;\">\n"
"                This industry is made for architects that sell all kind of architectural services such as feasibility studies, schematic design, design development, worksite follow-up, energy efficiency assessment.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 16px;\">\n"
"                Тази индустрия е създадена за архитекти, които предлагат всички видове архитектурни услуги като проучвания за осъществимост, схематично проектиране, разработване на дизайн, проследяване на строителния обект, оценка на енергийната ефективност.\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Set a cost on each employee and use the Expense application to record project expenses. It's not just about labor and materials, but also about tracking the costs of those undercover subcontractors and any other project-related\n"
"                expenses. Open up the profitability report for your project, and there you'll see budgeted costs, actual costs, revenues, and profit margins. This will help you to figure out how to boost your profit.\n"
"            </span>\n"
"            <br/>"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Задайте разхода за всеки служител и използвайте приложението \"Разходи\" за да записвате разходите по проекта. Става дума не само за разходи за труд и материали, но също и за проследяване на разходите на подизпълнителите, както и на всички други, свързани с проекта\n"
"                разходи. Отворете отчета за печалба на проекта си и там ще видите бюджетираните разходи, действителните разходи, приходите и маржовете на печалбата. Това ще ви помогне да разберете как да увеличите печалбата си.\n"
"            </span>\n"
"            <br/>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Use the documents app to store and share all project documents. Sub-workspaces get created for each project, <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​and link to these sub-workspace can be shared with customers so\n"
"                that they have a direct access to all produced <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​documents.\n"
"            </span>"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Използвайте приложението за документи, за да съхранявате и споделяте всички документи на проекта. За всеки проект се създават отделни работни пространства, <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​и връзка към тези работни пространства могат да бъдат споделени с клиенти, така че\n"
"да имат пряк достъп до всички създадени <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​документи.\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​<br/>"
msgstr "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​<br/>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Follow project "
"profitability"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​ Следете "
"рентабилността на проекта."

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Manage documents"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Управление на "
"документи"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Architect Industry"
msgstr "Архитектурна индустрия"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Architect industry"
msgstr "Архитектурна индустрия"

#. module: architects
#: model:project.project,name:architects.project_project_3
msgid "Architectural Project"
msgstr "Архитектурен проект"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_associated_location
msgid "Associated Location"
msgstr "Свързано местоположение."

#. module: architects
#: model:product.template,name:architects.product_product_47_product_template
msgid "BEP Declaration"
msgstr "Декларация за BEP"

#. module: architects
#: model:project.project,name:architects.project_project_5
msgid "BEP Mission"
msgstr "BEP Мисия"

#. module: architects
#: model:product.template,name:architects.product_product_46_product_template
msgid "BEP Study"
msgstr "BEP Проучване"

#. module: architects
#: model:product.template,name:architects.product_product_37_product_template
#: model:project.project,name:architects.project_project_2
msgid "Building Permit"
msgstr "Разрешение за строеж"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Business flows"
msgstr "Бизнес процеси"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_cadastral_number
msgid "Cadastral number"
msgstr "Номер на кадастър"

#. module: architects
#: model:base.automation,name:architects.base_automation_1
msgid "Call when there's a new opportunity"
msgstr "Обадете се при нова възможност"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "Contact us"
msgstr "Свържете се с нас"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"Свържете се с нас за всичко, свързано с нашата компания или услуги.<br/>\n"
"                                                                        Ще направим всичко възможно да се свържем с вас възможно най-скоро."

#. module: architects
#: model:base.automation,name:architects.base_automation_2
msgid "Create activity to send quote"
msgstr "Създаване на дейност за изпращане на оферта"

#. module: architects
#: model:ir.actions.server,name:architects.create_activity_quote_server_action
msgid "Create activity: Call"
msgstr "Създайте дейност: Обаждане"

#. module: architects
#: model:ir.actions.server,name:architects.call_opportunity_server_action
msgid "Create activity: Call the client to make sure the request is clear"
msgstr ""
"Създайте дейност: Обади се на клиента, за да се уверите, че заявката е ясна"

#. module: architects
#: model:ir.actions.server,name:architects.mark_opportunity_server_action
#: model:ir.actions.server,name:architects.set_reminder_server_action
msgid "Create activity: To-Do"
msgstr "Създайте дейност: За изпълнение"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Create quote"
msgstr "Създайте оферта"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid ""
"Designs and carries out contemporary and unique projects throughout the "
"world"
msgstr "Проектира и изпълнява модерни и уникални проекти в целия свят"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_15
msgid "Done"
msgstr "Извършен"

#. module: architects
#: model:product.template,name:architects.product_product_38_product_template
msgid "Execution Study"
msgstr "Проучване на изпълнението"

#. module: architects
#: model:product.template,name:architects.product_product_39_product_template
msgid "Feasibility Study"
msgstr "Предпроектно проучване"

#. module: architects
#: model:ir.model.fields,field_description:architects.res_partner_project_location
msgid "Is a project location"
msgstr "Местоположение на проекта ли е"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contact_search_inherit
msgid "Is not a project location"
msgstr "Не е местоположение на проекта"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Manage leads"
msgstr "Управление на потенциални клиенти"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Manage projects"
msgstr "Управление на проекти"

#. module: architects
#: model:base.automation,name:architects.base_automation_3
msgid "Mark as To-do when opportunity needs analysis"
msgstr ""
"Отбележи като Задача за изпълнение, когато възможността се нуждае от анализ."

#. module: architects
#: model:product.template,name:architects.product_product_40_product_template
msgid "Mileage Expense"
msgstr ""

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "My Company"
msgstr "Моята компания"

#. module: architects
#: model:crm.stage,name:architects.crm_stage_5
msgid "Needs Analysis"
msgstr "Анализ на нуждите"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_13
msgid "New"
msgstr "Нов"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_14
msgid "Ongoing"
msgstr "Протичащ"

#. module: architects
#: model:product.template,name:architects.product_product_41_product_template
msgid "Pre-project Study "
msgstr "Предпроектно проучване"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_project_expected_start_date
msgid "Project expected start date"
msgstr "Очаквана начална дата на проекта"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_project_reference
msgid "Project reference"
msgstr "Справка за проекта"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_project_type
#: model:ir.model.fields,field_description:architects.project_project_type
msgid "Project type"
msgstr "Тип на проекта"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_promoter
msgid "Promoter"
msgstr "Организатор"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "Reach out"
msgstr "Свържете се"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.crm_lead_form_customization_architects
msgid "Request Details"
msgstr "Детайли на заявката"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_request_date
msgid "Request date"
msgstr "Дата на заявката"

#. module: architects
#: model:base.automation,name:architects.base_automation_4
msgid "Set a reminder when quote is sent"
msgstr "Задайте напомняне при изпращане на оферта"

#. module: architects
#: model:product.template,name:architects.product_product_42_product_template
msgid "Sketches"
msgstr "Скици"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "Submit"
msgstr "Изпращане"

#. module: architects
#: model:product.template,name:architects.product_product_43_product_template
msgid "Synthesis Plans"
msgstr ""

#. module: architects
#: model:project.project,label_tasks:architects.project_project_2
#: model:project.project,label_tasks:architects.project_project_3
#: model:project.project,label_tasks:architects.project_project_5
msgid "Tasks"
msgstr "Задачи"

#. module: architects
#: model_terms:web_tour.tour,rainbow_man_message:architects.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Добре дошли! Приятно разглеждане!"

#. module: architects
#: model:product.template,name:architects.product_product_45_product_template
msgid "Worksite Visit"
msgstr "Оглед на обекта"

#. module: architects
#: model:product.template,name:architects.product_product_44_product_template
msgid "Worksite follow-up"
msgstr "Посещение на строителния обект"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "​Follow project profitability"
msgstr "Следете рентабилността на проекта"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "​Manage documents"
msgstr "Управление на документи"
