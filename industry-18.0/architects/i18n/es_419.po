# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* architects
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:18+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: architects
#: model:product.template,name:architects.product_product_36_product_template
msgid "3D Images"
msgstr "Imágenes 3D"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "<b>Architectural Office</b>"
msgstr "<b>Oficina arquitectónica</b>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "<b>Near You.</b>"
msgstr "<b>cerca de usted</b>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de teléfono</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Asunto</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su empresa</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su pregunta</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                Automated actions have been created to create activities at appropriated times (an action to call the lead is created when there's a new opportunity, a to-do action is generated when the opportunity is at the \"needs analysis\n"
"                stage\", a reminder and activity to cancel opportunity are created when the quotation is sent)\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                Se crearon acciones automatizadas para que se creen tareas en los momentos correctos. Por ejemplo, cuando hay una nueva oportunidad se crea una tarea para llamar al cliente, cuando la oportunidad llega a la etapa \"se requiere análisis\" se genera una tarea\n"
"                por hacer para el análisis, cuando se envía una cotización se crea un recordatorio para cancelar la oportunidad.\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                From the opportunity, a quote can simply be created using the \"create quote\" button, and using one of the pre-created quotation template (services and quantities can be suited to the client's request)\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                Desde la oportunidad puede crear una cotización con el botón \"crear cotización\" y usando una de las plantillas de cotización que se incluyen por defecto (los servicios y cantidades se pueden actualizar según la solicitud del cliente).\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">A set of architectural services have "
"already been created and can be edited to match the architects' "
"services.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Ya se creó un conjunto de servicios "
"arquitectónicos que puede editarse para que vaya con los servicios del "
"arquitecto.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Leads get created in the CRM by filling the"
" \"Contact us\" form of the website or manually by the salesperson.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Los leads de CRM se crean ya sea al llenar "
"el formulario \"Contáctenos\" del sitio web o si los crea de forma manual "
"una persona de ventas.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Thanks to the services configuration, a "
"project gets created automatically once the quote is confirmed by the "
"customer. A set of tasks are created in this project, based on the sale "
"order lines.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Gracias a la configuración de los "
"servicios, un proyecto se crea de forma automática una vez que el cliente "
"confirme la cotización. En este proyecto se crea una serie de tareas, según "
"las líneas de orden de venta.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">There is also a map view for the projects "
"(projects are assigned a partner based on the projects location, either with"
" the address or with the geolocation if there's no assigned address "
"yet)</span>"
msgstr ""
"<span style=\"font-size: 14px;\">También hay una vista de mapa para los "
"proyectos, los cuales se asignan a un usuario según la ubicación del "
"proyecto. Esto dependerá de una dirección o geologalización si no se ha "
"asignado una dirección todavía.</span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"font-size: 16px;\">\n"
"                This industry is made for architects that sell all kind of architectural services such as feasibility studies, schematic design, design development, worksite follow-up, energy efficiency assessment.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 16px;\">\n"
"                Este sector está hecho para arquitectos que venden todo tipo de servicios arquitectónicos como estudios de viabilidad, diseños preliminares, desarrollo de diseño, seguimiento en el sitio de trabajo, o evaluación de la eficiencia energética.\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Set a cost on each employee and use the Expense application to record project expenses. It's not just about labor and materials, but also about tracking the costs of those undercover subcontractors and any other project-related\n"
"                expenses. Open up the profitability report for your project, and there you'll see budgeted costs, actual costs, revenues, and profit margins. This will help you to figure out how to boost your profit.\n"
"            </span>\n"
"            <br/>"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Configure un costo en cada empleado y use la aplicación Gastos para registrar el costo de los proyectos. No se trata del trabajo ni los materiales, sino que también se registran los costos de aquellos subcontratistas necesarios y cualquier otro gasto\n"
"                relacionado al proyecto. Abra el reporte de ganancias de su proyecto para ver los costos en el presupuesto, los costos reales y el margen de utilidad. Así podrá saber cómo puede mejorar sus ganancias.\n"
"            </span>\n"
"            <br/>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Use the documents app to store and share all project documents. Sub-workspaces get created for each project, <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​and link to these sub-workspace can be shared with customers so\n"
"                that they have a direct access to all produced <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​documents.\n"
"            </span>"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​\n"
"            <span style=\"font-size: 14px;\">\n"
"                Use la aplicación Documentos para guardar y compartir todos lod documentos del proyecto. Se crearán espacios de trabajos secundarios para cada proyectos, <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​y puede compartir un enlace a estos proyectos secundarios con los clientes para que\n"
"                tengan acceso directo a todos los documentos <span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​producidos.\n"
"            </span>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​<br/>"
msgstr "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​<br/>"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Follow project "
"profitability"
msgstr ""
"<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Seguir la "
"rentabilidad del proyecto"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Manage documents"
msgstr "<span style=\"width: 40px;\" class=\"oe-tabs\"> </span>​Gestione documentos"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Architect Industry"
msgstr "Sector de arquitectura"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Architect industry"
msgstr "Sector de arquitectura"

#. module: architects
#: model:project.project,name:architects.project_project_3
msgid "Architectural Project"
msgstr "Proyecto arquitectónico "

#. module: architects
#: model:ir.model.fields,field_description:architects.project_associated_location
msgid "Associated Location"
msgstr "Ubicación asociada"

#. module: architects
#: model:product.template,name:architects.product_product_47_product_template
msgid "BEP Declaration"
msgstr "Declaración BIM"

#. module: architects
#: model:project.project,name:architects.project_project_5
msgid "BEP Mission"
msgstr "Misión BIM"

#. module: architects
#: model:product.template,name:architects.product_product_46_product_template
msgid "BEP Study"
msgstr "Estudio BIM"

#. module: architects
#: model:product.template,name:architects.product_product_37_product_template
#: model:project.project,name:architects.project_project_2
msgid "Building Permit"
msgstr "Permiso de construcción"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Business flows"
msgstr "Flujos empresariales"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_cadastral_number
msgid "Cadastral number"
msgstr "Número de predial"

#. module: architects
#: model:base.automation,name:architects.base_automation_1
msgid "Call when there's a new opportunity"
msgstr "Llamar cuando haya una nueva oportunidad "

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "Contact us"
msgstr "Contáctenos"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"Contáctenos para hablar acerca de cualquier cosa relacionada con nuestra empresa o servicios.<br/>\n"
"                                                                        Le responderemos tan pronto como sea posible."

#. module: architects
#: model:base.automation,name:architects.base_automation_2
msgid "Create activity to send quote"
msgstr "Crear actividad para enviar cotización"

#. module: architects
#: model:ir.actions.server,name:architects.create_activity_quote_server_action
msgid "Create activity: Call"
msgstr "Crear actividad: Llamada"

#. module: architects
#: model:ir.actions.server,name:architects.call_opportunity_server_action
msgid "Create activity: Call the client to make sure the request is clear"
msgstr ""
"Crear actividad: Llamar al cliente para asegurarse de que la solicitud quedó"
" clara"

#. module: architects
#: model:ir.actions.server,name:architects.mark_opportunity_server_action
#: model:ir.actions.server,name:architects.set_reminder_server_action
msgid "Create activity: To-Do"
msgstr "Crear actividad: Actividad pendiente"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Create quote"
msgstr "Crear cotización"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid ""
"Designs and carries out contemporary and unique projects throughout the "
"world"
msgstr ""
"Diseñamos y convertimos en realidad proyectos contemporáneos y únicos en "
"todo el mundo"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_15
msgid "Done"
msgstr "Listo"

#. module: architects
#: model:product.template,name:architects.product_product_38_product_template
msgid "Execution Study"
msgstr "Estudio de ejecución"

#. module: architects
#: model:product.template,name:architects.product_product_39_product_template
msgid "Feasibility Study"
msgstr "Estudio de viabilidad"

#. module: architects
#: model:ir.model.fields,field_description:architects.res_partner_project_location
msgid "Is a project location"
msgstr "Es la ubicación de un proyecto"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contact_search_inherit
msgid "Is not a project location"
msgstr "No es la ubicación de un proyecto"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Manage leads"
msgstr "Gestionar leads"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "Manage projects"
msgstr "Gestionar proyectos"

#. module: architects
#: model:base.automation,name:architects.base_automation_3
msgid "Mark as To-do when opportunity needs analysis"
msgstr ""
"Marcar como actividad pendiente cuando una oportunidad necesita más análisis"

#. module: architects
#: model:product.template,name:architects.product_product_40_product_template
msgid "Mileage Expense"
msgstr "Gastos de kilometraje"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "My Company"
msgstr "Mi Empresa"

#. module: architects
#: model:crm.stage,name:architects.crm_stage_5
msgid "Needs Analysis"
msgstr "Necesita análisis"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_13
msgid "New"
msgstr "Nuevo"

#. module: architects
#: model:project.task.type,name:architects.project_task_type_14
msgid "Ongoing"
msgstr "En curso"

#. module: architects
#: model:product.template,name:architects.product_product_41_product_template
msgid "Pre-project Study "
msgstr "Estudio preliminar"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_project_expected_start_date
msgid "Project expected start date"
msgstr "Fecha de inicio esperada del proyecto"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_project_reference
msgid "Project reference"
msgstr "Referencia del proyecto"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_project_type
#: model:ir.model.fields,field_description:architects.project_project_type
msgid "Project type"
msgstr "Tipo de proyecto"

#. module: architects
#: model:ir.model.fields,field_description:architects.project_promoter
msgid "Promoter"
msgstr "Promotora"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.homepage
msgid "Reach out"
msgstr "Contáctenos"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.crm_lead_form_customization_architects
msgid "Request Details"
msgstr "Solicite más detalles"

#. module: architects
#: model:ir.model.fields,field_description:architects.crm_lead_request_date
msgid "Request date"
msgstr "Solicitar fecha"

#. module: architects
#: model:base.automation,name:architects.base_automation_4
msgid "Set a reminder when quote is sent"
msgstr "Configurar un recordatorio cuando una cotización se envía"

#. module: architects
#: model:product.template,name:architects.product_product_42_product_template
msgid "Sketches"
msgstr "Bocetos"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.contactus
msgid "Submit"
msgstr "Enviar"

#. module: architects
#: model:product.template,name:architects.product_product_43_product_template
msgid "Synthesis Plans"
msgstr "Planos de síntesis"

#. module: architects
#: model:project.project,label_tasks:architects.project_project_2
#: model:project.project,label_tasks:architects.project_project_3
#: model:project.project,label_tasks:architects.project_project_5
msgid "Tasks"
msgstr "Tareas"

#. module: architects
#: model_terms:web_tour.tour,rainbow_man_message:architects.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Le damos la bienvenida! Disfrute del sitio."

#. module: architects
#: model:product.template,name:architects.product_product_45_product_template
msgid "Worksite Visit"
msgstr "Visita al lugar de trabajo"

#. module: architects
#: model:product.template,name:architects.product_product_44_product_template
msgid "Worksite follow-up"
msgstr "Seguimiento en el lugar de trabajo"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "​Follow project profitability"
msgstr "Seguir la rentabilidad del proyecto"

#. module: architects
#: model_terms:ir.ui.view,arch_db:architects.welcome_article_body
msgid "​Manage documents"
msgstr "Gestionar documentos"
