# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bookstore
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:22+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
", and feel free\n"
"                        to"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"1. Go to the \"Sales\" App and create a new quotation using the main \"New\""
" button on the top left."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "1. Go to the Barcode App, and select \"Operations\"."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "1. Go to the Purchase App."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "1. Point of sale"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "1. View results"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"1. We can now go through your weekly replenishment report to ensure a good "
"supply in your shop. To do so, navigate to the Inventory App &gt; Operations"
" &gt; Replenishment."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"1. Welcome Elise, your favorite customer, she wants to buy the \"Da Vinci "
"Code\"."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"1. You welcome now Hanna Liseuse, but she has a very specific need. She's "
"looking for \"Media Burn: Ant Farm and the Making of an Image\". You don't "
"have this book in stock, let's order it."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"2. By clicking on the search bar, you can select the \"Weekly "
"replenishment\" filter. It displays every products sold this week and their "
"remaining quantity."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "2. Control your stock"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "2. Create a new request for quotation, and start adding your products."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "2. Customer orders"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "2. Elise also wants to buy a 50€ Gift Card for a friend."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "2. In \"Receipts\", create a new reception by clicking the \"New\" button."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"2. Once you've found it on your local books database, go to the sales app and create a new product using its ISBN code : 9781941753354. Define the current distributor in your region by going to the \"Purchase\" tab (use \"My Book\n"
"                Distributor\" for this case)."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"2. They want to get an offer for three copies of \"Harry Potter and the "
"Philosophers Stone,\" \"Hidden Life of Trees,\" and \"Sapiens: Brief History"
" of Humankind.\""
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"3. Choose how much of each you want to buy by entering the value in the \"To"
" Order\" column."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "3. Elise has a customer account; select her using the \"Customer\" button."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "3. Institutional customers"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "3. Once your quotation is ready, you can email it or confirm it."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"3. Scan your products. If it does not exist in your database, they will be "
"created with the correct picture, title, and price."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "3. Set the quantity in negative value to create a reverted purchase."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"3. You can now create a new quotation. Type her name in the \"Customer\" "
"field to create a new contact."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"4. Add the product to your quotation by searching for its ISBN. You can "
"directly click on the confirm button to turn your quotation in a Sales "
"Order."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"4. Check the smart buttons, a new delivery has been created, but you need to"
" have all products in stock."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "4. Click then on the \"Order\" button to add it to your purchase orders."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"4. Enter the cost of each new product so you can easily reorder it "
"afterward."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"4. Register Elise's payment. You can now go back to your main dashboard by "
"selecting \"Backend\" in the top-right burger menu."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "4. Weekly replenishment"
msgstr ""

#. module: bookstore
#: model:loyalty.reward,description:bookstore.loyalty_reward_2
#: model:product.template,name:bookstore.product_product_17_product_template
msgid "5% on your order"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "5. Encode new release"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"5. Go to the Inventory App. A new Replenishment is suggested under "
"Operations &gt; Replenishment."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"5. Navigate to the Purchase App. You can see that all purchase orders have "
"been created and are ready to be sent to your distributors. Enter your "
"purchase orders and send them to your vendors using the Send by Email "
"button."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"5. Validate the reception, and this is it! (A purchase order will be "
"automatically created so you can easily reconcile your vendor bills)."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"6. If you select your product and click on \"Order\", a new Purchase Order "
"is created, you can then email it to your distributor and confirm the order "
"placement."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "6. Returns"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"7. A few days later, you receive your order from the distributor. Go to the "
"Inventory App on you dashboard, you'll see that a new receipt is ready to be"
" processed. Select it and validate your reception."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "7. Monitoring &amp; reporting"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"8. The last product is now in stock, you can go back to your Sales Order and"
" process the delivery by clicking the \"Delivery\" smart button."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">📖</i>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"<span class=\"display-4-fs\">\n"
"                        <strong>\n"
"                        <font class=\"text-800\"><span class=\"h1-fs\">Use Case</span></font>\n"
"                        </strong>\n"
"                </span>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "<span class=\"h1-fs\">Do you want to go further?</span>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"<strong>\n"
"                        <span class=\"display-4-fs\"><font class=\"text-800\">Odoo for Bookstore</font></span>\n"
"                </strong>"
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_11_product_template
msgid ""
"A NEW YORK TIMES WASHINGTON POST AND WALL STREET JOURNAL BESTSELLER - One of"
" the most beloved books of our time: an illuminating account of the forest "
"and the science that shows us how trees communicate feel and live in social "
"networks. After reading this book a walk in the woods will never be the same"
" again. Breaks entirely new ground.. [Peter Wohlleben] has listened to trees"
" and decoded their language. Now he speaks for them. --The New York Review "
"of Books NAMED ONE OF THE BEST BOOKS OF THE YEAR BY BRAINPICKINGS - "
"HONORABLE MENTION: SEJ Rachel Carson Environment Book Award - Shortlisted: "
"Audible International Book of the Year Award - Books For a Better Life Award"
" - Indie Choice Award--Nonfiction Book of the Year Are trees social beings? "
"In The Hidden Life of Trees forester and author Peter Wohlleben convincingly"
" makes the case that yes the forest is a social network. He draws on "
"groundbreaking scientific discoveries to describe how trees are like human "
"families: tree parents live together with their children communicate with "
"them support them as they grow share nutrients with those who are sick or "
"struggling and even warn each other of impending dangers. Wohlleben also "
"shares his deep love of woods and forests explaining the amazing processes "
"of life death and regeneration that he has observed in his woodland. A "
"declaration of love and an engrossing primer on trees brimming with facts "
"and an unashamed awe for nature. --Washington Post Heavily dusted with the "
"glitter of wonderment. --The New Yorker Includes a Note From a Forest "
"Scientist by Dr.Suzanne Simard Published in Partnership with the David "
"Suzuki Institute."
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_20_product_template
msgid ""
"A detailed account of Ant Farm s 1975 Media Burn performance a legendary act"
" of consumerist critique This book examines the complex set of cultural "
"references and art-making strategies informing Ant Farm s seminal 1975 "
"performance Media Burn in which a customized Cadillac dubbed the Phantom "
"Dream Car was driven through a wall of burning television sets. Originally "
"conceived as a conceptual architectural practice Ant Farm evolved into a "
"full-service art collaborative culminating in such notable works as House of"
" the Century (1971-73) Cadillac Ranch (1974) and The Eternal Frame (1975). "
"In Media Burn the artists flourished in a rich tumult of ideas that engaged "
"contemporary media theory an oddly complicated aesthetic spectacle textual "
"appropriation and an all-encompassing branding effort. Written by Steve Seid"
" (Berkeley Art Museum/Pacific Film Archive) and drawing upon a rich visual "
"documentation this book delves into the little-known critical backstory to "
"this influential performance (and video work) involving a massive effort to "
"mount a subversive critique of media hegemony while continually re-imagining"
" the crux of the performance itself."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"A free, personalized, integrated <strong><font class=\"text-o-color-1\">Website</font></strong> in a few clicks. Start receiving orders directly in Odoo and go live with your \n"
"                        <strong><font class=\"text-o-color-1\">E-shop</font></strong> in a few minutes."
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_9_product_template
msgid ""
"A hand-inspected Used copy of \"Da Vinci Code\" by Dan Brown. Ships directly"
" from Textbooks.com."
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_3_product_template
msgid "André Chamay (Auteur) - Paru le 01/06/2019 chez Jets D'encre."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"As monitoring your success is essential, there are some views that might "
"interest you:"
msgstr ""

#. module: bookstore
#: model:ir.model.fields,field_description:bookstore.x_author_template_field
msgid "Author"
msgstr "Autor"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Basics"
msgstr "Základní"

#. module: bookstore
#: model:pos.category,name:bookstore.pos_category_1
msgid "Books"
msgstr "Knihy"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products and 2 sample contacts."
msgstr ""

#. module: bookstore
#: model:pos.payment.method,name:bookstore.pos_payment_method_1
msgid "Cash"
msgstr "Hotovost"

#. module: bookstore
#: model:account.journal,name:bookstore.cash
msgid "Cash (Bookstore)"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Control the aging of your stock, and easily export aged stock in the "
"inventory valuation report (Inventory &gt; Reporting &gt; Valuation)."
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_9_product_template
msgid "Da Vinci Code"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_3_product_template
msgid "De Sapiens En Sapiens"
msgstr ""

#. module: bookstore
#: model:product.pricelist,name:bookstore.product_pricelist_1
msgid "Default"
msgstr "Výchozí"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Do you want to go further?"
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_14_product_template
msgid ""
"Excellent Marketplace listings for \"Narratives of Exile and Return\" by "
"Chamber starting as low as $92.46!"
msgstr ""

#. module: bookstore
#: model:ir.actions.server,name:bookstore.inventory_price_update
#: model:ir.actions.server,name:bookstore.ir_act_server_set_condominiums_on_partner
msgid "Execute Code"
msgstr "Provést kód"

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_10_product_template
msgid "First British Limited Edition hardcover, Bloomsbury Press."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "From the Point of sale, open your register on the main dashboard."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Get your stock level at anytime in Inventory &gt; Reporting &gt; Stock"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_10_product_template
msgid "Harry Potter and the Philosophers Stone"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Hello there!"
msgstr ""

#. module: bookstore
#: model:ir.model.fields,field_description:bookstore.x_isbn_barcode_field
#: model_terms:ir.ui.view,arch_db:bookstore.product_template_product_form_customization_view
msgid "ISBN"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"If you look for Elise in your \"Contact\" database, you can see that the Odoo Smart Buttons at the top of her page can bring you to her customer history, Elise may even see it on her website account if you activate the\n"
"                        relevant apps and configurations."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "If you want to sale consignment products, this is standard in Odoo."
msgstr ""

#. module: bookstore
#: model:product.pricelist,name:bookstore.product_pricelist_2
msgid "Institutions"
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_13_product_template
msgid ""
"It is the year 1327. Franciscans in an Italian abbey are suspected of "
"heresy, but Brother William of Baskerville’s investigation is suddenly "
"overshadowed by seven bizarre deaths. Translated by William Weaver. A Helen "
"and Kurt Wolff Book."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.stock_warehouse_orderpoint_reorder_search_customization_view
msgid "Last 7 Days"
msgstr "Posledních 7 dní"

#. module: bookstore
#: model:ir.model.fields,field_description:bookstore.x_last_sale_date_field
#: model:ir.model.fields,field_description:bookstore.x_last_sale_stock_field
msgid "Last Sale"
msgstr ""

#. module: bookstore
#: model:ir.model.fields,field_description:bookstore.x_last_vendor_template_field
msgid "Last Vendor"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Learn more"
msgstr "Další informace"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Let's say you now need to create a quotation for \"The Great Library\" near "
"your shop."
msgstr ""

#. module: bookstore
#: model:loyalty.program,name:bookstore.loyalty_program_2
msgid "Loyalty Cards"
msgstr "Věrnostní karty"

#. module: bookstore
#: model:loyalty.program,portal_point_name:bookstore.loyalty_program_2
msgid "Loyalty point(s)"
msgstr "Věrnostní bod(y)"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <strong><font class=\"text-o-color-1\">Marketing Automation</font></strong>, <strong><font class=\"text-o-color-1\">Email Marketing</font></strong>, \n"
"                        <strong><font class=\"text-o-color-1\">Social Media Marketing</font></strong>, and <strong><font class=\"text-o-color-1\">SMS Marketing</font></strong>."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_20_product_template
msgid "Media Burn: Ant Farm and the Making of an Image (Paperback)"
msgstr ""

#. module: bookstore
#: model_terms:product.template,description:bookstore.product_product_12_product_template
msgid ""
"Most startups are built to fail. But those failures according to "
"entrepreneur Eric Ries are preventable. Startups don t fail because of bad "
"execution or missed deadlines or blown budgets. They fail because they are "
"building something nobody wants. Whether they arise from someone s garage or"
" are created within a mature Fortune 500 organization new ventures by "
"definition are designed to create new products or services under conditions "
"of extreme uncertainly. Their primary mission is to find out what customers "
"ultimately will buy. One of the central premises of The Lean Startup "
"movement is what Ries calls validated learning about the customer. It is a "
"way of getting continuous feedback from customers so that the company can "
"shift directions or alter its plans inch by inch minute by minute. Rather "
"than creating an elaborate business plan and a product-centric approach Lean"
" Startup prizes testing your vision continuously with your customers and "
"making constant adjustments -- Every so often a business book comes along "
"that changes how we think about innovation and entrepreneurship. Eric Ries s"
" The Lean Startup has the chops to join this exalted company."
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_14_product_template
msgid "Narratives of Exile and Return"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Navigate to Inventory &gt; Reporting &gt; Performance to get insight on each"
" books categories"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Odoo for Bookstore"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional scanner (and much more)."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Or easier, download the Odoo App on your mobile phone, connect to your "
"database, and select the Barcode App to transform your phone into an "
"additional scanner!<br/>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the<strong>"
" <font class=\"text-o-color-1\">Events App</font></strong>."
msgstr ""

#. module: bookstore
#: model:ir.model.fields,field_description:bookstore.x_purchase_type_field
msgid "Purchase type"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_12_product_template
msgid "The Lean Startup"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_11_product_template
msgid "The Mysteries of Nature: the Hidden Life of Trees (Hardcover)"
msgstr ""

#. module: bookstore
#: model:product.template,name:bookstore.product_product_13_product_template
msgid "The Name of the Rose"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"This time, you received a new release from one of your distributors. Prepare"
" your scanner and go to the Barcode App."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Time to return some unsold products. This works like a reverted purchase "
"order."
msgstr ""

#. module: bookstore
#: model:base.automation,name:bookstore.update_inventory_description
msgid "Update Inventory Description"
msgstr ""

#. module: bookstore
#: model:base.automation,name:bookstore.base_automation_1
msgid "Update supplier info on incoming products"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Use Case"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Barcode "
"App</font></strong><font class=\"text-o-color-1\"> </font>to scan your "
"products and create new entries directly from this App to encode new books "
"faster than ever."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Inventory App</font></strong>"
" to manage your orders receive and deliver your products."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Point of Sale</font></strong>"
" for your sales at the desk. Manage your customer accounts and advantage in "
"the <strong><font class=\"text-o-color-1\">Contacts App</font></strong>."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Purchase App</font></strong> "
"to order books from your distributors."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the <strong><font class=\"text-o-color-1\">Sales App</font></strong> to order books for your customers, create quotations for your institutional clients (libraries, schools, etc.), and invoice them on the go using\n"
"                        the <strong><font class=\"text-o-color-1\">Invoicing App</font></strong>."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"Use the internal link button, at the right of the field, to view the new "
"contact record created, and don't forget to add an email address for her to "
"receive notifications. Go back to your quotation using the breadcrump."
msgstr ""

#. module: bookstore
#: model:pos.category,name:bookstore.pos_category_2
msgid "Various"
msgstr ""

#. module: bookstore
#: model_terms:web_tour.tour,rainbow_man_message:bookstore.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You can also use automated Reordering rules to ensure you will always have "
"your best seller in stock."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You can make a down payment using the Point of Sales. To do so, just go back"
" to this app and click on the Quotation/Order button to retreive the Sales "
"Order."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You can now confirm the purchase, validate the delivery using the smart "
"button, and print your delivery slip."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You can see that Elise has a Loyalty Card. The demo Library applies a 5% "
"discount for every 200€ spent. Manage Promotions, and Loyalty programs under"
" Point of Sale &gt; Products. <br/>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Bookstore package and check the related box."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You just installed the Odoo for Bookstore package. By doing so, we have "
"installed a bunch of necessary apps to run your bookstore efficiently."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "academy"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "and"
msgstr "and"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "documentation"
msgstr "dokumentace"

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "if you need help!<br/>"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "request a demo"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "​Track your Point of Sales results in Point of Sales &gt; Reporting"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid ""
"​Track your purchases performance in Purchase App &gt; Reporting &gt; "
"Purchase"
msgstr ""

#. module: bookstore
#: model_terms:ir.ui.view,arch_db:bookstore.welcome_article_body
msgid "​Track your sales order in Sales App &gt; Reporting &gt; Sales"
msgstr ""
