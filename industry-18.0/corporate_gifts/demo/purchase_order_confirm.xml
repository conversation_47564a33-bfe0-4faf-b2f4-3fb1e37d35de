<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="purchase.order" name="button_confirm">
        <value eval="[
            ref('purchase_order_1'),
            ref('purchase_order_2'),
            ref('purchase_order_3')]"
        />
    </function>

    <function model="stock.picking" name="button_validate">
        <value model="stock.picking" eval="
            obj().env.ref('corporate_gifts.purchase_order_1').group_id.stock_move_ids.picking_id.ids"/>
    </function>
</odoo>
