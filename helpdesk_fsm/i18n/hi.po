# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_fsm
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create & View Task"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create Task"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/models/helpdesk_ticket.py:0
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_create_fsm_task
msgid "Create a Field Service task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_uid
msgid "Created by"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_date
msgid "Created on"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Customer"
msgstr "साथी"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Discard"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__display_name
msgid "Display Name"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_team__fsm_project_id
msgid "FSM Project"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_team
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__id
msgid "ID"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_uid
msgid "Last Updated by"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_date
msgid "Last Updated on"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__helpdesk_ticket_id
msgid "Original Ticket"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Plan Intervention"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_team_view_form
msgid "Project"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
msgid "Project in which to create the task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__helpdesk_ticket_id
msgid "Related ticket"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: model:ir.model,name:helpdesk_fsm.model_project_task
msgid "Task"
msgstr ""

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_canceled
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_canceled
msgid "Task Cancelled"
msgstr ""

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_done
msgid "Task Done"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
msgid "Task created"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: code:addons/helpdesk_fsm/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Tasks"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
msgid "Tasks from Tickets"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
msgid "Tasks generated from this ticket"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket from this task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Ticket's customer, will be linked to the task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__name
msgid "Title"
msgstr ""
