# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# jabiri7, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# erii<PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# Jon<PERSON>n <PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <carles<PERSON><EMAIL>>, 2024
# <PERSON><PERSON>ianCruz<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Comptes"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "%(media_name)s (max %(max_chars)s chars)"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ", you must first link a social media."
msgstr ". primer has d'enllaçar un mitjà de contingut multimèdia."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Hi ha hagut un error en intentar enllaçar el vostre compte</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr ""

#. module: social
#: model_terms:web_tour.tour,rainbow_man_message:social.social_tour
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Felicitats. Torna en uns minuts per comprovar les "
"estadístiques.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Actiu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add"
msgstr "Afegir"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Afegeix una publicació"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add Stream"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Add a Stream"
msgstr "Afegir una transmissió en viu"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Afegir una transmissió en viu"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add an image"
msgstr "Afegir una imatge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "All Companies"
msgstr "Totes les empreses"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Totes les publicacions relacionades amb les xarxes socials"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Comptes permesos"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "¿Hi ha comptes disponibles?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Imatges adjuntes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__image_ids
msgid "Attached Images"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
msgid "Audience"
msgstr "Audiència"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Tendències de l'audiència"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Enllaç del autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Nom de l'autor "

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid "Author link to the external social.media (ex: link to the X Account)."
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr ""
"Abans de publicar, els enllaços es convertiran per a poder fer el seguiment."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Per Transmissió en viu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF Token"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Data del calendari"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Campanya"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Campanyes"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Les campanyes s'utilitzen per centralitzar els vostres esforços de "
"màrqueting i fer un seguiment dels seus resultats."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Cancel"
msgstr "Cancel·la"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Trieu quin <b>compte</b> voleu enllaçar primer."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Click to refresh."
msgstr "Feu clic per actualitzar."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Clics"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Clicks:"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Comment Image"
msgstr "Comentar imatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Empresa"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Configuració"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Connecting Problem"
msgstr "Problema de connexió"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' X account"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Contingut del missatge de l'entrada social que s'ha post-processat (es "
"redueixen els enllaços, UTMs, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr "Controla si els fluxos socials es gestionen a les xarxes socials."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Controla si podem enllaçar comptes o no."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Crea una campanya"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Crea una publicació"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Creeu altres comptes per a %(media_names)s per a aquesta empresa o demaneu a"
" %(company_names)s que comparteixin els seus comptes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Creat el"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Defineix si aquest compte té estadístiques d'Audiència/Engagements/Estòries.\n"
"        El compte amb estadístiques es mostra al tauler."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Defineix si aquest compte té tendència a les estadístiques o no."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Delete"
msgstr "Eliminar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Delete Comment"
msgstr "Esborra el comentari"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Delete Post"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Mode de prova"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Descripció"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Comptes per a desenvolupadors"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Discard"
msgstr "Descartar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__display_message
#: model:ir.model.fields,field_description:social.field_social_post_template__display_message
msgid "Display Message"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Do you really want to delete this %s?"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Do you really want to delete this Post ?"
msgstr ""

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Esborrany"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"A causa de les restriccions de longitud, no es poden enviar les publicacions següents:\n"
" %s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit"
msgstr "Modificar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit Your Post"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Habilitar el mode de prova"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Engagement"
msgstr "Compromís"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Tendència de compromís"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Engagement:"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Cada vegada que es crea un compte, també es crea un utm.medium i està "
"enllaçat al compte"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Fallits"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Raó de la fallada"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Feed Posts"
msgstr "Publicacions del feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Recompte de publicacions del feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Data de publicació amb format"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Audiència general del compte social (Page Likes, Account Follows, ...)."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Anar a la"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Nan / Nom curt"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Happy with the result? Let's post it!"
msgstr "T'agrada el resultat? Publica'l!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Té estadístiques de compte"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "¿Té tendències?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "Ombrejat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Imatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "URL de la imatge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Image Url"
msgstr "URL de la imatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "Enllaços de les imatges"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Imatges que s'han compartit amb aquesta publicació."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Insights"
msgstr "Estadístiques"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Sembla que hi ha un problema amb l'enllaç de les xarxes socials, feu Clic "
"aquí per enllaçar el compte de nou"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "It will appear in the Feed once it has posts to display."
msgstr "Apareixerà al canal una vegada que tingui publicacions per mostrar."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's <b>connect</b> to Facebook, LinkedIn or X."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Creem el vostre propi tauler de <b>xarxes socials</b>."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's start posting."
msgstr "Comença a publicar."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Like"
msgstr "M'agrada"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Likes"
msgstr "M'agrada"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Enllaçar una descripció"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Link Image"
msgstr "Enllaçar una imatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL de la imatge de l'enllaç"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Enllaçar un títol"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "URL de l'enllaç"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Link a new account"
msgstr "Enllaça un compte nou"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Enllaçar un compte"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Enllaça un compte"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Enllaça un compte a una empresa per restringir el seu ús o mantenir-lo buit "
"per permetre que totes les empreses l'utilitzin."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Enllaç de la publicació en directe al mitjà de destinació."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Enllaçar comptes de xarxes socials"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Enllaç a les estadístiques externes del compte social"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "L'enllaç amb les xarxes socials externes està trencat"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr ""
"Llista dels comptes que es poden seleccionar per a aquesta publicació."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Publicacions en directe per xarxes socials"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Load more comments..."
msgstr "Carregar més comentaris..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Longitud màxima de la publicació"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__media_type
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Contingut multimèdia"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_count
#: model:ir.model.fields,field_description:social.field_social_post_template__media_count
msgid "Media Count"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Tipus de contingut multimèdia"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Missatge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Message posted"
msgstr "Missatge penjat"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"La majoria de publicacions socials van directament de Ready a Posted/Failed, ja que són el resultat d'una única trucada a l'API de tercers.\n"
"        També hi ha disponible un estat «Posició» per a aquells que s'envien per lots (com les notificacions push)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Les meves publicacions"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Les meves transmissions en viu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Nom"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New Post"
msgstr "Nova publicació"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New content available"
msgstr "Nou contingut disponible"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Next"
msgstr "Següent"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Encara no hi ha cap compte social!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "¡Encara no hi ha fluxos socials!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Encara no s'ha afegit cap transmissió en viu!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "No comments yet."
msgstr "Encara no hi ha més comentaris."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "No social accounts configured, please contact your administrator."
msgstr ""
"No hi ha comptes de xarxes socials configurades. Contacta el teu "
"administrador, siusplau."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Number of Followers of your channel"
msgstr "Nombre de seguidors del teu canal"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Nombre de clics"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Nombre d'interaccions (m'agrada, accions, comentaris...) amb les "
"publicacions socials"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr ""
"Nombre de persones que es comprometen amb la publicació (lletres, "
"comentaris...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr ""
"Nombre de persones que es comprometen amb les vostres publicacions "
"(lliuraments, comentaris...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Reposts, ...)."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Nombre de vegades que la gent ha interaccionat amb les teves publicacions "
"(m'agrada, comentaris, comparticions,...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Reposts...)"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Or add"
msgstr "O afegeix"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Percentatge d'augment/disminució del públic durant un període definit."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Percentatge d'augment/disminució del compromís durant un període definit."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Percentatge d'augment/disminució de les històries durant un període definit."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Si us plau especifiqueu com a mínim un compte on publicar (per als ID/s de "
"les publicacions)%s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Please specify either a message or upload some images."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post"
msgstr "Publicació"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Post Image"
msgstr "Publica la imatge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.js:0
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
msgid "Post Images"
msgstr "Publica les imatges"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Enllaç de la publicació"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Publica el missatge"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Publica ara"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Enllaç de publicació a la xarxa social externa (ex: enllaç a l'actual "
"publicació de Facebook)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Publicació en"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Publicat"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Publicant"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Publicacions"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Publicacions per compte"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Posts Preview"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to edit. Press Shift+Enter to insert a Line Break."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Premeu Enter per publicar. Premeu Shift+Enter per a inserir un salt de "
"línia."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Previsualitza la publicació"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Previous"
msgstr "Anterior"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "Publica la teva entrada immediatament o programa-la més tard."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Publicat per"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Data de publicació"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Data de publicació"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Preparat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Xarxes socials relacionades"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, X, ...)."
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Compte social relacionat"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Reply"
msgstr "Respondre"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Reintentar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Planificació"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Planifica més tard"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Planificat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Data programada"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Selected accounts (%(account_list)s) do not match the selected company "
"(%(company)s)"
msgstr ""

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Envia ara"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Seqüència utilitzada per ordenar els fluxos (principalment per a la "
"visualització kanban \"Feed\")"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Estableix un nombre màxim de caràcters que es poden publicar a la "
"publicació. 0 sense límit."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Configuració"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Compte social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Comptes socials"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Publicació social en directe"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Gestor Social"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Social Marketing"
msgstr "Marketing Social"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Social Media"
msgstr "Xarxes socials"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Publicacions en les xarxes socials"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Publicació social"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Plantilla de publicació social"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Plantilles de correus socials"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Publicacions socials"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Transmissió en viu social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Publicació de la transmissió en viu social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Adjunt d'imatges després de la transmissió en viu social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Corrents socials"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Usuari social"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "Social: Publica les entrades programades"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
msgid "Something went wrong while posting the comment."
msgstr "Alguna cosa no ha anat bé mentre es publicava el comentari."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"No teniu permisos per tornar a enllaçar aquest compte, contacteu amb el "
"vostre administrador."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Font"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Camp tècnic especial que conté un dict que conté els noms de les "
"publicacions en directe per mitjà dels identificadors de les xarxes socials "
"(utilitzat per a la vista kanban)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_post_split_per_media.xml:0
msgid "Split Per Media"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_split_per_media
#: model:ir.model.fields,field_description:social.field_social_post_template__is_split_per_media
msgid "Split Per Network"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Enllaç d'estadístiques"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Estat"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
msgid "Stories"
msgstr "Històries"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Història"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Stream Added (%s)"
msgstr "S'ha afegit la transmissió en viu (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Publicació en directe"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Transmissió en viu d'imatges"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "L'URL de la transmissió en viu d'imatges"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Tipus de transmissió en viu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Nom del tipus de transmissió en viu (tècnic)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Emissions activades"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr "Subentrades que es publicaran a cada compte social seleccionada."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Sync"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Els comptes en què es publicarà aquesta publicació."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"El nom de l'autor de la publicació basat en informació de tercers (ex: 'John"
" Doe')."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"La publicació es considera com a «Posició», quan tots els seus subentrades "
"(una per compte social) són «Posits» o «Posits»"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "La data de publicació de l'entrada basada en informació de tercers."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"La raó per la qual una publicació no es publica correctament a les xarxes "
"socials (p. ex.: error de connexió, entrada duplicada...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "La imatge de les xarxes socials relacionada"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Les xarxes socials vinculades als comptes socials seleccionats."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Hi ha errors de publicació  a les sub-publicacions"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Aquest testimoni es pot utilitzar per verificar que no s'ha falsificat una "
"sol·licitud entrant d'un proveïdor social."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Títol"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "To add a stream"
msgstr "Per afegir transmissió en viu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Tipus"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campanya UTM"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM Mitjà"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "Font UTM"

#. module: social
#. odoo-python
#: code:addons/social/controllers/main.py:0
msgid "Uh-oh! It looks like this message has been deleted from X."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Unknown error"
msgstr "Error desconegut"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Uploaded file does not seem to be a valid image."
msgstr "El fitxer pujat no sembla una imatge vàlida."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_live_post__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "View"
msgstr "Vista"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Data i hora"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Quan es va publicar l'entrada global. Les dates de publicació de les sub-"
"publicacions reals poden ser diferents depenent dels mitjans de comunicació."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Adjuntarà imatges a les publicacions (si les xarxes socials ho admeten)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a comment..."
msgstr "Escriu un comentari..."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Write a message or upload an image"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Write a message to get a preview of your post."
msgstr ""
"Escriu un missatge per obtenir una vista prèvia de la teva publicació."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a reply..."
msgstr "Escriu una resposta..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Escriviu una publicació d'atractiu, afegiu imatges i programeu-la per a "
"publicar-la més tard en múltiples plataformes alhora."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"No podeu suprimir aquests suports UTM perquè estan enllaçats amb els següents comptes socials a Social:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"No podeu suprimir aquestes fonts UTM perquè estan enllaçades a publicacions socials en Social:\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot reschedule a post that has already been posted."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot schedule a post in the past."
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "La teva publicació"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "a Stream from an existing account"
msgstr "una transmissió en viu des d'un compte existent"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "abans de publicar."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "comment/reply"
msgstr "comentari/resposta"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "tauler"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "for"
msgstr "per"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "replies..."
msgstr "respostes..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"vigilar les vostres propies publicacions i controlar totes les activitats "
"socials."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "per a enllaçar els vostres comptes i començar a publicar."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "per començar a enviar missatges des d'Odoo."
