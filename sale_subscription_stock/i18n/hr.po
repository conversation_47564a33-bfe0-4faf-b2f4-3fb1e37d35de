# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription_stock
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: sale_subscription_stock
#: model:product.template,name:sale_subscription_stock.product_recurring_detergent_product_template
msgid "Detergent (SUB)"
msgstr ""

#. module: sale_subscription_stock
#: model:ir.model.fields,field_description:sale_subscription_stock.field_sale_order__display_recurring_stock_delivery_warning
msgid "Display Recurring Stock Delivery Warning"
msgstr ""

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.product_template_form_view
msgid ""
"Recurring order with this product will be invoiced at the beginning of the "
"period."
msgstr ""

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajnog naloga"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Izvještaj o nadopuni skladišta"

#. module: sale_subscription_stock
#. odoo-javascript
#: code:addons/sale_subscription_stock/static/src/report_stock_forecasted.xml:0
msgid "Subscriptions"
msgstr "Pretplate"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.sale_subscription_order_view_form
msgid ""
"The delivery order of the recurring product(s) will be created soon. If another delivery order exists,\n"
"                    recurring product will be added to it automatically."
msgstr ""

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_picking
msgid "Transfer"
msgstr "Prijenos"
