# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_twitter
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_post_template.py:0
msgid "%(current_length)s / %(max_length)s characters to fit in a Post"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "11m"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "<b class=\"text-900\">X Account</b>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a post\"/>\n"
"                                    <span>Quote</span>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-retweet me-1\" title=\"Repost a post\"/>"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "A repost already exists"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "Authentication failed. Please enter valid credentials."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Author Image"
msgstr "תמונת כותב"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid ""
"Can not like / unlike the tweet\n"
"%s."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "Cancel"
msgstr "בטל"

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_res_config_settings__twitter_use_own_account
msgid ""
"Check this if you want to use your personal X Developer Account instead of "
"the provided one."
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Key"
msgstr "שם משתמש"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Secret Key"
msgstr "סיסמה"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__description
msgid "Description"
msgstr "תיאור"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__display_twitter_preview
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__display_twitter_preview
msgid "Display X Preview"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comments.js:0
msgid ""
"Easy, tiger! No spamming allowed. Let's stick to three replies per Tweet."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "Empty post"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
msgid "Error"
msgstr "שגיאה"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_twitter_quote.js:0
msgid "Error while sending the data to the server."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid ""
"Failed to delete the post\n"
"%s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid ""
"Failed to fetch the posts in the same thread: '%(text)s' using the account "
"%(account)s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "Failed to post comment: %(error)s with the account %(account)s."
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_likes
msgid "Favorites of"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__has_twitter_account
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__has_twitter_account
msgid "Has X Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__id
msgid "ID"
msgstr "מזהה"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__image
msgid "Image"
msgstr "תמונה"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_post_template_view_form
msgid "Images"
msgstr "תמונות"

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_keyword
msgid "Keyword"
msgstr "מילת מפתח"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "Likes"
msgstr "לייקים"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
msgid ""
"Looks like you've made too many requests. Please wait a few minutes before "
"giving it another try."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_media__media_type
msgid "Media Type"
msgstr "סוג מדיה"

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_user_mentions
msgid "Mentions"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_post_template_view_form
msgid "Message"
msgstr "הודעה"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__name
msgid "Name"
msgstr "שם"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
msgid "Please select a X account for this stream type."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_post_template.py:0
msgid "Please specify either an X Message or upload some X Images."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "Post"
msgstr "שלח"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "Post Image"
msgstr "תמונת פוסט"

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_follow
msgid "Posts of"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
msgid "Quote a Tweet"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_link
msgid "Quoted post author Link"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_name
msgid "Quoted post author Name"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_message
msgid "Quoted post message"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_profile_image_url
msgid "Quoted post profile image URL"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "Read More about X Accounts"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "Replies from posts older than 7 days must be accessed on Twitter.com"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Repost"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Repost or Quote"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweet_count
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Reposts"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_searched_keyword
msgid "Search Keyword"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_search
msgid "Search User"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_searched_by_id
msgid "Searched by"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_account
msgid "Social Account"
msgstr "חשבון"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_live_post
msgid "Social Live Post"
msgstr "פוסט חי"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_media
msgid "Social Media"
msgstr "רשתות חברתיות"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post
msgid "Social Post"
msgstr "פוסט "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post_template
msgid "Social Post Template"
msgstr "תבנית פוסט חברתי"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream
msgid "Social Stream"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream_post
msgid "Social Stream Post"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_twitter_account
msgid "Social X Account"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
msgid ""
"The keyword you've typed in does not look valid. Please try again with other"
" words."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "This Post has been deleted."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "This post is outdated, please refresh the stream and try again."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comment.js:0
msgid "Tweet"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_post_template_view_form
msgid "Twitter"
msgstr "טוויטר"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
msgid "Twitter Comments"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_id
msgid "Twitter Followed Account"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Undo Repost"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "Unknown"
msgstr "לא ידוע"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "Unknown error"
msgstr "שגיאה לא ידועה"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_use_own_account
msgid "Use your own X Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_account.py:0
msgid ""
"We could not upload your image, it may be corrupted, it may exceed size "
"limit or API may have send improper response (error: %s)."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_social_post__twitter_image_ids
#: model:ir.model.fields,help:social_twitter.field_social_post_template__twitter_image_ids
msgid "Will attach images to your posts."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_author_id
msgid "X Author ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_key
msgid "X Consumer Key"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_secret_key
msgid "X Consumer Secret Key"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_conversation_id
msgid "X Conversation ID"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "X Developer Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_id
msgid "X ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_image_ids
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_image_ids
msgid "X Images"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_likes_count
msgid "X Likes"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_message
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_message
msgid "X Message"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token
msgid "X OAuth Token"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token_secret
msgid "X OAuth Token Secret"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_tweet_id
msgid "X Post ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__is_twitter_post_limit_exceed
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__is_twitter_post_limit_exceed
msgid "X Post Limit Exceeded"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_post_limit_message
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_post_limit_message
msgid "X Post Limit Message"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_preview
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_preview
msgid "X Preview"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
msgid "X Profile Image"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_profile_image_url
msgid "X Profile Image URL"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_id_str
msgid "X Quoted post ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_comments_count
msgid "X Replies"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweeted_tweet_id_str
msgid "X Repost ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_can_retweet
msgid "X Repost Permission"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_screen_name
msgid "X Screen Name"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_user_id
msgid "X User ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_user_likes
msgid "X User Likes"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "X did not provide a valid access token or it may have expired."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
msgid "X did not provide a valid access token."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_live_post__twitter_tweet_id
msgid "X post id"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
msgid "You are not authenticated"
msgstr ""

#. module: social_twitter
#: model:ir.model.constraint,message:social_twitter.constraint_social_stream_post_tweet_uniq
msgid "You can not store two times the same post on the same stream!"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
msgid ""
"You cannot create a Stream from this X account.\n"
"It may be because it's protected. To solve this, please make sure you follow it before trying again."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "אין לך מנוי פעיל. אנא רכוש אחד כאן: %s"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
msgid ""
"You need to add the following callback URL to your X application settings: "
"%s"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_view_form
msgid "e.g. #odoo"
msgstr ""
