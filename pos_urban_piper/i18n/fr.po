# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_urban_piper
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid ""
"(%(provider_name)s) delivery is only available in the following countries: \n"
"%(country_names)s"
msgstr ""
"(%(provider_name)s) La livraison est uniquement disponible dans les pays suivants :\n"
"%(country_names)s"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Accept"
msgstr "Accepter"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__acknowledged
msgid "Acknowledged"
msgstr "Confirmé"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Un identifiant interne du point de vente."

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Api Key"
msgstr "Clé API"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__available_country_ids
msgid "Available Countries"
msgstr "Pays disponibles"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urbanpiper_pos_config_ids
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urbanpiper_pos_config_ids
msgid "Available on Food Delivery"
msgstr "Disponilbe pour la livraison de nourriture"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/product_screen/product_screen.js:0
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/product_screen/product_screen.xml:0
msgid "Back"
msgstr "Retour"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__cancelled
msgid "Cancelled"
msgstr "Annulé"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Channel"
msgstr "Chaîne"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urbanpiper_pos_config_ids
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urbanpiper_pos_config_ids
msgid "Check this if the product is available for food delivery."
msgstr "Cochez cette case si le produit peut être livré."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_payment_method__is_delivery_payment
msgid "Check this if this payment method is used for online delivery orders."
msgstr ""
"Cochez cette case si ce mode de paiement est utilisé pour les bons de "
"livraison en ligne."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Close"
msgstr "Fermer"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__completed
msgid "Completed"
msgstr "Terminé"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Connectivity Issue"
msgstr "Problème de connectivité"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__available_country_ids
msgid "Countries where this provider is available"
msgstr "Pays pour lesquels ce fournisseur est disponible"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Create Store"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__create_uid
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__create_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__create_date
msgid "Created on"
msgstr "Créé le"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Customer Email"
msgstr "E-mail client"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Customer Info"
msgstr "Info client"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Customer Name"
msgstr "Nom du client"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Customer Phone"
msgstr "Téléphone du client"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Delivery Address"
msgstr "Adresse de livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Delivery Channel"
msgstr "Canal de distribution"

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_delivery_charges_product_template
msgid "Delivery Charges"
msgstr "Frais de livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_identifier
msgid "Delivery ID"
msgstr "ID de livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_json
msgid "Delivery JSON"
msgstr "JSON livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Delivery Order Status"
msgstr "Statut du bon de livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Delivery Partner"
msgstr "Partenaire de livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_payment_method__is_delivery_payment
msgid "Delivery Payment"
msgstr "Paiement de livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Delivery Person"
msgstr "Livreur"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_provider_id
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_payment_method__delivery_provider_id
msgid "Delivery Provider"
msgstr "Prestataire de livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_delivery_provider_ids
msgid "Delivery Providers"
msgstr "Prestataires de livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_rider_json
msgid "Delivery Rider JSON"
msgstr "JSON Livreur"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_status
msgid "Delivery Status"
msgstr "Statut de livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Delivery Time/Time-Slot"
msgstr "Délai de livraison / créneau horaire"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Details"
msgstr "Détails"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__dispatched
msgid "Dispatched"
msgstr "Expédié"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__display_name
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "Done"
msgstr "Terminé"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Edit"
msgstr "Modifier"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__3
msgid "Eggetarian"
msgstr "Ovo-végétarien"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Error"
msgstr "Erreur"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Error to send in preparation display."
msgstr "Erreur à envoyer dans l'affichage de la préparation."

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Fill this form to get Username &amp; Api key"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Fiscal Position"
msgstr "Position fiscale"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_fiscal_position_id
msgid "Fiscal position for Urban Piper sync menu."
msgstr "Position fiscale pour le menu de synchronisation d'Urban Piper."

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Food Delivery Platforms"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__prep_time
msgid "Food Preparation Time"
msgstr "Temps de préparation des aliments"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__food_ready
msgid "Food Ready"
msgstr "Aliments prêts"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Fulfilment Mode"
msgstr "Mode de réalisation"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid "Go to Company configuration"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urban_piper_status_ids
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urban_piper_status_ids
msgid ""
"Handle products with urban piper and pos config - Product is linked or not "
"with appropriate store."
msgstr ""
"Gérer les produits avec urban piper et la configuration du PdV - Le produit "
"est lié ou non au magasin adéquat."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "HungerStation Code"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__id
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__id
msgid "ID"
msgstr "ID"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__is_alcoholic_on_urbanpiper
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__is_alcoholic_on_urbanpiper
msgid "Indicates if the product contains alcohol."
msgstr "Indique si le produit contient de l'alcool."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Info"
msgstr "Infos"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Invalid Product"
msgstr "Produit invalide"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Invalid Variant/Addons"
msgstr "Variantes/dépendances invalides"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__is_alcoholic_on_urbanpiper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__is_alcoholic_on_urbanpiper
msgid "Is Alchoholic"
msgstr "Contient de l'alcool"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__is_product_linked
msgid "Is Product Linked?"
msgstr "Le produit est lié ?"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__is_recommended_on_urbanpiper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__is_recommended_on_urbanpiper
msgid "Is Recommended"
msgstr "Est recommandé"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_rider_json
msgid "JSON data of the delivery rider."
msgstr "Données JSON pour le livreur"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_json
msgid "JSON data of the order."
msgstr "Données JSON de la commande"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__journal_code
msgid "Journal Short Code"
msgstr "Code du journal"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_last_sync_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_last_sync_date
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Last Sync on"
msgstr "Dernière synchronisation le"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__write_uid
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__write_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_last_sync_date
msgid "Last sync date for menu sync."
msgstr "Date de la dernière synchronisation du menu."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Mark as ready"
msgstr "Marquer comme prêt"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urbanpiper_meal_type
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urbanpiper_meal_type
msgid "Meal Type"
msgstr "Type de repas"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__4
msgid "N/A"
msgstr "N/A"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__name
msgid "Name"
msgstr "Nom"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__name
msgid "Name of the delivery provider i.e. Zomato, UberEats, etc."
msgstr "Nom du prestataire de livraison, par ex. Zomato, UberEats, etc."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "New"
msgstr "Nouveau"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "New online order received."
msgstr "Nouvelle commande reçue en ligne."

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__2
msgid "Non-Vegetarian"
msgstr "Non végétarien"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.product_template_only_form_view_inherit_pos_urban_piper
msgid "Not available for online orders."
msgstr "Pas disponible pour les commandes en ligne."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "Ongoing"
msgstr "En cours"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_payment_methods_ids
msgid "Online Delivery Payment Methods"
msgstr "Mode de paiement en ligne pour les livraisons"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_delivery_provider
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_delivery_provider_ids
msgid "Online Delivery Providers"
msgstr "Prestataires de livraison en ligne"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Order ID"
msgstr "ID de la commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Order Info"
msgstr "Informations de commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Order OTP"
msgstr "OTP commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Order Status"
msgstr "Statut de la commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Order Time"
msgstr "Heure de commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Order does not load from server"
msgstr "La commande ne se charge pas depuis le serveur"

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_other_charges_product_template
msgid "Other Charges"
msgstr "Autres frais"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Others"
msgstr "Autres"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Out of Delivery Radius"
msgstr "Hors du rayon de livraison"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Outlet"
msgstr "Point de vente"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "POS ID"
msgstr "ID PdV"

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_packaging_charges_product_template
msgid "Packaging Charges"
msgstr "Frais d'emballage"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_payment_methods_ids
msgid "Payment methods for Urban Piper sync menu."
msgstr "Méthodes de paiement pour le menu de synchronisation d'Urban Piper."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Phone"
msgstr "Téléphone"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__placed
msgid "Placed"
msgstr "Placé(e)"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__name
msgid "Point of Sale"
msgstr "Point de Vente"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuration du point de vente"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_order
msgid "Point of Sale Orders"
msgstr "Commandes du point de vente"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Modes de paiement du point de vente"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Paiements du point de vente"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_session
msgid "Point of Sale Session"
msgstr "Session du point de vente"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_store_identifier
msgid "Pos ID from Urban Piper (Atlas)"
msgstr "ID PdV d'Urban Piper (Atlas)"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_preparation_display_order
msgid "Preparation orders"
msgstr "Préparation des commandes"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__prep_time
msgid "Preparation time for the food as provided by UrbanPiper."
msgstr "Temps de préparation des aliments tel que fourni par UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_pricelist
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Pricelist"
msgstr "Liste de prix"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_pricelist_id
msgid "Pricelist for Urban Piper sync menu."
msgstr "Liste de prix pour le menu de synchronisation d'Urban Piper."

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_template
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__product_tmpl_id
msgid "Product"
msgstr "Produit"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_urban_piper_status__is_product_linked
msgid "Product Status on Urban piper."
msgstr "Statut du produit sur Urban piper."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Product is out of Stock"
msgstr "Produit en rupture de stock"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urbanpiper_meal_type
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urbanpiper_meal_type
msgid "Product type i.e. Veg, Non-Veg, etc."
msgstr "Type de produit par ex. Végé, Non Végé, etc."

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__image_128
msgid "Provider Image"
msgstr "Image prestataire livraison"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__technical_name
msgid "Provider Name"
msgstr "Nom prestataire livraison"

#. module: pos_urban_piper
#: model:ir.model.constraint,message:pos_urban_piper.constraint_pos_delivery_provider_technical_name_uniq
msgid "Provider Name must be unique."
msgstr "Le nom du prestataire de livraison doit être unique."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__is_recommended_on_urbanpiper
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__is_recommended_on_urbanpiper
msgid "Recommended products on food platforms."
msgstr "Produits recommandés sur les plateformes de livraison de repas."

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Refresh webhooks"
msgstr "Actualiser les webhooks"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_webhook_url
msgid "Register Urbanpiper Webhook URL"
msgstr "Enregistrer l'URL du Webhook d'Urbanpiper"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_webhook_url
msgid "Register webhook with Urbanpiper."
msgstr "Enregistrer le webhook auprès d'Urbanpiper."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Reject"
msgstr "Rejeter"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Reject Order"
msgstr "Rejeter la commande"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Rejecting this order is not allowed for \"%(providerName)s\""
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_provider_id
#: model:ir.model.fields,help:pos_urban_piper.field_pos_payment_method__delivery_provider_id
msgid ""
"Responsible delivery provider for online order, e.g., UberEats, Zomato."
msgstr ""
"Prestataire de livraison responsable pour la commande en ligne, par ex. "
"Zomato, UberEats, etc."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Review Orders"
msgstr "Vérifier les commandes"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Rider is Not Available"
msgstr "Le livreur n'est pas disponible"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Set the POS ID in Urban Piper in Locations."
msgstr "Définissez l'ID du PdV dans Urban Piper dans Lieux"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__journal_code
msgid "Short code of the journal to be used for journal creation"
msgstr "Code abrégé du journal à utiliser pour la création du journal"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Status"
msgstr "Statut"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_status
msgid "Status of the order as provided by UrbanPiper."
msgstr "Statut de la commande tel que fourni par UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.constraint,message:pos_urban_piper.constraint_pos_config_urbanpiper_store_identifier_uniq
msgid "Store ID must be unique for every pos configuration."
msgstr ""
"L'identifiant du magasin doit être unique pour chaque configuration PdV."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Store is Busy"
msgstr "Le magasin est occupé"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Store is Closed"
msgstr "Le magasin est fermé"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_webhook_url
msgid "Store webhook url (base url) for security."
msgstr "Url webhook du magasin (url de base) pour des raisons de sécurité."

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urban_piper_status_ids
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urban_piper_status_ids
msgid "Stores"
msgstr "Magasins"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Sync Menu"
msgstr "Menu Sync"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_ir_config_parameter
msgid "System Parameter"
msgstr "Paramètres système"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Talabat Code"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Talabat Short Code"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__technical_name
msgid "Technical name of the provider used by UrbanPiper"
msgstr "Nom technique du prestataire utilisé par UrbanPiper"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__urbanpiper_apikey
msgid "The API key for accessing the UrbanPiper services."
msgstr "La clé API pour accéder aux services UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_store_identifier
msgid "The POS ID associated with UrbanPiper."
msgstr "L'ID du PdV associé à UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_last_sync_date
msgid "The date and time of the last synchronization with UrbanPiper."
msgstr "Date et heure de la dernière synchronisation avec UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_delivery_provider_ids
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_delivery_provider_ids
msgid "The delivery providers used for online delivery through UrbanPiper."
msgstr ""
"Les prestataires de livraison utilisés pour la livraison en ligne via "
"UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_fiscal_position_id
msgid "The fiscal position used for UrbanPiper transactions."
msgstr "La position fiscale utilisée pour les transactions UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_payment_methods_ids
msgid "The payment methods used for online delivery through UrbanPiper."
msgstr ""
"Les modes de paiement utilisés pour les livraisons en ligne via UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_pricelist_id
msgid "The pricelist used for UrbanPiper orders."
msgstr "La liste de prix utilisée pour les commandes d'UrbanPiper."

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__urbanpiper_username
msgid "The username for the UrbanPiper account."
msgstr "Le nom d'utilisateur du compte UrbanPiper."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "There are no active order(s)."
msgstr "Il n'y a pas de commande(s) active(s)."

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Total Missmatch"
msgstr "Incohérence du total"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_identifier
msgid "Unique delivery ID provided by UrbanPiper."
msgstr "ID de livraison unique fourni par UrbanPiper."

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.product_template_only_form_view_inherit_pos_urban_piper
msgid "Urban Piper"
msgstr "Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__config_id
msgid "Urban Piper Config"
msgstr "Configuration Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_fiscal_position_id
msgid "Urban Piper Fiscal Position"
msgstr "Position fiscale Urban Piper"

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Urban Piper Location"
msgstr "Emplacement Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_store_identifier
msgid "Urban Piper POS ID"
msgstr "ID PdV Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_payment_methods_ids
msgid "Urban Piper Payment Methods"
msgstr "Modes de paiement Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_pricelist_id
msgid "Urban Piper Pricelist"
msgstr "Liste de prix Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_webhook_url
msgid "Urban Piper Webhook URL"
msgstr "Url Webhook Urban Piper"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_urban_piper_status
msgid "Urban piper product status"
msgstr "Statut du produit Urban Piper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__urbanpiper_apikey
msgid "UrbanPiper API Key"
msgstr "Clé API UrbanPiper"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper API Key is required.\n"
msgstr "Clé API UrbanPiper requise.\n"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid "UrbanPiper Delivery Providers are required."
msgstr "Les prestataires de livraison UrbanPiper sont requis."

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_fiscal_position_id
msgid "UrbanPiper Fiscal Position"
msgstr "Position fiscale UrbanPiper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_pricelist_id
msgid "UrbanPiper Pricelist"
msgstr "Liste de prix UrbanPiper"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper Store ID is required.\n"
msgstr "L'ID du magasin UrbanPiper est requis.\n"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_store_identifier
msgid "UrbanPiper Store Identifier"
msgstr "Identifiant du magasin UrbanPiper"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper Username is required.\n"
msgstr "Le nom d'utilisateur UrbanPiper est requis.\n"

#. module: pos_urban_piper
#: model:account.fiscal.position,name:pos_urban_piper.pos_account_fiscal_position_urbanpiper
msgid "Urbanpiper"
msgstr "Urbanpiper"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__urbanpiper_username
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Username"
msgstr "Nom d'utilisateur"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Variants/Addons out of Stock"
msgstr "Variantes/dépendances en rupture de stock"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__1
msgid "Vegetarian"
msgstr "Végétarien"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/ir_config_parameter.py:0
msgid "You cannot change the pos_urban_piper.uuid."
msgstr "Vous ne pouvez pas modifier pos_urban_piper.uuid."

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid ""
"Your company %s needs to have a correct city in order create the store."
msgstr ""
