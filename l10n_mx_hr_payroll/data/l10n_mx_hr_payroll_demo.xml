<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.demo_company_mx" model="res.company" forcecreate="1">
        <field name="name">My Mexican Company</field>
        <field name="currency_id" ref="base.MXN"/>
        <field name="street">Del Nino Artillero 27</field>
        <field name="zip">62520</field>
        <field name="city">Tepoztlan</field>
        <field name="country_id" ref="base.mx"/>
    </record>

    <record id="base.user_admin" model="res.users">
        <field name="company_ids" eval="[(4, ref('base.demo_company_mx'))]"/>
    </record>

    <record id="base.user_demo" model="res.users">
        <field name="company_ids" eval="[(4, ref('base.demo_company_mx'))]"/>
    </record>

    <record id="hr_department_rdlt" model="hr.department">
        <field name="name">RD MX</field>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="job_developer_lithuania" model="hr.job">
        <field name="name">Experienced Developer MX</field>
        <field name="department_id" ref="hr_department_rdlt"/>
        <field name="no_of_recruitment">5</field>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="hr_employee_roque" model="hr.employee">
        <field name="name">Roque Yepes</field>
        <field name="job_id" ref="job_developer_lithuania"/>
        <field name="country_id" ref="base.mx"/>
        <field name="company_id" ref="base.demo_company_mx"/>
        <field name="image_1920" type="base64" file="l10n_mx_hr_payroll/static/img/hr_employee_roque.jpg"/>
        <field name="gender">male</field>
    </record>

    <record id="res_partner_elvera" model="res.partner">
        <field name="name">Elvera Ramirez</field>
        <field name="street">Zona Hotelera</field>
        <field name="city">Cancun</field>
        <field name="zip">77500</field>
        <field name="country_id" ref="base.mx"/>
        <field name="phone">+*************</field>
        <field name="email"><EMAIL></field>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="user_elvera" model="res.users">
        <field name="partner_id" ref="l10n_mx_hr_payroll.res_partner_elvera"/>
        <field name="login"><EMAIL></field>
        <field name="password">elveraramirez</field>
        <field name="signature" type="html"><span>--<br/>+E. Ramirez</span></field>
        <field name="company_ids" eval="[(4, ref('base.demo_company_mx'))]"/>
        <field name="company_id" ref="base.demo_company_mx"/>
        <field name="groups_id" eval="[(6,0,[ref('base.group_user')])]"/>
        <field name="image_1920" type="base64" file="l10n_mx_hr_payroll/static/img/hr_employe_elvera.jpg"/>
    </record>

    <record id="res_partner_elvera_work_address" model="res.partner">
        <field name="name">MX Offices</field>
        <field name="street">Paseo Ensenada 901</field>
        <field name="city">Tijuana</field>
        <field name="zip">22500</field>
        <field name="country_id" ref="base.mx"/>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="res_partner_elvera_private_address" model="res.partner">
        <field name="name">Elvera Ramirez</field>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="res_partner_bank_account_elvera" model="res.partner.bank">
        <field name="acc_number">********************</field>
        <field name="bank_id" ref="base.bank_ing"/>
        <field name="partner_id" ref="l10n_mx_hr_payroll.res_partner_elvera_private_address"/>
        <field name="company_id" ref="base.demo_company_mx"/>
    </record>

    <record id="hr_employee_elvera" model="hr.employee">
        <field name="name">Elvera Ramirez (era)</field>
        <field name="gender">female</field>
        <field name="marital">single</field>
        <field name="job_title">Software Developer</field>
        <field name="private_street">Francisco I. Madero Poniente 1300</field>
        <field name="private_city">Morelia</field>
        <field name="private_zip">58010</field>
        <field name="private_country_id" ref="base.mx"/>
        <field name="private_phone">+*************</field>
        <field name="private_email"><EMAIL></field>
        <field name="address_id" ref="l10n_mx_hr_payroll.res_partner_elvera_work_address"/>
        <field name="emergency_contact">Norberto Ramirez</field>
        <field name="emergency_phone">+*************</field>
        <field name="birthday">1991-07-28</field>
        <field name="distance_home_work">25</field>
        <field name="place_of_birth">Mexico</field>
        <field name="country_of_birth" ref="base.mx"/>
        <field name="certificate">master</field>
        <field name="study_field">Civil Engineering</field>
        <field name="study_school">Université Catholique de Louvain-la-Neuve</field>
        <field name="parent_id" ref="l10n_mx_hr_payroll.hr_employee_roque"/>
        <field name="country_id" ref="base.mx"/>
        <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>
        <field name="identification_id">*************</field>
        <field name="bank_account_id" ref="l10n_mx_hr_payroll.res_partner_bank_account_elvera"/>
        <field name="image_1920" type="base64" file="l10n_mx_hr_payroll/static/img/hr_employe_elvera.jpg"/>
        <field name="company_id" ref="base.demo_company_mx"/>
        <field name="user_id" ref="l10n_mx_hr_payroll.user_elvera"/>
    </record>

    <record id="hr_contract_cdi_elvera_previous" model="hr.contract">
        <field name="name">CDI - Elvera Ramirez - Experienced Developer</field>
        <field name="department_id" ref="hr_department_rdlt"/>
        <field name="employee_id" ref="hr_employee_elvera"/>
        <field name="job_id" ref="l10n_mx_hr_payroll.job_developer_lithuania"/>
        <field name="structure_type_id" ref="l10n_mx_hr_payroll.structure_type_employee_mx"/>
        <field name="wage">23000</field>
        <field name="state">close</field>
        <field name="hr_responsible_id" ref="base.user_demo"/>
        <field name="company_id" ref="base.demo_company_mx"/>
        <field name="date_start" eval="(DateTime.today() + relativedelta(years=-2, month=1, day=1))"/>
        <field name="date_end" eval="(DateTime.today() + relativedelta(years=-1, month=1, day=1, days=-2))"/>
        <field name="resource_calendar_id" model="resource.calendar" eval="obj().search([('company_id', '=', obj().env.ref('base.demo_company_mx').id)], limit=1)"/>
        <field name="l10n_mx_risk_bonus_rate">0.5</field>
        <field name="l10n_mx_christmas_bonus">15</field>
        <field name="l10n_mx_holidays_count">12</field>
        <field name="l10n_mx_holiday_bonus_rate">25</field>
    </record>

    <record id="hr_contract_cdi_elvera" model="hr.contract">
        <field name="name">CDI - Elvera Ramirez - Experienced Developer</field>
        <field name="department_id" ref="hr_department_rdlt"/>
        <field name="employee_id" ref="hr_employee_elvera"/>
        <field name="job_id" ref="l10n_mx_hr_payroll.job_developer_lithuania"/>
        <field name="structure_type_id" ref="l10n_mx_hr_payroll.structure_type_employee_mx"/>
        <field name="wage">25000</field>
        <field name="state">open</field>
        <field name="hr_responsible_id" ref="base.user_demo"/>
        <field name="company_id" ref="base.demo_company_mx"/>
        <field name="date_start" eval="(DateTime.today() + relativedelta(years=-1, month=1, day=1, days=-1))"/>
        <field name="resource_calendar_id" model="resource.calendar" eval="obj().search([('company_id', '=', obj().env.ref('base.demo_company_mx').id)], limit=1)"/>
        <field name="l10n_mx_risk_bonus_rate">0.5</field>
        <field name="l10n_mx_christmas_bonus">15</field>
        <field name="l10n_mx_holidays_count">12</field>
        <field name="l10n_mx_holiday_bonus_rate">25</field>
    </record>

    <record id="hr_employee_elvera" model="hr.employee">
        <field name="contract_id" ref="l10n_mx_hr_payroll.hr_contract_cdi_elvera"/>
    </record>
</odoo>
