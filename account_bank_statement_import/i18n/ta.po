# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2016-02-05 10:18+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "%d transactions had already been imported and were ignored."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "1 transaction had already been imported and was ignored."
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once!"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Cannot find in which journal import this statement. Please manually select a journal."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file?"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Go to Apps"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "If it contains transactions for more than one account, it must be imported on each of them."
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr ""

#. module: account_bank_statement_import
#. odoo-javascript
#: code:addons/account_bank_statement_import/static/src/account_bank_statement_import_model.js:0
msgid "Import Template for Bank Statements"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Manual (or import %(import_formats)s)"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No attachment was provided"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No currency found matching '%s'."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "The account of this statement (%s) is not the same as the journal (%s)."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "The currency of the bank statement (%s) is not the same as the currency of the journal (%s)."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "The following files could not be imported:\n"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "This file doesn't contain any statement for account %s."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "This file doesn't contain any transaction for account %s."
msgstr ""

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Transactions"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "View successfully imported statements"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You already have imported that file."
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You have to set a Default Account for the journal: %s"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You uploaded an invalid or empty file."
msgstr ""

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "or"
msgstr ""
