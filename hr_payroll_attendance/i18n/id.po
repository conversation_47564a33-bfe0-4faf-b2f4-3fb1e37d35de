# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_attendance
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_attendance
#: model:ir.model.fields,field_description:hr_payroll_attendance.field_hr_payslip__attendance_count
msgid "Attendance Count"
msgstr "Jumlah <PERSON>i"

#. module: hr_payroll_attendance
#. odoo-python
#: code:addons/hr_payroll_attendance/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll_attendance.view_hr_payslip_form_inherit_payroll_attendance
msgid "Attendances"
msgstr "Absensi"

#. module: hr_payroll_attendance
#: model_terms:ir.ui.view,arch_db:hr_payroll_attendance.hr_attendance_view_inherit_filter
msgid "Employee Code"
msgstr "Kode Karyawan"

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "Kontrak Karyawan"

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_payslip
msgid "Pay Slip"
msgstr "Slip Gaji"

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr "Jumlah Hari Kerja Slip Gaji"
