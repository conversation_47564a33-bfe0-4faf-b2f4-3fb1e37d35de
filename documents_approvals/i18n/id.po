# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_approvals
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_approval_request
msgid "Approval Request"
msgstr "Permintaan Persetujuan"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__documents_approvals_settings
msgid "Approvals"
msgstr "Persetujuan"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_tag_ids
msgid "Approvals Tag"
msgstr "Tag Approval"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_tag_ids
msgid "Approvals Tags"
msgstr "Tag-Tag Approval"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_folder_id
msgid "Approvals Workspace"
msgstr "Ruang Kerja Approval"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_folder_id
msgid "Approvals default workspace"
msgstr "Ruang kerja default approval"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Centralize files attached to Approvals"
msgstr "Pusatkan file yang terlampir ke Approval"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_company
msgid "Companies"
msgstr "Perusahaan-Perusahaan"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfig"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Default Tags"
msgstr "Tag Default"

#. module: documents_approvals
#. odoo-python
#: code:addons/documents_approvals/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:documents_approvals.approval_request_view_form
msgid "Documents"
msgstr "Dokumen"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_enabled
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__documents_approvals_settings
msgid "Documents Approvals Settings"
msgstr "Pengaturan Approval Dokumen"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_count
msgid "Documents Count"
msgstr "Jumlah Dokumen"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Workspace"
msgstr "Ruang Kerja"
