<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.sale.timesheet</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='timesheet_ids']/list" position="inside">
                <field name="timesheet_invoice_id" column_invisible="True"/>
                <field name="is_so_line_edited" column_invisible="True"/>
                <field name="helpdesk_ticket_id" column_invisible="True"/>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/list/field[@name='unit_amount']" position="before">
                <field name="so_line"
                    groups="!sales_team.group_sale_salesman"
                    column_invisible="not parent.use_helpdesk_sale_timesheet or not parent.partner_id"
                    readonly="readonly_timesheet"
                    domain="[('is_service', '=', True), ('order_partner_id', 'child_of', parent.commercial_partner_id), ('is_expense', '=', False), ('state', '=', 'sale'), ('is_downpayment', '=', False)]"
                    optional="show" options="{'no_create': True, 'no_open': True}"/>
                <field name="so_line"
                    groups="sales_team.group_sale_salesman"
                    column_invisible="not parent.use_helpdesk_sale_timesheet or not parent.partner_id"
                    readonly="readonly_timesheet"
                    domain="[('is_service', '=', True), ('order_partner_id', 'child_of', parent.commercial_partner_id), ('is_expense', '=', False), ('state', '=', 'sale'), ('is_downpayment', '=', False)]"
                    context="{'with_remaining_hours': True, 'with_price_unit': True}"
                    optional="hide" options="{'no_create': True, 'no_open': True}"/>
            </xpath>
            <xpath expr="//field[@name='project_id']" position="before">
                <field name="use_helpdesk_sale_timesheet" invisible="1"/>
                <field name="display_invoice_button" invisible="1"/>
            </xpath>
            <xpath expr="//button[@name='action_open_helpdesk_ticket']" position="before">
                <button class="oe_stat_button"
                        type="object" name="action_view_so" icon="fa-dollar"
                        invisible="not use_helpdesk_sale_timesheet or not sale_order_id and not sale_line_id"
                        string="Sales Order"
                        groups="sales_team.group_sale_salesman"/>
                <button class="oe_stat_button"
                        type="object" name="action_view_invoices" icon="fa-pencil-square-o"
                        invisible="not display_invoice_button"
                        string="Invoices"
                        groups="account.group_account_readonly,account.group_account_invoice,account.group_account_manager,sales_team.group_sale_salesman_all_leads">
                    <field name="invoice_count" widget="statinfo" string="Invoices"/>
                </button>
            </xpath>
            <xpath expr="//field[@name='project_id']" position="after">
                <field name="sale_order_state" invisible="1"/>
                <label for="sale_line_id" invisible="not use_helpdesk_sale_timesheet or not partner_id"/>
                <div class="o_row" invisible="not use_helpdesk_sale_timesheet or not partner_id">
                    <field name="sale_line_id" groups="!sales_team.group_sale_salesman" placeholder="Non-billable" options="{'no_open': True}" readonly="True"/>
                    <field name="sale_line_id" groups="sales_team.group_sale_salesman" placeholder="Non-billable"
                        context="{
                            'create': False, 'edit': False,
                            'with_remaining_hours': True,
                            'with_price_unit': True,
                            'form_view_ref': 'sale_project.sale_order_line_view_form_editable',
                            'default_partner_id': partner_id,
                            'default_company_id': company_id,
                            'is_downpayment': False,
                        }"
                    />
                    <span
                        class="fa fa-exclamation-triangle text-warning"
                        title="The sales order associated with this ticket has been cancelled. We recommend either updating the sales order item or cancelling this ticket in alignment with the cancellation of the sales order."
                        invisible="sale_order_state != 'cancel'"
                    />
                </div>
            </xpath>
            <xpath expr="//field[@name='total_hours_spent']" position="after">
                <t groups="sales_team.group_sale_salesman">
                    <field name="remaining_hours_available" invisible="1"/>
                    <span id="remaining_hours_so_label" invisible="not sale_order_id or not use_helpdesk_sale_timesheet or not partner_id or not sale_line_id or not remaining_hours_available">
                        <label class="fw-bold" for="remaining_hours_so"
                            invisible="remaining_hours_so &lt; 0"/>
                        <label class="fw-bold text-danger" for="remaining_hours_so"
                            invisible="remaining_hours_so &gt;= 0"/>
                    </span>
                    <field name="remaining_hours_so" nolabel="1" widget="timesheet_uom" invisible="not sale_order_id or not use_helpdesk_sale_timesheet or not partner_id or not sale_line_id or not remaining_hours_available" decoration-danger="remaining_hours_so &lt; 0"/>
                </t>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_tree_inherit_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.list.inherit.sale.timesheet</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">90</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_tree"/>
        <field name="arch" type="xml">
            <field name='partner_id' position="after">
                <field name="sale_line_id" readonly="1" optional="hide" groups="helpdesk_sale.group_use_helpdesk_sale_timesheet"/>
            </field>
        </field>
    </record>

</odoo>
