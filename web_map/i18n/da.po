# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_map
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"
msgstr ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                Sign up to MapBox to get a free token"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Visning af handlingsvindue"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#: model_terms:ir.ui.view,arch_db:web_map.view_res_partner_filter_inherit_map
msgid "Address"
msgstr "Adresse"

#. module: web_map
#: model:ir.model,name:web_map.model_base
msgid "Base"
msgstr "Basis"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Close"
msgstr "Luk"

#. module: web_map
#: model:ir.model,name:web_map.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: web_map
#: model:ir.model,name:web_map.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_partner__contact_address_complete
#: model:ir.model.fields,field_description:web_map.field_res_users__contact_address_complete
msgid "Contact Address Complete"
msgstr "Kontakt adresse fuldført"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
msgid "Items"
msgstr "Posteringer"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Locating new addresses..."
msgstr "Finder nye adresser..."

#. module: web_map
#: model:ir.model.fields.selection,name:web_map.selection__ir_actions_act_window_view__view_mode__map
#: model:ir.model.fields.selection,name:web_map.selection__ir_ui_view__type__map
msgid "Map"
msgstr "Kort"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Map Routes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "MapBox servers unreachable"
msgstr "MapBox servere kan ikke nås"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
msgid "Name"
msgstr "Navn"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Navigate to"
msgstr ""

#. module: web_map
#: model:ir.model.fields,help:web_map.field_res_config_settings__map_box_token
msgid "Necessary for some functionalities in the map view"
msgstr "Nødvendig for visse funktionaliteter i kort visning"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "No"
msgstr "Nej"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "None"
msgstr "Ingen"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Open"
msgstr "Åben"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "OpenStreetMap's request limit exceeded, try again later."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Set a MapBox account to activate routes and style"
msgstr "Angiv en MapBox konto for at aktivere ruter og stil"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Set up token"
msgstr "Opsæt token"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Some routing points are too far apart"
msgstr "Visse rute punkter er for langt fra hinanden"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "The MapBox server is unreachable"
msgstr "MapBox serveren kan ikke nås"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "The token input is not valid"
msgstr "Token input er ugyldigt"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid ""
"The view has switched to another provider but functionalities will be "
"limited"
msgstr ""
"Visningen har skiftet til en anden udbyder, men funktionaliteten vil være "
"begrænset"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "This referer is not authorized"
msgstr "Denne reference er ikke autoriseret"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid ""
"To get routing on your map, you first need to set up your MapBox token. It's"
" free."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Token"
msgstr "Token"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_config_settings__map_box_token
msgid "Token Map Box"
msgstr "Token Map Boks"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Token invalid"
msgstr "Token ugyldig"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Too many requests, try again in a few minutes"
msgstr "For mange anmodninger, prøv venligst igen om et par minutter"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Too many routing points (maximum 25)"
msgstr "For mange rute punkter (maksimalt 25)"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Unauthorized connection"
msgstr "Uautoriseret forbindelse"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Undefined"
msgstr "Udefineret"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Unsuccessful routing request:"
msgstr "Mislykket rute anmodning:"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_controller.js:0
msgid "Untitled"
msgstr "Uden titel"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_ui_view
msgid "View"
msgstr "Vis"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_map.field_ir_ui_view__type
msgid "View Type"
msgstr "Se type"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "View in Google Maps"
msgstr "Vis i Google Maps"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Yes"
msgstr "Ja"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/models.py:0
msgid "You need to set a Contact field on this model to use the Map View"
msgstr ""
"Du skal angive et Kontakt felt på denne model for at bruge Kort Visningen"
