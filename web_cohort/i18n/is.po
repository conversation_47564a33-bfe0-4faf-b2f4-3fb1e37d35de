# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_cohort
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"Language: is\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
msgid "%s - By %s"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "- By"
msgstr ""

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web_cohort
#. odoo-python
#. odoo-javascript
#: code:addons/web_cohort/controllers/main.py:0
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Average"
msgstr ""

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_base
msgid "Base"
msgstr "Stofnupphæð"

#. module: web_cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_actions_act_window_view__view_mode__cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_ui_view__type__cohort
msgid "Cohort"
msgstr ""

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
msgid "Cohort %(title)s (%(model_name)s)"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid "Cohort view has not defined \"date_start\" attribute."
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid "Cohort view has not defined \"date_stop\" attribute."
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Day"
msgstr "Dagur"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Download as Excel file"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Main actions"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Month"
msgstr "Mánuður"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "No data available."
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.js:0
msgid ""
"Period: %(period)s\n"
"%(measure)s: %(count)s"
msgstr ""

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_ui_view
msgid "View"
msgstr "View"

#. module: web_cohort
#: model:ir.model.fields,field_description:web_cohort.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_cohort.field_ir_ui_view__type
msgid "View Type"
msgstr "View Type"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Week"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Year"
msgstr "Ár"
