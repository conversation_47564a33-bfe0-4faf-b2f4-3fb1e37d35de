# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_card
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-03 08:35+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "%(card_campaign_name)s Mailings"
msgstr "%(card_campaign_name)s 邮件发送"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"Clicks\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"点击\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-share\" aria-label=\"Shares\" role=\"img\" title=\"Shares\"/>"
msgstr "<i class=\"fa fa-fw fa-share\" aria-label=\"分享\" role=\"img\" title=\"分享\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "<small>Where does this link to?</small>"
msgstr "<small>这个链接到哪里？</small>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Cards\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                海报\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Clicks\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                点击次数\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Mailings\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                邮件发送\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Opened\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                已开启\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Shared\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                已分享\n"
"                            </span>"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction
msgid "Action Needed"
msgstr "所需操作"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__active
#: model:ir.model.fields,field_description:marketing_card.field_card_card__active
msgid "Active"
msgstr "有效"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_ids
msgid "Activities"
msgstr "活动"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Archived"
msgstr "已归档"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Are you sure you want to update all cards of the campaign?"
msgstr "你确定要更新活动中的所有海报吗？"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_background
msgid "Background"
msgstr "背景"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__body_html
#: model:ir.model.fields,field_description:marketing_card.field_card_template__body
msgid "Body"
msgstr "主体"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_button
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
msgid "Button"
msgstr "按钮"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Campaign"
msgstr "活动"

#. module: marketing_card
#: model:ir.ui.menu,name:marketing_card.card_campaign_menu
msgid "Campaigns"
msgstr "活动"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.cards_card_action
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_ids
msgid "Card"
msgstr "海报"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_campaign_action
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_campaign_id
msgid "Card Campaign"
msgstr "海报活动"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid "Card Campaign Mailing should target model %(model_name)s"
msgstr "模型 %(model_name)s的营销推广卡"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_click_count
msgid "Card Click Count"
msgstr "海报点击次数"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_count
msgid "Card Count"
msgstr "海报数目"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Card Layout"
msgstr "卡片布局"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_requires_sync_count
msgid "Card Requires Sync Count"
msgstr "需要同步海报数目"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_share_count
msgid "Card Share Count"
msgstr "海报分享次数"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_template_action
#: model:ir.ui.menu,name:marketing_card.cards_template_menu
msgid "Card Template"
msgstr "海报模板"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Cards"
msgstr "已发送"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Click here for your reward!"
msgstr "点击此处领取奖励！"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__color
msgid "Color"
msgstr "颜色"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.cards_card_action
msgid "Create a Card Campaign to send cards to your partners"
msgstr "创建海报活动，向合作伙伴发送海报"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid "Create a Sharing Campaign!"
msgstr "创建分享活动！"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_template_action
msgid "Create a design to use in Card Campaigns"
msgstr "创建一个用于营销活动海报的设计"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_uid
msgid "Created by"
msgstr "创建人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_date
msgid "Created on"
msgstr "创建日期"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__default_background
msgid "Default Background"
msgstr "默认背景"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__post_suggestion
msgid "Description below the card and default text when sharing on X"
msgstr "海报下方的描述，以及分享到 X 上时的默认文本"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_template_id
msgid "Design"
msgstr "设计"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_card__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Dynamic Field?"
msgstr "动态字段？"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image1_path
msgid "Dynamic Image 1"
msgstr "动态图片 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image2_path
msgid "Dynamic Image 2"
msgstr "动态图片 2"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_card_campaign_record_unique
msgid "Each record should be unique for a campaign"
msgstr "活动中的每条记录都应是唯一的"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mail_compose_message
msgid "Email composition wizard"
msgstr "EMail撰写向导"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Filter By"
msgstr "筛选方式"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Group By"
msgstr "分组方式"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__has_message
msgid "Has Message"
msgstr "有消息"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header
msgid "Header"
msgstr "标题"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_color
msgid "Header Color"
msgstr "标题颜色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_path
msgid "Header Path"
msgstr "标题路径"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Help us share the news"
msgstr "帮助我们分享新闻"

#. module: marketing_card
#: model:ir.module.category,description:marketing_card.module_category_marketing_card
msgid "Helps you manage marketing card campaigns."
msgstr "帮助您管理营销卡活动。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__id
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__id
#: model:ir.model.fields,field_description:marketing_card.field_card_card__id
#: model:ir.model.fields,field_description:marketing_card.field_card_template__id
msgid "ID"
msgstr "ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__image
msgid "Image"
msgstr "图像"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__image_preview
msgid "Image Preview"
msgstr "图片预览"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_dyn
msgid "Is Dynamic Header"
msgstr "是动态标题"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_dyn
msgid "Is Dynamic Section"
msgstr "是动态章节"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_dyn
msgid "Is Dynamic Sub-Header"
msgstr "是动态副标题"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_dyn
msgid "Is Dynamic Sub-Section 1"
msgstr "是动态小分节1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_dyn
msgid "Is Dynamic Sub-Section 2"
msgstr "是动态小分节2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Join me at this event!"
msgstr "跟我一起参加这个活动吧！"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__lang
msgid "Language"
msgstr "语言"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__link_tracker_id
msgid "Link Tracker"
msgstr "跟踪链"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_ids
msgid "Mailing"
msgstr "邮件"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_count
msgid "Mailing Count"
msgstr "推广邮件计数"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_card
#: model:ir.module.category,name:marketing_card.module_category_marketing_card
#: model:ir.ui.menu,name:marketing_card.card_menu
#: model:ir.ui.menu,name:marketing_card.marketing_card_menu_technical
msgid "Marketing Card"
msgstr "营销海报"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign
msgid "Marketing Card Campaign"
msgstr "营销活动海报"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign_tag
msgid "Marketing Card Campaign Tag"
msgstr "营销活动海报标签"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_manager
msgid "Marketing Card Manager"
msgstr "海报管理人"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_template
msgid "Marketing Card Template"
msgstr "海报模板"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_user
msgid "Marketing Card User"
msgstr "海报用户"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mailing_mailing
msgid "Mass Mailing"
msgstr "群发邮件"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_ids
msgid "Messages"
msgstr "消息"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__res_model
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_model
msgid "Model Name"
msgstr "模型名称"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid ""
"Model of campaign %(campaign)s may not be changed as it already has cards"
msgstr "活动模型 %(campaign)s 可能无法更改，因为它已经有生成海报了"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "My Campaigns"
msgstr "我的活动"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__name
msgid "Name"
msgstr "名称"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一活动日历事件"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "No button"
msgstr "为空则不显示按钮"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url_click_count
msgid "Number of Clicks"
msgstr "点击数"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Odoo"
msgstr "Odoo"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"发送邮件时可选择的翻译代码（ISO 代码）。如果未设置，将使用英文版本。一般使用占位符确定适当语言，例如：{{ "
"object.partner_id.lang }}"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url
msgid "Post Link"
msgstr "文章链接"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__post_suggestion
msgid "Post Suggestion"
msgstr "发帖建议"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Powered By"
msgstr "技术支持"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid ""
"Prepare a design and some content and let your community spread the word!"
msgstr "准备一些设计和内容，让你的社区来传播消息！"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview"
msgstr "预览"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__preview_record_ref
msgid "Preview On"
msgstr "预览"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview on..."
msgstr "预览"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_color
msgid "Primary Color"
msgstr "主要颜色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_text_color
msgid "Primary Text Color"
msgstr "主文字颜色"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "Profile Picture"
msgstr "主题图片"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__rating_ids
msgid "Ratings"
msgstr "点评"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipient Message"
msgstr "收件人信息"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipients"
msgstr "收件人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "收件人模型"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_id
msgid "Record ID"
msgstr "记录ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__render_model
msgid "Rendering Model"
msgstr "渲染模型"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_title
msgid "Request"
msgstr "请求"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_description
msgid "Request Description"
msgstr "请求描述"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__requires_sync
msgid "Requires Sync"
msgstr "需要同步"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__user_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Responsible"
msgstr "负责人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_target_url
msgid "Reward Link"
msgstr "奖励链接"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Search Card"
msgstr "搜索海报"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Search Share Campaign"
msgstr "搜索分享推广活动"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_color
msgid "Secondary Color"
msgstr "次要颜色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_text_color
msgid "Secondary Text Color"
msgstr "次要文字颜色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section
msgid "Section"
msgstr "小节"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_path
msgid "Section Path"
msgstr "章节路径"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Select a field"
msgstr "选择一个字段"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Select where to share"
msgstr "选择分享位置"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Send"
msgstr "发送"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Send Cards"
msgstr "发送海报"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Share Campaign"
msgstr "分享推广活动"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_list
msgid "Share Card"
msgstr "分享海报"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__share_status
msgid "Share Status"
msgstr "分享状态"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_template_view_form
msgid "Share Template"
msgstr "分享模板"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Share with your community!"
msgstr "与您的社区分享！"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__shared
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Shared"
msgstr "分享"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_color
msgid "Sub Header Color"
msgstr "副标题颜色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header
msgid "Sub-Header"
msgstr "副标题"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_path
msgid "Sub-Header Path"
msgstr "副标题路径"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1
msgid "Sub-Section 1"
msgstr "小分节 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_path
msgid "Sub-Section 1 Path"
msgstr "小分节 1 路径"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2
msgid "Sub-Section 2"
msgstr "小分节 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_path
msgid "Sub-Section 2 Path"
msgstr "小分节 2 路径"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Tags"
msgstr "标签"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_campaign_tag_name_uniq
msgid "Tags may not reuse existing names."
msgstr "标签名称不可以重复。"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Thanks for sharing, here is your reward!"
msgstr "感谢分享，这是你的奖励！"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_message
msgid "Thanks to You Message"
msgstr "感谢语"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/utm_source.py:0
msgid ""
"The UTM source '%s' cannot be deleted as it is used to promote marketing "
"cards campaigns."
msgstr "不能删除 UTM 来源 “%s”，因为它用于推广营销卡活动。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_utm_source
msgid "UTM Source"
msgstr "UTM来源"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update"
msgstr "更新"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.robots
msgid ""
"User-agent: *\n"
"                Allow: /cards/"
msgstr ""
"User-agent: *\n"
"                Allow: /cards/"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__visited
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Visited"
msgstr "已到访"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_card__requires_sync
msgid "Whether the image needs to be updated to match the campaign template."
msgstr "是否需要更新图片以匹配活动模板。"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid ""
"You should update all the cards for %(mailing)s before scheduling a mailing."
msgstr "你应该在安排邮寄之前更新 %(mailing)s 的所有海报。"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Your Home Page"
msgstr "您的网站主页"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "button text"
msgstr "按钮文本"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"https://www.mycompany.com/reward\""
msgstr "例如：“https://www.mycompany.com/reward”"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Aug 24, Brussels Expo"
msgstr "例如： 8 月 24 日，布鲁塞尔展览中心"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. By Lionel Messy"
msgstr "例如：由莱昂内尔·梅西"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. CFO Chief Football Officer"
msgstr "例如：财务总监"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Join Odoo Experience 2024"
msgstr "例如：参加2024年Odoo体验活动"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Odoo Experience Talks"
msgstr "例如：Odoo 体验展演讲"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Sample Talk"
msgstr "例如：对话样本"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Why people should share on their network?"
msgstr "例如：为什么人们应该在他们的社交圈中分享？"
