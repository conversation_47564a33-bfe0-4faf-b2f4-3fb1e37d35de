# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON><PERSON> Thaidev <odo<PERSON><PERSON><EMAIL>>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:23+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                เพิ่มที่อยู่ลงในรายการที่อนุญาต\n"
"            </p><p>\n"
"                เพื่อปกป้องคุณจากสแปมและการตอบกลับ Odoo จะบล็อกอีเมล\n"
"                ที่มาถึงเกตเวย์ของคุณโดยอัตโนมัติเกินเกณฑ์อีเมลของ<b>%(threshold)i</b> ทุกๆ <b>%(minutes)i</b> นาที\n"
"                หากมีที่อยู่บางแห่งที่คุณต้องการรับการอัปเดตบ่อยครั้ง\n"
"                คุณสามารถเพิ่มที่อยู่ด้านล่างได้ แล้ว Odoo จะปล่อยให้ดำเนินการต่อไป\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" ไม่ได้ติดตามอีกต่อไป"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "\"%(activity_name)s: %(summary)s\" ถูกมอบหมายให้กับคุณ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\" จำเป็นต้องเข้าถึงไมโครโฟนของคุณ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\" ต้องมีการเข้าถึงไมโครโฟน"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s ถูกมอบหมายให้กับ %(name)s ครบกำหนดเมื่อ %(deadline)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s จาก %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s ไม่ได้รับการยอมรับว่าเป็นอีเมลที่ถูกต้อง "
"ซึ่งมีความจำเป็นสำหรับการสร้างลูกค้าใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person)s"
msgstr "%(emoji)s ตอบโต้โดย %(person)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s and %(person2)s"
msgstr "%(emoji)s ตอบโต้โดย %(person1)s และ %(person2)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s "
"others"
msgstr ""
"%(emoji)s ตอบโต้โดย %(person1)s, %(person2)s, %(person3)s, และ %(count)s "
"คนอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other"
msgstr ""
"%(emoji)s ตอบโต้โดย %(person1)s, %(person2)s, %(person3)s, และอีก 1 คน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s"
msgstr "%(emoji)s ตอบโต้โดย %(person1)s, %(person2)s, และ %(person3)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "%(model_name)s.%(field_path)s does not seem to be a valid field path"
msgstr ""
"%(model_name)s.%(field_path)s ดูเหมือนจะไม่ใช่เส้นทางของฟิลด์ที่ถูกต้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""
"%(open_button)s%(icon)s%(open_em)s ยกเลิกการแก้ไข "
"%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sเพื่อ "
"%(open_cancel)sยกเลิก%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sเพื่อ "
"%(open_save)sบันทึก%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sเพื่อ "
"%(open_cancel)sยกเลิก%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sเพื่อ "
"%(open_save)sบันทึก%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.js:0
msgid "%(recipientCount)s more"
msgstr "เหลือ %(recipientCount)s "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr "%(user)s เชื่อมต่อแล้ว ซึ่งเป็นการเชื่อมต่อครั้งแรกของพวกเขา"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(user)s started a thread: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sSee all threads%(goto_all_end)s."
msgstr ""
"%(user)s เริ่มเธรด: %(goto)s%(thread_name)s%(goto_end)s %(goto_all)s "
"ดูกระทู้ทั้งหมด %(goto_all_end)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s and %(user2)s are typing..."
msgstr "%(user1)s และ %(user2)s กำลังพิมพ์..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s, %(user2)s and more are typing..."
msgstr "%(user1)s, %(user2)s และคนอื่นๆ กำลังพิมพ์..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s ได้ปักหมุดข้อความไว้ที่ช่องนี้แล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
msgid "%s (Email Template)"
msgstr "%s (เทมเพลตอีเมล)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan.py:0
#: code:addons/mail/models/mail_template.py:0
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "%s created"
msgstr "สร้าง %s แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "%s days overdue"
msgstr "เกินกำหนด %s วัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%s is typing..."
msgstr "%s กำลังพิมพ์ ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_result.js:0
msgid "%s messages found"
msgstr "%s ข้อความที่พบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "%s new messages"
msgstr "%s ข้อความใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s raised their hand"
msgstr "%s ยกมือขึ้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "%s started a live conference"
msgstr "%s เริ่มการประชุมไลฟ์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"camera\" access"
msgstr "%s\" ต้องการการเข้าถึง \"กล้อง\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" ต้องการการเข้าถึง \"การบันทึกหน้าจอ\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_attachment_links
msgid "&amp;#128229;"
msgstr "&amp;#128229;"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translated from: %(language)s)"
msgstr "(แปลจาก: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translation Failure: %(error)s)"
msgstr "(การแปลล้มเหลว: %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "(edited)"
msgstr "(แก้ไข)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "(from"
msgstr "(จาก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(เดิมกำหนดให้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid ") added you as a follower of this"
msgstr ") เพิ่มคุณเป็นผู้ติดตามสิ่งนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid ". Narrow your search to see more choices."
msgstr "จำกัดการค้นหาของคุณเพื่อดูตัวเลือกเพิ่มเติม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "1 new message"
msgstr "1 ข้อความใหม่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""
"<b invisible=\"not no_record\" class=\"text-"
"warning\">ไม่มีบันทึกสำหรับรุ่นนี้</b>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid ""
"<button>Change your preferences</button> to receive new notifications in "
"your inbox."
msgstr ""
"<button>เปลี่ยนการตั้งค่าของคุณ</button>เพื่อรับการแจ้งเตือนใหม่ในกล่องจดหมายของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-check\"/> Done"
msgstr "<i class=\"fa fa-check\"/> เสร็จสิ้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_kanban
msgid ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"
msgstr ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"นับก้าว\" "
"title=\"นับก้าว\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"URL ของเอกสาร\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr "<i class=\"fa fa-times\"/> ยกเลิก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a channel here.</p>"
msgstr "<p>Create a channel here.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Create a public or private channel.</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">สีของปุ่ม</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">สีของหัวเรื่อง</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">เปิดเอกสาร</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">เปิดเอกสารหลัก</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                ข้อความจะถูกส่งเป็นอีเมลไปยังผู้รับเทมเพลต\n"
"                                และจะไม่ปรากฏในประวัติการส่งข้อความ\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                ข้อความจะถูกโพสต์เป็นบันทึกภายในที่\n"
"                                ผู้ใช้ภายในมองเห็นได้ในประวัติการส่งข้อความ\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                ข้อความจะถูกโพสต์เป็นข้อความในบันทึก\n"
"                                ซึ่งแจ้งเตือนผู้ติดตามทุกท่านและจะปรากฏในประวัติการส่งข้อความ\n"
"                            </span>"

#. module: mail
#: model_terms:web_tour.tour,rainbow_man_message:mail.discuss_channel_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>ทำได้ดีมาก!</b> คุณได้ผ่านทุกขั้นตอนของการทัวร์นี้แล้ว</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>หากคุณทำสิ่งนี้:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>หากคุณไม่ได้ทำสิ่งนี้:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>เปิดบันทึก</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>เราขอแนะนำให้คุณเริ่มต้นด้วย</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong> บันทึก </strong> หน้านี้และกลับมาที่นี่เพื่อตั้งค่าคุณลักษณะ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>คุณไม่ได้ติดตามเอกสารอีกต่อไป:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"พจนานุกรม Python "
"ที่จะถูกประเมินเพื่อให้เป็นค่าเริ่มต้นเมื่อสร้างการบันทึกใหม่สำหรับนามแฝงนี้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A Scheduled Message cannot be scheduled in the past"
msgstr "ไม่สามารถกำหนดเวลาส่งข้อความในอดีตได้"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "การปรากฏตัวของรถบัสจะต้องมีผู้ใช้หรือลูกค้า"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "สมาชิกของช่องจะต้องเป็นพาร์ทเนอร์หรือลูกค้า"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "ช่องประเภท 'แชท' ไม่สามารถมีผู้ใช้เกินสองคน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "ไม่ควรสร้างการแชทกับบุคคลมากกว่า 2 คน ให้สร้างกลุ่มแทน"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "A message can only be scheduled in monocomment mode"
msgstr "สามารถกำหนดเวลาข้อความได้เฉพาะในโหมดความคิดเห็นเดียวเท่านั้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"A message cannot be scheduled on a model that does not have a mail thread."
msgstr "ไม่สามารถกำหนดเวลาส่งข้อความบนโมเดลที่ไม่มีเธรดอีเมลได้"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "การโต้ตอบข้อความจะต้องมาจากพาร์ทเนอร์หรือลูกค้า"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "A next activity can only be planned on models that use activities."
msgstr "กิจกรรมถัดไปสามารถวางแผนได้บนโมเดลที่ใช้กิจกรรมเท่านั้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A scheduled message could not be sent"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"ต้องใช้คีย์ Google API ที่ถูกต้องเพื่อเปิดใช้งานการแปลข้อความ "
"https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "การตั้งค่าระดับเสียงจะต้องมีพาร์ทเนอร์หรือลูกค้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_chatgpt.js:0
msgid "AI"
msgstr "AI"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept"
msgstr "ยอมรับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept with camera"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Access Denied"
msgstr "การเข้าถึงถูกปฏิเสธ"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "กลุ่มการเข้าถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "โทเคนเข้าถึง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "การเข้าถึงถูกจำกัดเฉพาะกลุ่ม \"%(groupFullName)s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Account"
msgstr "บัญชี"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "การดำเนินการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "มุมมองหน้าต่างการดำเนินการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Actions"
msgstr "การดำเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"การดำเนินการอาจทำให้เกิดพฤติกรรมเฉพาะเช่นการเปิดมุมมองปฏิทินหรือทำเครื่องหมายโดยอัตโนมัติว่าเสร็จสิ้นเมื่ออัปโหลดเอกสาร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "การดำเนินการที่ต้องทำกับเมลขาเข้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "เปิดใช้งานตามค่าเริ่มต้นเมื่อสมัครสมาชิก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "เปิดใช้งาน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities_section
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activities"
msgstr "กิจกรรม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Create"
msgstr "กิจกรรมที่จะสร้าง"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr "กิจกรรมจะต้องเชื่อมโยงกับบันทึกที่มี res_id ไม่เป็นค่าว่าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity"
msgstr "กิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "กิจกรรม Mixin"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.ui.menu,name:mail.menu_mail_activities
msgid "Activity Overview"
msgstr "ภาพรวมกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "แผนกิจกรรม"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "แผนกิจกรรม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "การตั้งค่ากิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Activity Type Name"
msgstr "ชื่อประเภทกิจกรรม"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "ประเภทกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "แบบฟอร์มแผนกิจกรรม"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Prospect Follow-up\", \"Project Milestone Meeting\", ...)"
msgstr ""
"แผนกิจกรรมใช้เพื่อกำหนดรายการกิจกรรมด้วยการคลิกเพียงไม่กี่ครั้ง\n"
"                    (เช่น \"การเริ่มต้นใช้งาน\" \"การติดตามผลผู้มีแนวโน้มจะเป็นลูกค้า\" \"การประชุมหลักสำคัญของโครงการ\" ...)"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "โปรแกรมสร้างแผนกำหนดการกิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Activity type"
msgstr "ประเภทกิจกรรม"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Activity: %s"
msgstr "กิจกรรม: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "เพิ่มการดำเนินการตามบริบท"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "เพิ่มบัญชีดำอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add Followers"
msgstr "เพิ่มผู้ติดตาม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.xml:0
msgid "Add Reaction"
msgstr "เพิ่มการโต้ตอบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Add a Reaction"
msgstr "เพิ่มการโต้ตอบ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "เพิ่มคีย์ Tenor GIF API เพื่อเปิดใช้การรองรับ GIF"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"เพิ่มคีย์ Tenor GIF API เพื่อเปิดใช้การรองรับ GIF "
"https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Add a description"
msgstr "เพิ่มคำอธิบาย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "เพิ่มคำอธิบายให้กับกิจกรรมของคุณ..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "เพิ่ม%(document)s ใหม่หรือส่งอีเมลไปที่ %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "เพิ่มที่อยู่อีเมลลงในบัญชีแบล็คลิสต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add and close"
msgstr "เพิ่มและปิด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Add as recipient and follower (reason: %s)"
msgstr "เพิ่มเป็นผู้รับและผู้ติดตาม (สาเหตุ: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts"
msgstr "เพิ่มผู้ติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Add contacts to notify..."
msgstr "เพิ่มที่ติดต่อเพื่อแจ้งให้ทราบ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
msgid "Add followers to this document"
msgstr "เพิ่มผู้ติดตามลงในเอกสารนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Add or join a channel"
msgstr "เพิ่มหรือเข้าร่วมช่อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "เพิ่มลายเซ็น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "เพิ่มข้อมูลรับรอง twilio ของคุณสำหรับเซิร์ฟเวอร์ ICE"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "ไม่สามารถเพิ่มผู้ติดตามในช่องได้ ลองเพิ่มสมาชิกแทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr "ไม่สามารถเพิ่มสมาชิกในการแชทนี้ได้ มันถูกออกแบบมาสำหรับผู้ใช้งานสองคน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "ผู้ติดต่อเพิ่มเติม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "ระดับสูง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "ตัวเลือกขั้นสูง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__after_plan_date
msgid "After Plan Date"
msgstr "หลังจากวันที่วางแผน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "การเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "นามแฝง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"นามแฝง %(matching_name)s (%(current_id)s) เชื่อมโยงกับ %(alias_model_name)s "
"(%(matching_id)s) แล้ว และใช้โดย %(parent_name)s %(parent_model_name)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"นามแฝง %(matching_name)s (%(current_id)s) เชื่อมโยงกับ %(alias_model_name)s "
"(%(matching_id)s) แล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "นามแฝงสำหรับติดต่อเพื่อความปลอดภัย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "ชื่อโดเมน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "ชื่อโดเมนนามแฝง"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "โดเมนนามแฝง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "อีเมลนามแฝง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "นามแฝง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "สถานะนามแฝง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "ชื่อโดเมนนามแฝง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "ประเมินสถานะนามแฝงตามข้อความล่าสุดที่ได้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "โมเดลนามแฝง"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "นามแฝง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""
"มีการใช้นามแฝง %(alias_names)s เป็นที่อยู่ตีกลับหรือที่รับทั้งหมดแล้ว "
"โปรดเลือกนามแฝงอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "All"
msgstr "ทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__all
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__all
msgid "All Messages"
msgstr "ข้อความทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "All conversations have been muted"
msgstr "บทสนทนาทั้งหมดถูกปิดเสียงแล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "All partners must belong to the same message"
msgstr "พาร์ทเนอร์ทั้งหมดจะต้องอยู่ในข้อความเดียวกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "อนุญาตให้อัปโหลดแบบสาธารณะ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"มีข้อผิดพลาด SSL เกิดขึ้น ตรวจสอบการกำหนดค่า SSL/TLS บนพอร์ตเซิร์ฟเวอร์\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "An access token must be provided for each attachment."
msgstr "ต้องระบุโทเค็นการเข้าถึงสำหรับไฟล์แนบแต่ละรายการ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "An email is required for find_or_create to work"
msgstr "จำเป็นต้องมีอีเมลเพื่อให้ find_or_create ทำงาน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email"
msgstr "เกิดข้อผิดพลาดขณะส่งอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email on “%(record_name)s”"
msgstr "เกิดข้อผิดพลาดขณะส่งอีเมลใน “%(record_name)s”"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "An error occurred while fetching messages."
msgstr "เกิดข้อผิดพลาดขณะเรียกข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "An unexpected error occurred during the creation of the chat."
msgstr "เกิดข้อผิดพลาดที่ไม่คาดคิดระหว่างการสร้างการแชท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And"
msgstr "และ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And 1 other member."
msgstr "และสมาชิกอีก 1 คน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "ใช้กับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
msgid "Apply"
msgstr "นำไปใช้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr "เก็บถาวรเนื่องจาก %(user_name)s (#%(user_id)s) ลบบัญชีพอร์ทัล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Are you sure you want to cancel the scheduled message?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกข้อความที่กำหนดเวลาไว้?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are you sure you want to delete \"%(template_name)s\"?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบ \"%(template_name)s\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบเทมเพลตจดหมายนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Are you sure you want to delete this message?"
msgstr "คุณแน่ใจหรือว่าต้องการลบข้อความนี้?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการรีเซ็ตเทมเพลตอีเมลเหล่านี้เป็นการกำหนดค่าเดิม "
"การเปลี่ยนแปลงและการแปลจะหายไป"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการแบล็คลิสต์ที่อยู่อีเมลนี้?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Are you sure you want to unblacklist this email address?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกแบล็คลิสต์ที่อยู่อีเมลนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are your sure you want to update \"%(template_name)s\"?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการอัปเดต \"%(template_name)s\""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "ถามตอนเปิดตัว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to ..."
msgstr "มอบหมายให้..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to me"
msgstr "มอบหมายให้ฉัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Assigned To"
msgstr "มอบหมายให้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
msgid "Assigned to"
msgstr "มอบหมายให้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "งานที่มอบหมาย"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "At this point lang should be correctly set"
msgstr "ในตอนนี้ ภาษาควรได้รับการตั้งค่าอย่างถูกต้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Attach files"
msgstr "แนบไฟล์"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "การแนบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Attachment counter loading..."
msgstr "กำลังโหลดตัวนับไฟล์แนบ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr "แนบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Audio player:"
msgstr "เครื่องเสียง:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "พาร์ทเนอร์ที่ได้รับการรับรอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "ผู้เขียน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"ผู้เขียนข้อความ หากไม่ได้ตั้งค่า email_from "
"อาจมีที่อยู่อีเมลที่ไม่ตรงกับพาร์ทเนอร์รายใด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "อวาตาร์ของผู้เขียน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "กลุ่มที่ได้รับสิทธิ์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__group_ids
msgid "Authorized Groups"
msgstr "กลุ่มที่ได้รับอนุญาต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "ลบอัตโนมัติ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "สมัครสมาชิกกลุ่มอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "สมัครสมาชิกอัตโนมัติ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "สมัครสมาชิกอัตโนมัติ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "การแจ้งเตือนที่กำหนดเป้าหมายอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "กิจกรรมอัตโนมัติ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Automated message"
msgstr "ข้อความอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"กำหนดเวลากิจกรรมนี้โดยอัตโนมัติเมื่อกิจกรรมปัจจุบันถูกทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
msgid "Avatar"
msgstr "อวตาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "อวตาร 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "อวตาร 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "อวตาร 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "อวตาร 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_cache_key
msgid "Avatar Cache Key"
msgstr "คีย์แคชอวาตาร์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Avatar of user"
msgstr "รูปประจำตัวของผู้ใช้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Away"
msgstr "ห่างออกไป"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Background blur intensity"
msgstr "ความเข้มของพื้นหลังเบลอ"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "ฐาน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "เทมเพลตฐาน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "เทมเพลตฐาน"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "คีย์เข้ารหัส Base64"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "องค์ประกอบแบทช์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr "บันทึกแบทช์ไม่รองรับไฟล์แนบหรือค่าการติดตามในเอกสารมากกว่า 1 ฉบับ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__before_plan_date
msgid "Before Plan Date"
msgstr "ก่อนวันที่วางแผน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "บัญชีแบล็คลิสต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "วันที่บัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "ที่อยู่บัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "ที่อยู่อีเมลบัญชีแบล็คลิสต์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"ถูกบล็อกโดยการลบบัญชีพอร์ทัล %(portal_user_name)s โดย %(user_name)s "
"(#%(user_id)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Blur Background"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Blur video background"
msgstr "วิดีโอพื้นหลังเบลอ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "เนื้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "เนื้อหาเนื้อความเหมือนกับเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Bot"
msgstr "บอท"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "การตีกลับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "นามแฝงตีกลับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "อีเมลตีกลับ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"นามแฝงการตีกลับ %(bounce)s ถูกใช้อยู่แล้วสำหรับโดเมนอื่นที่มีชื่อเดียวกัน "
"ใช้การตีกลับอื่นหรือใช้โดเมนนามแฝงอื่น"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "อีเมลตีกลับไม่ควรซ้ำกัน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"ตีกลับ/Catchall '%(matching_alias_name)s' ถูกใช้ไปแล้วโดย %(document_name)s "
"เลือกนามแฝงอื่นหรือเปลี่ยนในเอกสารอื่น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"ตีกลับ/Catchall '%(matching_alias_name)s' ถูกใช้แล้ว "
"เลือกนามแฝงอื่นหรือเปลี่ยนในโมเดลที่เชื่อมโยง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
msgid "Bounced"
msgstr "การตีกลับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""
"Brave: เปิดใช้งาน 'บริการ Google สำหรับการส่งข้อความแบบ Push' "
"เพื่อเปิดใช้งานการแจ้งเตือนแบบพุช"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Browser default"
msgstr "เบราว์เซอร์เริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__endpoint
msgid "Browser endpoint"
msgstr "ปลายทางเบราว์เซอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__keys
msgid "Browser keys"
msgstr "ปุ่มเบราว์เซอร์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
msgid "CC Email"
msgstr "CC อีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "CTRL"
msgstr "CTRL"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "โทร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Call Settings"
msgstr "การตั้งค่าการโทร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Camera is off"
msgstr "กล้องปิดอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "ยกเลิกได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "แก้ไขเนื้อหาหลักได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "ส่งซ้ำได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "เขียนได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Can not update the message or recipient of a notification."
msgstr "ไม่สามารถอัปเดตข้อความหรือผู้รับการแจ้งเตือนได้"

#. module: mail
#: model:ir.model,name:mail.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "สามารถส่งข้อความผ่าน bus.bus ได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "ยกเลิกอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Cancel Message"
msgstr "ยกเลิกข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
msgid "Cancelled"
msgstr "ยกเลิก"

#. module: mail
#: model:ir.model,name:mail.model_mail_canned_response
msgid "Canned Response"
msgstr "คำตอบสำเร็จรูป"

#. module: mail
#: model:res.groups,name:mail.group_mail_canned_response_admin
msgid "Canned Response Administrator"
msgstr "ผู้ดูแลระบบการตอบกลับสำเร็จรูป"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_canned_response_action
#: model:ir.module.category,name:mail.module_category_canned_response
#: model:ir.ui.menu,name:mail.menu_canned_responses
msgid "Canned Responses"
msgstr "การตอบกลับสำเร็จรูป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Canned Responses Search"
msgstr "ค้นหาคำตอบสำเร็จรูป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_form
msgid "Canned response"
msgstr "คำตอบสำเร็จรูป"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__source
msgid ""
"Canned response that will automatically be substituted with longer content "
"in your messages. Type ':' followed by the name of your shortcut (e.g. "
":hello) to use in your messages."
msgstr ""
"การตอบกลับสำเร็จรูปที่จะถูกแทนที่ด้วยเนื้อหาที่ยาวกว่าในข้อความของคุณโดยอัตโนมัติ"
" พิมพ์ ':' ตามด้วยชื่อทางลัดของคุณ (เช่น :hello) เพื่อใช้ในข้อความของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_tree
msgid "Canned responses"
msgstr "คำตอบสำเร็จรูป"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                    your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                    replaced directly in your message, so that you can still edit\n"
"                    it before sending."
msgstr ""
"การตอบกลับสำเร็จรูปช่วยให้คุณสามารถแทรกการตอบกลับที่เขียนไว้ล่วงหน้าใน\n"
"                    ข้อความของคุณโดยการพิมพ์ <i>:shortcut</i> ทางลัดจะถูกแทนที่\n"
"                    ด้วยข้อความของคุณโดยตรง ดังนั้นคุณจึงยังคงสามารถแก้ไขได้\n"
"                    ก่อนที่จะส่ง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change initial message nor parent channel of: %(channels)s."
msgstr "ไม่สามารถเปลี่ยนข้อความเริ่มต้นหรือช่องหลักของ: %(channels)s ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "ไม่สามารถเปลี่ยนประเภทช่องของ: %(channel_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: initial message should belong to parent channel."
msgstr "ไม่สามารถสร้าง %(channels)s ได้: ข้อความเริ่มต้นควรเป็นของช่องหลัก"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: parent should not be a sub-channel and should be"
" of type 'channel'."
msgstr ""
"ไม่สามารถสร้าง %(channels)s ได้: ช่องหลักไม่ควรเป็นช่องย่อยและควรเป็นประเภท "
"'ช่อง'"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "ผู้รับข้อความสำเนาคาร์บอน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "ผู้รับสำเนาคาร์บอน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "ผู้รับสำเนาคาร์บอน (อาจใช้ตัวยึดตำแหน่งที่นี่)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "จับทั้งหมด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "นางแฝง Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "อีเมลรับทั้งหมด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"นามแฝงการจับทั้งหมด %(catchall)s "
"ถูกใช้ไปแล้วสำหรับโดเมนอื่นที่มีชื่อเดียวกัน ใช้ catchall "
"อื่นหรือเพียงแค่ใช้โดเมนชื่อแทนอื่น"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "อีเมล Catchall ไม่ควรซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "ประเภทการผูกมัด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "เปลี่ยนสีพื้นหลังของกิจกรรมที่เกี่ยวข้องประเภทนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Channel"
msgstr "ช่อง"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "สมาชิกช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
#: model:ir.model.fields,field_description:mail.field_res_users_settings__channel_notifications
msgid "Channel Notifications"
msgstr "การแจ้งเตือนของช่อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "ประเภทช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Channel full"
msgstr "ช่องเต็ม"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "Channel members cannot include public users."
msgstr "สมาชิกของช่องไม่สามารถรวมผู้ใช้สาธารณะได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Channel settings"
msgstr "การตั้งค่าช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
msgid "Channels"
msgstr "ช่อง"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "ช่องทาง/สมาชิก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
msgid "Chat"
msgstr "แชท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Chat Options"
msgstr "ตัวเลือกการแชท"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"การแชทเป็นแบบส่วนตัวและไม่ซ้ำกันระหว่าง 2 คน "
"กลุ่มเป็นแบบส่วนตัวในกลุ่มผู้ได้รับเชิญ สามารถเข้าร่วมช่องได้อย่างอิสระ "
"(ขึ้นอยู่กับการกำหนดค่า)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Chats"
msgstr "แชท"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "ตรวจสอบรายชื่อยกเว้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "ข้อความย่อย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "เลือกเทมเพลต..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "เลือกผู้ใช้..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Choose another value or change it on the other document."
msgstr "เลือกค่าอื่นหรือเปลี่ยนแปลงในเอกสารอื่น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "เลือกการมอบหมายกิจกรรมด้วยการมอบหมายตามความต้องการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Clear quick search"
msgstr "ล้างการค้นหาด่วน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Click here to retry"
msgstr "คลิกที่นี่เพื่อลองอีกครั้ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Click to see the attachments"
msgstr "คลิกเพื่อดูเอกสารแนบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Close"
msgstr "ปิด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "Close Chat Bubble"
msgstr "ปิดช่องแชท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Chat Window (ESC)"
msgstr "ปิดหน้าต่างแชท (ESC)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Search"
msgstr "ปิดการค้นหา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Close all conversations"
msgstr "ปิดการสนทนาทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_ad_banner.xml:0
msgid "Close banner"
msgstr "ปิดแบนเนอร์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Close button"
msgstr "ปุ่มปิด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
msgid "Close panel"
msgstr "ปิดแผงควบคุม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
msgid "Close search"
msgstr "ปิดการค้นหา"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "ปิดแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Collapse panel"
msgstr "ยุบแผง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "รวบรวมการตอบกลับตามที่อยู่อีเมลที่ระบุ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Come here often? Install the app for quick and easy access!"
msgstr "มาที่นี่บ่อยใช่ไหม? ติดตั้งแอปเพื่อเข้าถึงได้อย่างรวดเร็วและง่ายดาย!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "ที่อยู่ผู้รับสำเนาคาร์บอนที่คั่นด้วยเครื่องหมายจุลภาค"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "ID ที่คั่นด้วยเครื่องหมายจุลภาคของพาร์ทเนอร์ผู้รับ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"รหัสที่คั่นด้วยเครื่องหมายจุลภาคของพาร์ทเนอร์ผู้รับ "
"(อาจใช้ตัวยึดตำแหน่งที่นี่)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "ที่อยู่ผู้รับโดยคั่นด้วยเครื่องหมายจุลภาค"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "ที่อยู่ผู้รับที่คั่นด้วยเครื่องหมายจุลภาค (อาจใช้ตัวยึดตำแหน่งที่นี่)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "บริษัท"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr "บริษัทที่ใช้โดเมนนี้เป็นค่าเริ่มต้นในการส่งอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "บริษัท"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr "เขียนอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "โหมดการเขียน"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model:ir.ui.menu,name:mail.menu_configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "กำหนดค่ารายการเซิร์ฟเวอร์ ICE ของคุณสำหรับ webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "กำหนดค่าประเภทกิจกรรมของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "กำหนดค่าเซิร์ฟเวอร์อีเมลของคุณเอง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
msgid "Confirm"
msgstr "ยืนยัน"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Confirmation"
msgstr "การยืนยัน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Congratulations, you're done with your activities."
msgstr "ขอแสดงความยินดี คุณทำกิจกรรมเสร็จแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Congratulations, your inbox is empty"
msgstr "ยินดีด้วย กล่องจดหมายของคุณว่างเปล่า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_patch.js:0
msgid "Congratulations, your inbox is empty!"
msgstr "ขอแสดงความยินดี กล่องจดหมายของคุณว่างเปล่า"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "การเชื่อมต่อล้มเหลว (ปัญหาเซิร์ฟเวอร์อีเมลขาออก)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Connection test failed: %s"
msgstr "การทดสอบการเชื่อมต่อล้มเหลว: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid ""
"Connection to SFU server closed by the server, falling back to peer-to-peer"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection type:"
msgstr "ประเภทการเชื่อมต่อ:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection:"
msgstr "การเชื่อมต่อ:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"การเชื่อมต่อถูกเข้ารหัสด้วย SSL/TLS ผ่านพอร์ตเฉพาะ (ค่าเริ่มต้น: IMAPS=993, "
"POP3S=995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "ถือว่าคำตอบเป็นเธรดใหม่"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "ติดต่อผู้ดูแลระบบของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "การติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "โมเดลคอนเทนเนอร์"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this user. This includes: creating, joining, pinning"
msgstr ""
"ประกอบด้วยวันที่และเวลาของเหตุการณ์ที่น่าสนใจล่าสุดที่เกิดขึ้นในช่องนี้สำหรับผู้ใช้รายนี้"
" ซึ่งรวมถึง: การสร้าง การเข้าร่วม การปักหมุด"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel. This updates itself when new message posted."
msgstr ""
"ประกอบด้วยวันและเวลาเหตุการณ์ที่น่าสนใจล่าสุดที่เกิดขึ้นในช่องนี้ "
"สิ่งนี้จะอัปเดตตัวเองเมื่อมีการโพสต์ข้อความใหม่"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__unpin_dt
msgid "Contains the date and time when the channel was unpinned by the user."
msgstr "ประกอบด้วยวันที่และเวลาที่ผู้ใช้เลิกปักหมุดช่องสัญญาณ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "เนื้อหา"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""
"เนื้อหาที่จะเข้ามาแทนที่ทางลัดที่คุณเลือกโดยอัตโนมัติ "
"เนื้อหานี้ยังสามารถปรับเปลี่ยนได้ก่อนที่จะส่งข้อความของคุณ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__body
msgid "Contents"
msgstr "เนื้อหา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "สถานะการพับการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Copy Link"
msgstr "คัดลอกลิงก์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"
msgstr ""
"ไม่สามารถรับอีเมลของคุณได้ ตรวจสอบข้อความแสดงข้อผิดพลาดด้านล่างเพื่อดูข้อมูลเพิ่มเติม:\n"
"%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "ตัวนับจำนวนอีเมลตีกลับสำหรับผู้ติดต่อรายนี้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "ประเทศ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Create"
msgstr "สร้าง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "สร้างกิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "วันที่สร้าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Create Group Chat"
msgstr "สร้างการแชทเป็นกลุ่ม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Create Thread"
msgstr "สร้างเธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "สร้าง Uid"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Create a Mail Template"
msgstr "สร้างเทมเพลตอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "สร้างบันทึกถัดไป"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid "Create a new canned response"
msgstr "สร้างการตอบกลับสำเร็จรูปใหม่"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr "สร้างแผนกิจกรรม"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s"
msgstr "สร้าง %(document)s ใหม่"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "สร้าง %(document)s ใหม่โดยส่งอีเมลไปที่ %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
msgid "Create: #"
msgstr "สร้าง: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Created"
msgstr "สร้างแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "สร้างโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/chatter.js:0
msgid "Creating a new record..."
msgstr "กำลังสร้างบันทึกใหม่..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "ผู้สร้าง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "ข้อมูลประจำตัว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "ผู้ใช้ปัจจุบันมีการแจ้งเตือนที่ติดดาวซึ่งเชื่อมโยงกับข้อความนี้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "ข้อความตีกลับที่กำหนดเอง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "รายการเซิร์ฟเวอร์ ICE ที่กำหนดเอง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "เทมเพลตที่กำหนดเอง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "เทมเพลตที่กำหนดเอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "ชื่อช่องที่กำหนดเอง"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "ลูกค้าจำเป็นต้องแจ้งทาง กล่องจดหมาย/อีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "ปรับแต่งรูปลักษณ์ของอีเมลอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "การแจ้งเตือนที่กำหนดเอง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Data channel:"
msgstr "ช่องทางข้อมูล:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "วันที่"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "วันเวลาที่ควรส่งการแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "วันที่และเวลาที่มีการปักหมุดข้อความ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "วัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "วันครบกำหนด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "วันครบกำหนด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Deadline: %s"
msgstr "วันครบกำหนด: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Deafen"
msgstr "คนหูหนวก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "เรียน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Dear Sender"
msgstr "เรียนผู้ส่ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "เรียนผู้ส่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "ประเภทการตกแต่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
#: model_terms:ir.ui.view,arch_db:mail.mail_message_subtype_view_search
msgid "Default"
msgstr "เริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "โหมดการแสดงผลเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "ค่าเริ่มต้นจาก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "ค่าเริ่มต้นจากนามแฝง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "บันทึกภายในเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "การสรุปเริ่มต้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Default Summary:"
msgstr "สรุปเริ่มต้น:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "ผู้ใช้เริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "ค่าเริ่มต้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Default deadline for the activities..."
msgstr "กำหนดเวลาเริ่มต้นสำหรับกิจกรรม..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"ค่าเริ่มต้นเมื่อไม่ตรงกับตัวกรองเซิร์ฟเวอร์ขาออก "
"สามารถเป็นได้ทั้งส่วนท้องถิ่นเช่น 'การแจ้งเตือน' "
"ไม่ว่าจะเป็นที่อยู่อีเมลที่สมบูรณ์ เช่น '<EMAIL>' "
"เพื่อแทนที่อีเมลขาออกทั้งหมด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "ผู้รับเริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"ผู้รับเริ่มต้นของบันทึก:\n"
"- พาร์ทเนอร์ (โดยใช้รหัสในพาร์ทเนอร์หรือช่อง Partner_id) หรือ\n"
"- อีเมล (ใช้ email_from หรือช่องอีเมล)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "ผู้ใช้เริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "กำหนดลำดับการประมวลผล ค่าที่ต่ำกว่าหมายถึงลำดับความสำคัญที่สูงกว่า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "ป้ายกำกับความล่าช้า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "ประเภทความล่าช้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Delay after releasing push-to-talk"
msgstr "ความล่าช้าหลังจากปล่อย push-to-talk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "หน่วยล่าช้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Delete"
msgstr "ลบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "ลบอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Delete Template"
msgstr "ลบเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Delete all previews"
msgstr "ลบการแสดงตัวอย่างทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
msgid "Delivered"
msgstr "จัดส่งแล้ว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "การส่งล้มเหลว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Delivery failure"
msgstr "การส่งมอบล้มเหลว"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr "การใช้งาน 'default_res_id' ที่เลิกใช้แล้ว ควรใช้ 'default_res_ids'"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "คำอธิบาย"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"คำอธิบายที่จะเพิ่มในข้อความที่โพสต์สำหรับประเภทย่อยนี้ "
"หากว่างเปล่าจะมีการเพิ่มชื่อแทน"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"กำหนดวิธีการแสดงช่องตามค่าเริ่มต้นเมื่อเปิดจากลิงก์คำเชิญ "
"ไม่มีค่าจะหมายถึงข้อความที่แสดง (ไม่มีเสียง/วิดีโอ)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_editable
msgid "Determines if the canned response can be edited by the current user"
msgstr "กำหนดว่าผู้ใช้ปัจจุบันสามารถแก้ไขการตอบกลับสำเร็จรูปได้หรือไม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_shared
msgid "Determines if the canned_response is currently shared with other users"
msgstr "กำหนดว่าปัจจุบัน canned_response แชร์กับผู้ใช้รายอื่นหรือไม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Direct messages"
msgstr "ข้อความโดยตรง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "Disconnect"
msgstr "ยกเลิกการเชื่อมต่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Disconnected from the RTC call by the server"
msgstr "ถูกตัดการเชื่อมต่อจากการเรียก RTC โดยเซิร์ฟเวอร์"

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "แชท"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "แชทแถบด้านข้าง"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "แชท: สมาชิกของช่องเปิดเสียง"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_users_settings_unmute_ir_actions_server
msgid "Discuss: users settings unmute"
msgstr "แชท: การตั้งค่าผู้ใช้เปิดเสียง"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "ช่องแชท"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "แชท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Dismiss"
msgstr "ยกเลิก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"แสดงตัวเลือกในเอกสารที่เกี่ยวข้อง "
"เพื่อเปิดตัวช่วยสร้างการเรียบเรียงด้วยเทมเพลตนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
msgid "Display avatar name"
msgstr "แสดงชื่อประจำตัว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
msgid "Do you really want to delete \"%s\"?"
msgstr "คุณต้องการลบ \"%s\" หรือไม่?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Do you really want to delete this preview?"
msgstr "คุณต้องการลบการแสดงตัวอย่างนี้หรือไม่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "เอกสาร"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "ผู้ติดตามเอกสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "รหัสเอกสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "โมเดลเอกสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "ชื่อเอกสาร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "เอกสาร: \""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "โดเมน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "เสร็จสิ้นและเปิดตัวถัดไป"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Schedule Next"
msgstr "เสร็จสิ้นและกำหนดเวลาถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done Date"
msgstr "วันที่เสร็จสิ้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Done and Schedule Next"
msgstr "เสร็จสิ้นและกำหนดเวลาถัดไป"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Download Files"
msgstr "ดาวน์โหลดไฟล์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Download logs"
msgstr "ดาวน์โหลดบันทึก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Download:"
msgstr "ดาวน์โหลด:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/mail_attachment_dropzone.xml:0
msgid "Drop Files here"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "วันครบกำหนด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "วันครบกำหนดเมื่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due in"
msgstr "ครบกำหนดใน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Due in %s days"
msgstr "ครบกำหนดใน %s วัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due on"
msgstr "ครบกำหนดเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "ประเภทวันครบกำหนด"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "อีเมลที่ซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "ระยะเวลาของกิจกรรมเสียงในหน่วย ms"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "รายงานแบบไดนามิก"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "ผู้ใช้แบบไดนามิก (ตามบันทึก)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Edge blur intensity"
msgstr "ความเข้มของขอบเบลอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Edit"
msgstr "แก้ไข"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "แก้ไขพาร์ทเนอร์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Message"
msgstr "แก้ไขข้อความที่กำหนดเวลาไว้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Note"
msgstr "แก้ไขบันทึกกำหนดการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "Edit Subscription of %(name)s"
msgstr "แก้ไขการสมัครสมาชิกของ %(name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Edit subscription"
msgstr "แก้ไขการสมัครรับข้อมูลข่าวสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
msgid "Edit: %s"
msgstr "แก้ไข: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "เพิ่มลายเซ็นอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "ที่อยู่อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "นามแฝงอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "อีเมลนามแฝง"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "อีเมลแทน Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "นามแฝงอีเมล Mixin (ใช้ง่าย)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "อีเมลบัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "สีของปุ่มอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "การจัดการ CC อีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "การตั้งค่าอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "โดเมนอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Email Failure: %(modelName)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "สีของหัวเรื่องอีเมล"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "การส่งอีเมลกลุ่ม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "รูปแบบการแจ้งเตือนทางอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "ตัวอย่างอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "ค้นหาอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "เทมเพลตอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "ตัวอย่างแม่แบบอีเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "เทมเพลตอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "เธรดอีเมล"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "ที่อยู่อีเมลมีอยู่แล้ว!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"ที่อยู่อีเมลของผู้ส่ง ฟิลด์นี้ถูกตั้งค่าเมื่อไม่พบคู่ที่ตรงกันและแทนที่ฟิลด์"
" author_id ในการแชท."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "ที่อยู่อีเมลที่การตอบกลับจะถูกเปลี่ยนเส้นทางเมื่อส่งอีเมลกลุ่ม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"ที่อยู่อีเมลที่การตอบกลับจะถูกเปลี่ยนเส้นทางเมื่อส่งอีเมลกลุ่ม "
"ใช้เฉพาะเมื่อการตอบกลับไม่ได้ถูกบันทึกไว้ในกระทู้การสนทนาเดิม"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "ที่อยู่อีเมลที่อยู่ในบัญชีแบล็คลิสต์จะไม่ได้รับอีเมลอีกต่อไป"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"ไม่สามารถใช้นามแฝงอีเมล %(alias_name)s กับหลายระเบียนพร้อมกันได้ "
"โปรดอัปเดตบันทึกทีละรายการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "cc อีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "ตัวช่วยการเขียนอีเมล"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "โดเมนอีเมล เช่น 'example.com' ใน '<EMAIL>'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "ข้อความอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "โปรแกรมสร้างการส่งอีเมลซ้ำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "เทมเพลตอีเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "อีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "Emoji"
msgstr "อิโมจิ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
msgid "Emojis"
msgstr "อีโมจิ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "เฉพาะพนักงานเท่านั้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Enable"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "เปิดใช้การติดตามคำสั่งซื้อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Enter"
msgstr "เข้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Enter Full Screen"
msgstr "เข้าสู่โหมดเต็มหน้าจอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
msgid "Error"
msgstr "ผิดพลาด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "ข้อความผิดพลาด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
msgid "Error during communication with the publisher warranty server."
msgstr ""
"เกิดข้อผิดพลาดระหว่างการสื่อสารกับเซิร์ฟเวอร์การรับประกันของผู้จัดพิมพ์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "ข้อความผิดพลาด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"เกิดข้อผิดพลาดโดยไม่มีข้อยกเว้น "
"อาจเป็นเพราะการอัปเดตการเข้าถึงบันทึกการแจ้งเตือนพร้อมกัน "
"กรุณาตรวจสอบกับผู้ดูแลระบบ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""
"เกิดข้อผิดพลาดโดยไม่มีข้อยกเว้น "
"อาจเป็นเพราะการส่งอีเมลโดยไม่มีผู้รับที่คำนวณได้"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "เกิดข้อผิดพลาด พาร์ทเนอร์ไม่สามารถติดตามวัตถุเดียวกันสองครั้งได้"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "ทุกคน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "ข้อยกเว้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Exit Fullscreen"
msgstr "ออกจากโหมดเต็มหน้าจอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Expand"
msgstr "ขยาย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Expand panel"
msgstr "ขยายแผง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__expiration_time
msgid "Expiration Token Date"
msgstr "วันที่โทเค็นหมดอายุ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "ตัวกรองเพิ่มเติม..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Extra Comments ..."
msgstr "ความคิดเห็นเพิ่มเติม..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "จดหมายล้มเหลว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "ล้มเหลว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid "Failed to enable push notifications"
msgstr "ไม่สามารถเปิดใช้งานการแจ้งเตือนแบบพุชได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Failed to load gifs..."
msgstr "ไม่สามารถโหลด GIFs..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr "โหลดเซิร์ฟเวอร์ SFU ไม่สำเร็จ กำลังถอยกลับไปเป็นแบบ P2P"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Failed to post the message. Click to retry"
msgstr "ไม่สามารถโพสต์ข้อความได้ คลิกเพื่อลองอีกครั้ง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"ไม่สามารถแสดงเทมเพลต QWeb: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render template: %(view_ref)s"
msgstr "ไม่สามารถแสดงเทมเพลต: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "เหตุผลความล้มเหลว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "สาเหตุที่ความล้มเหลว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"สาเหตุของความล้มเหลว โดยปกติจะเป็นข้อยกเว้นที่เซิร์ฟเวอร์อีเมลส่งออกไป "
"ซึ่งจัดเก็บไว้เพื่อความสะดวกในการแก้ไขข้อบกพร่องของปัญหาการส่งเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "ประเภทความล้มเหลว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Failure: %(modelName)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "เพิ่มเป็นรายการโปรดโดย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Favorites"
msgstr "รายการโปรด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Feedback:"
msgstr "ข้อเสนอแนะ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "ดึงข้อมูลตอนนี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "ดึงข้อมูล GIF ได้ตามจำนวนที่กำหนด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "ฟิลด์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"กิจกรรมของอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"แบล็คลิสต์ของอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"เธรดอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "รายละเอียดฟิลด์"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"ฟิลด์ที่ใช้เพื่อเชื่อมโยงโมเดลที่เกี่ยวข้องกับโมเดลย่อย "
"เมื่อใช้การสมัครสมาชิกอัตโนมัติในเอกสารที่เกี่ยวข้อง ฟิลด์นี้ใช้เพื่อคำนวณ "
"getattr ( related_document.relation_field )"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"ฟิลด์ “%(field)s” บนโมเดล “%(model)s” ต้องเป็นประเภท Many2one "
"และมีการติดตาม=True สำหรับการคำนวณระยะเวลา"

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "ฟิลด์"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "ไฟล์จากที่ที่เทมเพลตเริ่มต้น ใช้เพื่อรีเซ็ตเทมเพลตที่ใช้งานไม่ได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "File too large"
msgstr "ไฟล์มีขนาดใหญ่เกินไป"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is disabled for external users"
msgstr "การอัปโหลดไฟล์ถูกปิดใช้งานสำหรับผู้ใช้ภายนอก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is enabled for external users"
msgstr "เปิดใช้งานการอัปโหลดไฟล์สำหรับผู้ใช้ภายนอก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Files"
msgstr "ไฟล์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Fold"
msgstr "พับ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "พับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Follow"
msgstr "ติดตาม"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "แบบฟอร์มผู้ติดตาม"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "ผู้ติดตามเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "ผู้ติดตามที่จะเพิ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "ผู้ติดตามที่จะลบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Following"
msgstr "กำลังติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"สำหรับ %(channels)s channel_type ควรเป็น 'channel' "
"เพื่อให้มีการอนุญาตตามกลุ่มหรือการสมัครรับข้อมูลกลุ่มอัตโนมัติ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 1 hour"
msgstr "เป็นเวลา 1 ชั่วโมง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 15 minutes"
msgstr "เป็นเวลา 15 นาที"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 24 hours"
msgstr "เป็นเวลา 24 ชั่วโมง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 3 hours"
msgstr "เป็นเวลา 3 ชั่วโมง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 8 hours"
msgstr "เป็นเวลา 8 ชั่วโมง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "บังคับส่ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "บังคับให้ใช้ภาษา:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "รูปแบบอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "จาก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__from_message_id
msgid "From Message"
msgstr "จากข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "From peer:"
msgstr "จากเพื่อน:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Full composer"
msgstr "ผู้แต่งเต็ม"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "วิดีโอแบบเต็มหน้าจอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Future"
msgstr "อนาคต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Category"
msgstr "หมวดหมู่ GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Favorites"
msgstr "GIF รายการโปรด"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF รายการโปรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "รหัส GIF จาก Tenor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "GIFs"
msgstr "GIFs"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "เกตเวย์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Go to conversation"
msgstr "ไปที่การสนทนา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "การผสานการใช้งาน Google Translate"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "กลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "ชื่อกลุ่ม"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""
"การอนุญาตกลุ่มและการสมัครสมาชิกกลุ่มอัตโนมัติได้รับการรับรองในช่องเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "จัดกลุ่มโดย…"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_sub_channel_no_group_public_id
msgid ""
"Group public id should not be set on sub-channels as access is based on "
"parent channel"
msgstr ""
"ไม่ควรกำหนดกลุ่ม ID สาธารณะบนช่องย่อย "
"เนื่องจากการเข้าถึงจะอิงจากช่องหลักเท่านั้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Grouped Chat"
msgstr "แชทกลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "กลุ่ม"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
msgid "Guest"
msgstr "ลูกค้า"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name cannot be empty."
msgstr "ชื่อลูกค้าไม่สามารถเว้นว่างได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name is too long."
msgstr "ชื่อของลูกค้ายาวเกินไป"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "ลูกค้า"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "จัดการโดยอีเมลล์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "จัดการในระบบ odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "มีข้อผิดพลาด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "มีอีเมลกิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "มีอีเมลบัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "มีอีเมลเธรด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "มีการกล่าวถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "ได้ปิดการใช้งานเสียงที่เข้ามา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "มีข้อผิดพลาด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "มีหน้าที่ตามความต้องการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "ส่วนหัว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "เรียน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "ซ่อนแล้ว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "เทมเพลตที่ถูกซ่อน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
msgid "Hide Pinned Messages"
msgstr "ซ่อนข้อความที่ปักหมุด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Hide all conversations"
msgstr "ซ่อนการสนทนาทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Hide sidebar"
msgstr "ซ่อนแถบด้านข้าง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "ซ่อนประเภทย่อยในตัวเลือกผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"ซ่อนสำหรับผู้ใช้สาธารณะ/พอร์ทัล โดยไม่ขึ้นอยู่กับการกำหนดค่าประเภทย่อย"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "สูง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "History"
msgstr "ประวัติ"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "ชื่อโฮสต์หรือ IP ของเมลเซิร์ฟเวอร์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Hover on your message and mark as todo"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"ระยะเวลาที่การออกอากาศเสียงจะยังคงทำงานอยู่หลังจากผ่านเกณฑ์ระดับเสียงแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE gathering:"
msgstr "การรวมตัวของ ICE:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_push__id
#: model:ir.model.fields,field_description:mail.field_mail_push_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID ของบันทึกหลักที่มีนามแฝง (ตัวอย่าง: โปรเจ็กต์ที่มีนามแฝงในการสร้างงาน)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "สถานะ IM"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "เซิร์ฟเวอร์ IMAP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id ของทรัพยากรที่ติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "ตัวตน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Idle"
msgstr "ไม่ได้ใช้งาน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "หากจำเป็นต้องใช้ SSL"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""
"หากตั้งค่าไว้ ทุกการแก้ไขที่ทำในฟิลด์นี้จะถูกติดตาม "
"ค่าใช้เพื่อสั่งซื้อค่าการติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr "หากตั้งค่าไว้ สมาชิกจะไม่ได้รับการแจ้งเตือนจากช่องจนกว่าจะถึงวันนี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"หากตั้งค่าไว้ ผู้จัดการคิวจะส่งอีเมลหลังจากวันที่ดังกล่าว หากไม่ได้ตั้งค่า "
"อีเมลจะถูกส่งโดยเร็วที่สุด เว้นแต่จะระบุเขตเวลา ซึ่งจะถือว่าอยู่ในเขตเวลา "
"UTC"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"หากตั้งค่าไว้ผู้จัดการคิวจะส่งอีเมลหลังจากวันที่ดังกล่าว หากไม่ได้ตั้งค่าไว้"
" ระบบจะส่งอีเมลโดยเร็วที่สุด คุณสามารถใช้ตัวสั่งงานแบบไดนามิกได้"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__mute_until_dt
msgid ""
"If set, the user will not receive notifications from all the channels until "
"this date."
msgstr ""
"หากตั้งค่าไว้ผู้ใช้จะไม่ได้รับการแจ้งเตือนจากทุกช่องทางจนกว่าจะถึงวันนี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"หากตั้งค่าไว้ "
"เนื้อหานี้จะถูกส่งไปยังผู้ใช้ที่ไม่ได้รับอนุญาตโดยอัตโนมัติแทนข้อความเริ่มต้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"หากตั้งค่าไว้ จะจำกัดเทมเพลตไว้เฉพาะผู้ใช้รายนี้"
"                                                   หากไม่ได้ตั้งค่า "
"จะแชร์กับผู้ใช้ทั้งหมด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"หากที่อยู่อีเมลมีอยู่ในบัญชีดำ ผู้ติดต่อจะไม่ได้รับเมลกลุ่มอีกต่อไป "
"จากรายการใด ๆ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_scheduled_message__is_note
msgid "If the message will be posted as a Note."
msgstr "หากข้อความจะถูกโพสต์เป็นโน๊ต"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"หากเป็นจริง คำตอบจะไม่อยู่ในเธรดการพูดคุยในเอกสารต้นฉบับ "
"แต่จะตรวจสอบการตอบกลับในการติดตาม message-id และเปลี่ยนเส้นทางตามนั้น "
"สิ่งนี้มีผลกระทบต่อ message-id ที่สร้างขึ้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"หากคุณได้ตั้งค่าโดเมนอีเมลที่รับทั้งหมดซึ่งเปลี่ยนเส้นทางไปยังเซิร์ฟเวอร์ "
"Odoo ให้ป้อนชื่อโดเมนที่นี่"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "หากคุณต้องการใช้ twilio เป็นผู้ให้บริการเซิร์ฟเวอร์ TURN/STUN"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "ละเว้นทั้งหมด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "รูปภาพ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "ประเภทรูปภาพ MIME"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "รูปภาพคือลิงก์"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"ในโหมดแสดงความคิดเห็น: หากตั้งค่าไว้ ให้เลื่อนการส่งการแจ้งเตือนออกไป "
"ในโหมดอีเมลกลุ่ม: หากส่งแล้ว ให้ส่งอีเมลหลังจากวันที่ดังกล่าว "
"วันที่นี้ถือเป็นวันที่อยู่ในเขตเวลา UTC"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Inactive Alias"
msgstr "นามแฝงที่ไม่ใช้งาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "เซิร์ฟเวอร์จดหมายขาเข้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
msgid "Inbox"
msgstr "กล่องข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Call..."
msgstr "สายเรียกเข้า..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "อีเมลขาเข้า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Video Call..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"ระบุว่ากิจกรรมนี้ถูกสร้างขึ้นโดยอัตโนมัติและไม่ใช่โดยผู้ใช้รายใดรายนึง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Info"
msgstr "ข้อมูล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "โมเดลเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "ที่อยู่แบบอินไลน์ที่สมบูรณ์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Input device"
msgstr "อุปกรณ์การป้อนข้อมูล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Insert Template"
msgstr "แทรกเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Insert Templates"
msgstr "แทรกเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Install"
msgstr "ติดตั้ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Install Odoo"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "ผสาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "ภายในเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "การสื่อสารภายใน:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_count
msgid "Interval"
msgstr "ช่วงเวลา"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "ไม่ถูกต้อง"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Invalid domain “%(domain)s” (type “%(domain_type)s”)"
msgstr "โดเมนที่ไม่ถูกต้อง “%(domain)s” (พิมพ์ “%(domain_type)s”)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "ที่อยู่อีเมลที่ไม่ถูกต้อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Invalid email address “%s”"
msgstr "ที่อยู่อีเมลไม่ถูกต้อง “%s”"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"ตัวสั่งงานไม่ถูกต้อง จะต้องเป็นคำจำกัดความของพจนานุกรมคอมพิวเตอร์ตามตัวอักษร"
" เช่น \"{'field': 'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr "ช่อง “%(field_name)s” ไม่ถูกต้องเมื่อสร้างช่องกับสมาชิก"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "ไม่ถูกต้องจากที่อยู่"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Invalid primary email field on model %s"
msgstr "ฟิลด์อีเมลหลักไม่ถูกต้องในโมเดล %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "res_ids ไม่ถูกต้อง %(res_ids_str)s (ประเภท %(res_ids_type)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"ชื่อเซิร์ฟเวอร์ไม่ถูกต้อง!\n"
"%s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""
"เทมเพลตหรือแหล่งที่มาของมุมมอง %(svalue)s (ประเภท %(stype)s) ไม่ถูกต้อง "
"ควรเป็นบันทึกหรือ XMLID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""
"เทมเพลตที่ไม่ถูกต้องหรือรหัส Xml ของมุมมองแหล่งที่มา %(source_ref)s "
"ไม่มีอยู่อีกต่อไป"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""
"เทมเพลตหรือบันทึกแหล่งที่มาของมุมมอง %(svalue)s ไม่ถูกต้อง เปลี่ยนเป็น "
"%(model)s แทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""
"เทมเพลตหรือการอ้างอิงแหล่งที่มาของมุมมอง %(svalue)s ไม่ถูกต้อง เปลี่ยนเป็น "
"%(model)s แทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"ค่าไม่ถูกต้องเมื่อสร้างช่องที่มีการเป็นสมาชิก อนุญาตผู้ใช้เพียง 4 หรือ 6 "
"รายเท่านั้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"ค่าไม่ถูกต้องเมื่อสร้างช่องที่มีการเป็นสมาชิก อนุญาตผู้ใช้เพียง 0 "
"รายเท่านั้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "URL คำเชิญ"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "คำเชิญให้ติดตาม %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite"
msgstr "เชิญ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Invite People"
msgstr "เชิญผู้คน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Invite a User"
msgstr "เชิญผู้ใช้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Invite people"
msgstr "เชิญบุคคล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Channel"
msgstr "เชิญเข้าช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Group Chat"
msgstr "เชิญเข้าร่วมแชทกลุ่ม"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "เชิญโปรแกรมสร้าง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "มีการใช้งานอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "เป็นผู้ใช้ปัจจุบันหรือผู้เขียนรับเชิญ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "สามารถแก้ไขได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "เป็นผู้แก้ไข"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__is_hidden
msgid "Is Hidden"
msgstr "ถูกซ่อนไว้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "เป็นสมาชิก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "อ่านแล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "เป็น ด้วยตนเอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "เป็นตัวแก้ไขเทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "เป็นบันทึก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__is_note
msgid "Is a note"
msgstr "เป็นโน้ต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "ช่องหมวดหมู่ของการสนทนาแถบด้านข้างเปิดอยู่หรือไม่?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "แชทหมวดหมู่ของการสนทนาแถบด้านข้างเปิดอยู่หรือไม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "ปิดเสียงไมโครโฟนอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "ถูกปักหมุดไว้บนอินเทอร์เฟซ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "กำลังส่งวิดีโอของผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "กำลังแชร์หน้าจอ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""
"ดูเหมือนว่าคุณกำลังพยายามสร้างสมาชิกของช่อง "
"แต่ดูเหมือนว่าคุณลืมระบุช่องที่เกี่ยวข้อง หากต้องการดำเนินการต่อ "
"โปรดตรวจสอบให้แน่ใจว่าได้ให้ข้อมูลช่องที่จำเป็นแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_push_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"มันอ้างถึงคีย์เบราว์เซอร์ที่ใช้โดยการแจ้งเตือน:\n"
"- p256dh: เป็นรหัสสาธารณะการสมัครสมาชิกที่สร้างโดยเบราว์เซอร์ เบราว์เซอร์จะ\n"
"           เก็บคีย์ส่วนตัวไว้เป็นความลับและใช้เพื่อถอดรหัสเพย์โหลด\n"
"- auth: ค่าการรับรองความถูกต้องควรถือว่าเป็นความลับและห้ามแชร์ภายนอก Odoo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON ที่แมปรหัสจากฟิลด์ many2one เป็นวินาทีที่ใช้ไป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "เข้าร่วม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Join Call"
msgstr "เข้าร่วมการโทร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Join Channel"
msgstr "เข้าร่วมช่อง"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "เข้าร่วมกลุ่ม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
msgid "Jump"
msgstr "ข้าม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Jump to Present"
msgstr "ข้ามไปยังปัจจุบัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "เก็บไฟล์แนบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "เก็บเสร็จสิ้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "เก็บข้อความคัดลอก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "เก็บต้นฉบับไว้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr "เก็บสำเนาเนื้อหาอีเมลไว้หากอีเมลถูกลบ (เฉพาะการส่งอีเมลกลุ่มเท่านั้น)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr "เก็บกิจกรรมที่ทำเครื่องหมายว่าเสร็จสิ้นไว้ในมุมมองกิจกรรม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "คีย์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Kind Regards"
msgstr "ขอแสดงความนับถือ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "LIVE"
msgstr "ถ่ายทอดสด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "ภาษา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "วันที่ดึงข้อมูลล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "ดึงข้อมูลล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__last_interest_dt
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "ดอกเบี้ยล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "เห็นครั้งล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "อัปเดตล่าสุดเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__last_used
msgid "Last Used"
msgstr "ใช้ล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "วันที่พบเห็นล่าสุด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__last_used
msgid "Last time this canned_response was used"
msgstr "ครั้งล่าสุดที่ใช้ canned_response นี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Late"
msgstr "ล่าช้า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Launch Plans"
msgstr "แผนการเปิดตัว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "เลย์เอาต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "ลา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Channel"
msgstr "ออกจากช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Conversation"
msgstr "ออกจากการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Leave this channel"
msgstr "ออกจากช่องสนทนานี้"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "ลิงก์ตัวอย่าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Link copied!"
msgstr "คัดลอกลิงก์แล้ว!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "List Activity"
msgstr "รายการกิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "List users in the current channel"
msgstr "รายชื่อผู้ใช้ในช่องปัจจุบัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Load More"
msgstr "โหลดเพิ่มเติม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Load more"
msgstr "โหลดเพิ่ม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/navigable_list.xml:0
#: code:addons/mail/static/src/core/public_web/messaging_menu.xml:0
msgid "Loading…"
msgstr "กำลังโหลด..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "เซิร์ฟเวอร์ภายในเครื่อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "การตรวจจับขาเข้าตามชิ้นส่วนในพื้นที่"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""
"ส่วนของอีเมลที่ใช้สำหรับการตอบกลับเพื่อรับคำตอบ เช่น 'catchall' ใน "
"'<EMAIL>'"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"ส่วนของอีเมลภายในที่ใช้สำหรับ Return-Path ใช้เมื่ออีเมลตีกลับ เช่น 'ตีกลับ' "
"ใน '<EMAIL>'"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Log"
msgstr "ล็อก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Log Later"
msgstr "บันทึกภายหลัง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Log Now"
msgstr "บันทึกตอนนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Log RTC events"
msgstr "บันทึกอีเวนต์ RTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "บันทึกโน้ต..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "บันทึกกิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Log an internal note…"
msgstr "จดบันทึกภายใน…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Log note"
msgstr "บันทึกโน้ต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Log step:"
msgstr "ขั้นตอนการบันทึก:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
msgid "Logged in as %s"
msgstr "เข้าสู่ระบบด้วย %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "ข้อมูลล็อกอิน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "ต่ำ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Lower Hand"
msgstr "มือล่าง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "ประเภท MIME"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
msgid "Mail"
msgstr "เมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "กิจกรรมอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "การส่งเมลแบล็คลิสต์"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "การส่งเมลแบล็คลิสต์ mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "แบบฟอร์มช่องทางการส่งเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "ผู้แต่งการส่งเมล Mixin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mail Failures"
msgstr "ความล้มเหลวของเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "อนุญาตให้ใช้เกตเวย์เมล"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "Mail Layout"
msgstr "รูปแบบการส่งเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "การจัดการไฟล์แนบหลักของเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "รหัสข้อความเมล Int"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "เซสชัน RTC ของอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "มิกซ์อินการแสดงผลอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "เมลเซิร์ฟเวอร์ขาออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "เทมเพลตเมล"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "ตัวแก้ไขเทมเพลตอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "รีเซ็ตเทมเพลตอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "เธรดอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "ค่าการติดตามเมล"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"ผู้แต่งจดหมายในโหมดแสดงความคิดเห็นควรทำงานในบันทึกอย่างน้อยหนึ่งรายการ "
"ไม่พบบันทึก (โมเดล %(model_name)s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"อีเมลถูกสร้างขึ้นเพื่อแจ้งให้ผู้คนทราบเกี่ยวกับ mail.message ที่มีอยู่"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "Mail template model of %(action_name)s does not match action model."
msgstr "โมเดลเทมเพลตเมลของ %(action_name)s ไม่ตรงกับโมเดลการดำเนินการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "เทมเพลตเมลที่ใช้เมลเซิร์ฟเวอร์นี้"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "อีเมล: ตัวจัดการคิวอีเมล"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "อีเมล: บริการ Fetchmail"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_post_scheduled_message_ir_actions_server
msgid "Mail: Post scheduled messages"
msgstr "เมล: โพสต์ข้อความตามกำหนดเวลา"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "จดหมาย: ส่งการแจ้งเตือนแบบพุชทางเว็บ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Mailbox unavailable - %s"
msgstr "กล่องจดหมายไม่พร้อมใช้งาน - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mailboxes"
msgstr "กล่องจดหมาย"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""
"การส่งจดหมายหรือการโพสต์ด้วยแหล่งที่มาไม่ควรเรียก %(source_type)s "
"ว่าว่างเปล่า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "จัดการคำตอบเป็นอีเมลขาเข้าใหม่ แทนที่จะตอบกลับไปที่ชุดข้อความเดียวกัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Mark As Read"
msgstr "ทำเครื่องหมายว่าอ่านแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Mark Done"
msgstr "ทำเครื่องหมายว่าเสร็จแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Mark all read"
msgstr "ทำเครื่องหมายว่าอ่านแล้วทั้งหมด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Mark as Done"
msgstr "ทำเครื่องหมายว่าสำเร็จ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Mark as Read"
msgstr "ทำเครื่องหมายว่าอ่านแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Todo"
msgstr "ตั้งเป็นสิงที่ต้องทำ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Unread"
msgstr "ทำเครื่องหมายว่ายังไม่ได้อ่าน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Mark as done"
msgstr "ทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr "ไม่สามารถรับอุปกรณ์สื่อได้ SSL อาจตั้งค่าไม่ถูกต้อง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "วิธีที่เข้ามา"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "การประชุม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "จำนวนสมาชิก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "สมาชิก"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"สมาชิกของกลุ่มเหล่านั้นจะถูกเพิ่มเป็นผู้ติดตามโดยอัตโนมัติ "
"โปรดทราบว่าพวกเขาจะสามารถจัดการการสมัครสมาชิกด้วยตนเองได้หากจำเป็น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Mentions"
msgstr "กล่าวถึง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
msgid "Mentions Only"
msgstr "กล่าวถึงเท่านั้น"

#. module: mail
#: model:ir.model,name:mail.model_ir_ui_menu
msgid "Menu"
msgstr "เมนู"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "ตัวช่วยผสานพาร์ทเนอร์"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
msgid "Merged with the following partners: %s"
msgstr "รวมกับพาร์ทเนอร์ดังต่อไปนี้: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Message"
msgstr "ข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message \"%(subChannelName)s\""
msgstr "ข้อความ \"%(subChannelName)s\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message #%(threadName)s…"
msgstr "ข้อความ #%(threadName)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message %(thread name)s…"
msgstr "ข้อความ %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID ข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copied!"
msgstr "คัดลอกลิงก์ข้อความแล้ว!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "การคัดลอกลิงก์ข้อความล้มเหลว (ไม่ได้รับอนุญาต?)!"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "การแจ้งเตือนข้อความ"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "การโต้ตอบต่อข้อความ"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "การโต้ตอบต่อข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "ข้อความ"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "การแปลข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "คีย์ API การแปลข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "ประเภทข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__new_message_separator
msgid "Message id before which the separator should be displayed"
msgstr "รหัสข้อความที่ควรแสดงตัวคั่นก่อน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message posted on \"%s\""
msgstr "ข้อความที่โพสต์เมื่อ \"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "ผู้รับข้อความ (อีเมล)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "การอ้างอิงข้อความ เช่น ตัวระบุข้อความก่อนหน้า"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Message should be a valid EmailMessage instance"
msgstr "ข้อความควรเป็นอินสแตนซ์ข้อความของอีเมลที่ถูกต้อง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"ประเภทย่อยของข้อความช่วยให้ประเภทข้อความมีความแม่นยำยิ่งขึ้น "
"โดยเฉพาะการแจ้งเตือนของระบบ ตัวอย่างเช่น "
"อาจเป็นการแจ้งเตือนที่เกี่ยวข้องกับบันทึกใหม่ (ใหม่) "
"หรือการเปลี่ยนแปลงขั้นตอนในกระบวนการ (การเปลี่ยนแปลงขั้นตอน) "
"ประเภทย่อยของข้อความช่วยให้ปรับแต่งการแจ้งเตือนที่ผู้ใช้ต้องการรับบนผนังได้อย่างแม่นยำ"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "ประเภทย่อยของข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"ประเภทย่อยของข้อความที่ตามมา "
"หมายถึงประเภทย่อยที่จะถูกผลักเข้าสู่วอลล์ของผู้ใช้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"ประเภทข้อความ: อีเมลสําหรับข้อความอีเมล, การแจ้งเตือนสําหรับข้อความของระบบ, "
"ข้อคิดเห็นสําหรับข้อความอื่น ๆ เช่นการตอบกลับของผู้ใช้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "ตัวระบุข้อความที่ไม่ซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Message-Id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "ข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "ค้นหาข้อความ"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_from_message_id_unique
msgid "Messages can only be linked to one sub-channel"
msgstr "ข้อความสามารถเชื่อมโยงกับช่องย่อยได้เพียงช่องเดียวเท่านั้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Messages marked as read will appear in the history."
msgstr "ข้อความที่ทำเครื่องหมายว่าอ่านแล้วจะแสดงขึ้นในประวัติ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"ข้อความที่มีประเภทย่อยภายในจะแสดงให้เห็นโดยพนักงานเท่านั้น "
"หรือที่เรียกว่าสมาชิกของกลุ่ม base_user"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Messages with tracking values cannot be modified"
msgstr "ข้อความที่มีค่าการติดตามไม่สามารถแก้ไขได้"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "ข้อมูลเมตาสำหรับไฟล์เสียงที่แนบ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "อีเมลที่หายไป"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "ที่อยู่อีเมลหายไป"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "หายไปจากที่อยู่"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""
"Mixin ใช้เพื่อคำนวณเวลาที่ใช้ไปในแต่ละค่าที่ฟิลด์ many2one สามารถทำได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "โมเดล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "โมเดลมีการเปลี่ยนแปลง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "รูปแบบของทรัพยากรที่ตามมา"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr "โมเดลที่ประเภทย่อยนำไปใช้ หากเป็น False ชนิดย่อยนี้จะใช้กับทุกโมเดล"

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "โมเดล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"การปรับเปลี่ยนโมเดลอาจมีผลกระทบต่อกิจกรรมที่มีอยู่โดยใช้ประเภทกิจกรรมนี้ "
"โปรดใช้ด้วยความระมัดระวัง"

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "ถอนการติดตั้งโมดูล"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "เดือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
msgid "More"
msgstr "เพิ่มเติม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Mute"
msgstr "ปิดเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Mute Conversation"
msgstr "ปิดเสียงการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute all conversations"
msgstr "ปิดเสียงการสนทนาทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute duration"
msgstr "ระยะเวลาปิดเสียง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
#: model:ir.model.fields,field_description:mail.field_res_users_settings__mute_until_dt
msgid "Mute notifications until"
msgstr "ปิดเสียงการแจ้งเตือนจนถึง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Muting prevents unread indicators and notifications from appearing."
msgstr "การปิดเสียงจะป้องกันไม่ให้การแจ้งเตือนที่ยังไม่ได้อ่านปรากฏขึ้น"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action_my
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "My Activities"
msgstr "กิจกรรมของฉัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "เทมเพลตของฉัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "My canned responses"
msgstr "คำตอบสำเร็จรูปของฉัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "ชื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "ต้องดำเนินการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "New"
msgstr "ใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Channel"
msgstr "ช่องทางใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Message"
msgstr "ข้อความใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__new_message_separator
msgid "New Message Separator"
msgstr "ตัวแยกข้อความใหม่"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "New Thread"
msgstr "เธรดใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "ค่าใหม่ของ Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "วันที่และเวลาของค่าใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "ค่าใหม่ของทศนิยม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "ค่าจำนวนเต็มใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "ค่าใหม่ของข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "New message"
msgstr "ส่งข้อความใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "New messages appear here."
msgstr "ข้อความใหม่จะปรากฎขึ้นบริเวณนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "ค่าใหม่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "กิจกรรมถัดไป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "กิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Next Monday Morning"
msgstr "เช้าวันจันทร์ถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "กิจกรรมต่อไปที่มี"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "No"
msgstr "ไม่"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "No Error"
msgstr "ไม่มีข้อผิดพลาด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "No Followers"
msgstr "ไม่มีผู้ติดตาม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.js:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "No IM status available"
msgstr "ไม่มีสถานะของ IM"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "ไม่มีบันทึก"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "ไม่มีกิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No channel found"
msgstr "ไม่พบช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "No conversation selected."
msgstr "ไม่ได้เลือกการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No conversation yet..."
msgstr "ยังไม่มีการสนทนา..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No history messages"
msgstr "ไม่มีประวัติข้อความ"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "No message_id found in context"
msgstr "ไม่พบ message_id ในบริบท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
msgid "No messages found"
msgstr "ไม่พบข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "No recipient"
msgstr "ไม่มีผู้รับ"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "No recipient found."
msgstr "ไม่พบผู้รับ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"ไม่ได้รับการตอบกลับ ตรวจสอบข้อมูลเซิร์ฟเวอร์\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""
"ไม่มีการระบุผู้รับผิดชอบสำหรับ %(activity_type_name)s: %(activity_summary)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "No results found"
msgstr "ไม่พบผลลัพธ์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "No saved templates"
msgstr "ไม่มีเทมเพลตที่บันทึกไว้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No starred messages"
msgstr "ไม่มีข้อความที่ติดดาว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No thread found."
msgstr "ไม่พบเธรด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.js:0
msgid "No thread named \"%(thread_name)s\""
msgstr "ไม่มีเธรดที่ชื่อ \"%(thread_name)s\""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "ไม่มีเธรดสำหรับคำตอบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No user found"
msgstr "ไม่พบผู้ใช้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "No user found that is not already a member of this channel."
msgstr "ไม่พบผู้ใช้ที่ไม่ได้เป็นสมาชิกของช่องนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "No users found"
msgstr "ไม่พบผู้ใช้"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
msgid "Non existing record or wrong token."
msgstr "ไม่มีบันทึกที่มีอยู่หรือโทเค็นไม่ถูกต้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "None"
msgstr "ไม่มี"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "ทำให้อีเมลเป็นมาตรฐาน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "ไม่ได้รับการยืนยัน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "ไม่ได้ทดสอบ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid "Not interested by this?"
msgstr "ไม่สนใจเรื่องนี้?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "โน้ต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__no_notif
msgid "Nothing"
msgstr "ไม่มี"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "การแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "อีเมลแจ้งเตือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Notification Item Image"
msgstr "รูปภาพรายการการแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "พารามิเตอร์การแจ้งเตือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Notification Settings"
msgstr "การตั้งค่าการแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "ประเภทการแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__notification_parameters
msgid "Notification parameters"
msgstr "พารามิเตอร์การแจ้งเตือน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "การแจ้งเตือนควรได้รับไฟล์แนบเป็นรายการหรืออันดับ (ได้รับ %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr "การแจ้งเตือนควรได้รับบันทึกไฟล์แนบเป็นรายการรหัส (%(aids)s ที่ได้รับ)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""
"การแจ้งเตือนควรได้รับพาร์ทเนอร์ที่ได้รับเป็นรายการรหัส (ได้รับ %(pids)s)"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "การแจ้งเตือน: ลบการแจ้งเตือนที่เก่ากว่า 6 เดือน"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Notify scheduled messages"
msgstr "การแจ้งเตือน: แจ้งข้อความตามกำหนดเวลา"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.actions.client,name:mail.discuss_notification_settings_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model:ir.ui.menu,name:mail.menu_notification_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "การแจ้งเตือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications allowed"
msgstr "อนุญาตการแจ้งเตือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications blocked"
msgstr "การแจ้งเตือนถูกบล็อก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Notify Recipients"
msgstr "แจ้งให้ผู้รับทราบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "Notify everyone"
msgstr "แจ้งผู้ใช้ทุกราย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_count
msgid ""
"Number of days/week/month before executing the action after or before the "
"scheduled plan date."
msgstr "จำนวนวัน/สัปดาห์/เดือนก่อนดำเนินการหลังจากหรือก่อนวันที่วางแผนไว้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"จำนวนวัน/สัปดาห์/เดือนก่อนดำเนินการ "
"ช่วยให้สามารถวางแผนกำหนดเวลาการดำเนินการได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will not send notifications on this device."
msgstr "Odoo จะไม่ส่งการแจ้งเตือนบนอุปกรณ์นี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will send notifications on this device!"
msgstr "Odoo จะส่งการแจ้งเตือนบนอุปกรณ์นี้!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "ปิด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Offline"
msgstr "ออฟไลน์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Offline - %(offline_count)s"
msgstr "ออฟไลน์ - %(offline_count)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "ค่าเก่าของ Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "ค่าเก่าของวันที่และเวลา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "ค่าเก่าของทศนิยม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "ค่าเก่าของจำนวนเต็ม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "ค่าเก่าของข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "ค่าเก่า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr "เมื่อติดดาวข้อความแล้ว คุณสามารถกลับมาตรวจสอบได้ตลอดเวลาที่นี่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.xml:0
msgid "Ongoing call"
msgstr "กำลังโทรอยู่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Online"
msgstr "ออนไลน์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Online - %(online_count)s"
msgstr "ออนไลน์ - %(online_count)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators are allowed to export mail message"
msgstr "เฉพาะผู้ดูแลระบบเท่านั้นที่ได้รับอนุญาตให้ส่งออกข้อความอีเมล"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators can modify 'model' and 'res_id' fields."
msgstr "เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถแก้ไขฟิลด์ 'model' และ 'res_id' ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Only custom models can be modified."
msgstr "เฉพาะโมเดลที่กำหนดเองเท่านั้นที่สามารถแก้ไขได้"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "เฉพาะผู้ใช้ภายในเท่านั้นที่สามารถรับการแจ้งเตือนใน Odoo ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Only members of %(group_name)s group are allowed to edit templates "
"containing sensible placeholders"
msgstr ""
"เฉพาะสมาชิกของกลุ่ม %(group_name) "
"เท่านั้นที่ได้รับอนุญาตให้แก้ไขเทมเพลตที่มีตัวแทนที่เหมาะสม"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Only messages type comment can have their content updated"
msgstr "เฉพาะข้อความประเภทความคิดเห็นเท่านั้นที่สามารถอัปเดตเนื้อหาได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""
"เฉพาะข้อความที่พิมพ์ความคิดเห็นเท่านั้นที่สามารถอัปเดตเนื้อหาในโมเดล "
"'discuss.channel'"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
msgid "Open"
msgstr "เปิด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
msgid "Open Actions Menu"
msgstr "เปิดเมนูการดำเนินการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
msgid "Open Channel"
msgstr "เปิดช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Open Discuss App"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "เปิดเอกสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Open Form View"
msgstr "เปิดมุมมองแบบฟอร์ม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Open Link"
msgstr "ลิงก์ที่เปิด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "เปิดเจ้าของ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Open card"
msgstr "เปิดการ์ด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
msgid "Open in Discuss"
msgstr "เปิดในแชท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_view.xml:0
msgid "Open preview in a separate window."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Operation not supported"
msgstr "ไม่รองรับการทำงาน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "ยกเลิกแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ไอดีทางเลือกของเธรด (บันทึก) ที่จะแนบข้อความขาเข้าทั้งหมด "
"แม้ว่าพวกเขาจะไม่ตอบกลับก็ตาม หากตั้งค่าไว้ "
"การดำเนินการนี้จะปิดใช้งานการสร้างการบันทึกใหม่ทั้งหมด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "ID mail_mail ทางเลือก ใช้เพื่อเพิ่มประสิทธิภาพการค้นหาเป็นหลัก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"เซิร์ฟเวอร์ที่ต้องการเพิ่มเติมสำหรับอีเมลขาออก หากไม่ได้ตั้งค่า "
"ระบบจะใช้ลำดับความสำคัญสูงสุด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"ตัวเลือกภาษาการแปล (โค้ด ISO) เพื่อเลือกเมื่อส่งอีเมล หากไม่ได้ตั้งค่าไว้ "
"ระบบจะใช้เวอร์ชันภาษาอังกฤษ โดยปกติควรเป็นตัวอย่างนิพจน์ที่ให้ภาษาที่เหมาะสม"
" เช่น {{ object.partner_id.lang }}"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Options"
msgstr "ตัวเลือก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"การสนทนาต้นฉบับ: คำตอบอยู่ในหัวข้อการสนทนาในเอกสารต้นฉบับ\n"
"ที่อยู่อีเมลอื่น: คำตอบไปที่ที่อยู่อีเมลที่กล่าวถึงในรหัสข้อความติดตาม แทนที่จะเป็นชุดการสนทนาในเอกสารต้นฉบับ\n"
"สิ่งนี้มีผลกระทบต่อรหัสข้อความที่สร้างขึ้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Original message was deleted"
msgstr "ข้อความต้นฉบับถูกลบแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Original note:"
msgstr "หมายเหตุต้นฉบับ:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
msgid "Other activities"
msgstr "กิจกรรมอื่นๆ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "ขาออก"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "อีเมลขาออก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "เมลเซิร์ฟเวอร์ขาออก"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "อีเมลขาออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "อีเมลล์ขาออก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Overdue"
msgstr "เกินกำหนด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "เขียนทับอีเมลผู้แต่ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Overwrite Template"
msgstr "เทมเพลตการเขียนทับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "เซิร์ฟเวอร์ POP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "เซิร์ฟเวอร์ POP/IMAP"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets received:"
msgstr "แพ็กเก็ตที่ได้รับ:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets sent:"
msgstr "แพ็คเก็ตที่ส่งแล้ว:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "หลัก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__parent_channel_id
msgid "Parent Channel"
msgstr "ช่องหลัก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "ข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "โมเดลหลัก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ไอดีเธรดการบันทึกหลัก"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__parent_channel_id
msgid "Parent channel"
msgstr "ช่องหลัก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"โมเดลหลักที่ถือนามแฝง "
"โมเดลที่มีการอ้างอิงนามแฝงไม่จำเป็นต้องเป็นโมเดลที่กำหนดโดย alias_model_id "
"(ตัวอย่าง: project (parent_model) and task (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"ประเภทย่อยหลัก ใช้สำหรับการสมัครสมาชิกอัตโนมัติ "
"ฟิลด์นี้ไม่ได้ตั้งชื่ออย่างถูกต้อง ตัวอย่างเช่น ในโปรเจ็กต์ parent_id "
"ของประเภทย่อยของโปรเจ็กต์อ้างอิงถึงประเภทย่อยที่เกี่ยวข้องกับงาน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "Participant avatar"
msgstr "รูปประจำตัวผู้เข้าร่วม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_push_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "Partner Profile"
msgstr "โปรไฟล์พาร์ทเนอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "พาร์ทเนอร์อ่านอย่างเดียว"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "ร่วมมือกับข้อมูลเพิ่มเติมสำหรับการส่งอีเมลอีกครั้ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "พาร์ทเนอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "คู่ค้าที่ต้องดำเนินการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "ใส่รหัส"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "วางคีย์ API ของคุณ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Pause"
msgstr "หยุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__payload
msgid "Payload"
msgstr "เพย์โหลด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "ลบเทมเพลตนี้อย่างถาวร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "สายเข้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Pick a specific time"
msgstr "เลือกเวลาที่ต้องการ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Pick an Activity Plan to launch"
msgstr "เลือกแผนกิจกรรมที่จะเปิดตัว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Pin"
msgstr "ปักหมุด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Pin It"
msgstr "ปักหมุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "ปักหมุดแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
msgid "Pinned Messages"
msgstr "ปักหมุดข้อความแล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "แผน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "มีแผนให้บริการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date
msgid "Plan Date"
msgstr "วันที่วางแผน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "ชื่อแผนการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_summary
msgid "Plan Summary"
msgstr "สรุปแผน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "สรุปแผน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
msgid "Planned"
msgstr "วางแผน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Planned Activities"
msgstr "กิจกรรมที่วางแผนไว้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "วางแผนไว้แล้วใน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "การวางแผน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Play"
msgstr "เล่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Please complete customer's information"
msgstr "กรุณากรอกข้อมูลของลูกค้าให้ครบถ้วน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "โปรดติดต่อเราแทนการใช้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Please wait while the file is uploading."
msgstr "กรุณารอสักครู่ในขณะที่ไฟล์กำลังอัปโหลด"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"นโยบายเกี่ยวกับวิธีจัดการกับการแจ้งเตือน Chatter:\n"
"- จัดการโดยอีเมล: การแจ้งเตือนจะถูกส่งไปยังที่อยู่อีเมลของคุณ\n"
"- จัดการใน Odoo: การแจ้งเตือนจะปรากฏในกล่องจดหมาย Odoo ของคุณ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"นโยบายการโพสต์ข้อความบนเอกสารโดยใช้เมลล์เกตเวย์\n"
"- ทุกคน: ทุกคนโพสต์ได้\n"
"- พาร์ทเนอร์: เฉพาะพาร์ทเนอร์ที่ได้รับการรับรองเท่านั้น\n"
"- ผู้ติดตาม: เฉพาะผู้ติดตามเอกสารที่เกี่ยวข้องหรือสมาชิกของช่องดังต่อไปนี้\n"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Pop out Attachments"
msgstr "เปิดไฟล์แนบออกมา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "พอร์ต"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Granted"
msgstr "ได้รับสิทธิ์การเข้าถึงพอร์ทัลแล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Revoked"
msgstr "เพิกถอนการเข้าถึงพอร์ทัลแล้ว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "โพสต์บนเอกสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Post your message on the thread"
msgstr "ฝากข้อความของคุณไว้ที่เธรด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"การโพสต์ข้อความควรทำในเอกสารทางธุรกิจ ใช้ message_notify "
"เพื่อส่งการแจ้งเตือนไปยังผู้ใช้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "การโพสต์ข้อความควรได้รับไฟล์แนบเป็นรายการหรืออันดับ (ได้รับ %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""
"การโพสต์ข้อความควรได้รับบันทึกไฟล์แนบเป็นรายการรหัส (%(aids)s ที่ได้รับ)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr "การโพสต์ข้อความควรได้รับพาร์ทเนอร์เป็นรายการ IDs (ได้รับ %(pids)s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "สนับสนุนโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "กิจกรรมก่อนหน้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "ที่อยู่ตอบกลับที่ต้องการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "Press Enter to start"
msgstr "กด Enter เพื่อเริ่มต้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr "กดปุ่มเพื่อลงทะเบียนเป็นทางลัดฟีเจอร์กดเพื่อพูด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr "ตัวอย่าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Preview my camera"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "ตัวอย่างของ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "ประเภทกิจกรรมก่อนหน้า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"ประมวลผลอีเมลขาเข้าแต่ละฉบับโดยเป็นส่วนหนึ่งของการสนทนาที่เกี่ยวข้องกับเอกสารประเภทนี้"
" สิ่งนี้จะสร้างเอกสารใหม่สำหรับการสนทนาใหม่ "
"หรือแนบอีเมลติดตามผลกับการสนทนาที่มีอยู่ (เอกสาร)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
msgid "Processing"
msgstr "กำลังดำเนินการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Public Channel"
msgstr "ช่องสาธารณะ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Public Channels"
msgstr "ช่องสาธารณะ"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "สัญญาการรับประกันของผู้เผยแพร่"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "สำนักพิมพ์: อัปเดตการแจ้งเตือน"

#. module: mail
#: model:ir.model,name:mail.model_mail_push_device
msgid "Push Notification Device"
msgstr "อุปกรณ์แจ้งเตือนแบบพุช"

#. module: mail
#: model:ir.model,name:mail.model_mail_push
msgid "Push Notifications"
msgstr "การแจ้งเตือนแบบพุช"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push to Talk"
msgstr "กดเพื่อพูด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Push to talk"
msgstr "กดเพื่อพูด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "ทางลัดฟีเจอร์กดเพื่อพูด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push-to-talk key"
msgstr "คีย์ฟีเจอร์กดเพื่อพูด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search"
msgstr "ค้นหาด่วน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search…"
msgstr "ค้นหาด่วน…"

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "QWeb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "เซสชัน RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "RTC Session ID:"
msgstr "รหัสเซสชัน RTC:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "เซสชัน RTC"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "เซสชัน RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Raise Hand"
msgstr "ยกมือ"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "ช่วงระหว่าง 0.0 ถึง 1.0 ขนาดขึ้นอยู่กับการใช้งานเบราว์เซอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "ลูกค้าที่กำลังโต้ตอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "พาร์ทเนอร์ที่ตอบโต้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "โต้ตอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "วันที่อ่าน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read Less"
msgstr "อ่านน้อยลง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read More"
msgstr "อ่านเพิ่มเติม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
msgid "Ready"
msgstr "พร้อม"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "พร้อมส่ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/bus_connection_alert.xml:0
msgid "Real-time connection lost..."
msgstr "การเชื่อมต่อแบบเรียลไทม์ขาดหาย..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "เหตุผล"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "รับการแจ้งเตือนใน Odoo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "ได้รับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Recent"
msgstr "ล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "ผู้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "ชื่อผู้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "ผู้รับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "กิจกรรมที่แนะนำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "ประเภทกิจกรรมที่แนะนำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "บันทึก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "ชื่อบันทึก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "บันทึกเธรด ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "อ้างอิง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Refuse"
msgstr "ปฏิเสธ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "ขอแสดงความนับถือ,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Register new key"
msgstr "ลงทะเบียนรหัสใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Reject"
msgstr "ปฏิเสธ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "บริษัท ที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "รหัสเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "IDs เอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__res_id
msgid "Related Document Id"
msgstr "รหัสเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "รูปแบบเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "ชื่อโมเดลเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "เทมเพลตจดหมายที่เกี่ยวข้อง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "ข้อความที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "พาร์ทเนอร์ที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "ฟิลด์ความสัมพันธ์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
msgid "Remove"
msgstr "นำออก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Remove Blur"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "ลบการดำเนินการตามบริบท"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "ลบผู้ติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Remove address from blacklist"
msgstr "ลบที่อยู่ออกจากบัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "ลบอีเมลออกจากโปรแกรมสร้างบัญชีแบล็คลิสต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "ลบการดำเนินการตามบริบทเพื่อใช้เทมเพลตนี้กับเอกสารที่เกี่ยวข้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Remove this follower"
msgstr "นำผู้ติดตามนี้ออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "ลบข้อมูลฟิลด์แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Rename Thread"
msgstr "เปลี่ยนชื่อเธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "โมเดลการแสดงผล"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""
"ไม่สามารถเรนเดอร์ %(field_name)s "
"ได้เนื่องจากไม่มีการแสดงที่เหมือนกันในเทมเพลต"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr "ไม่สามารถเรนเดอร์ %(field_name)s ได้เนื่องจากไม่ได้กำหนดไว้ในเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Repeat"
msgstr "ทำซ้ำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "ตอบกลับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Reply"
msgstr "ตอบกลับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "ตอบไปยัง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr "ตอบกลับที่อยู่อีเมล การตั้งค่า Reply_to จะข้ามการสร้างเธรดอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "ตอบกลับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "ที่อยู่ตอบกลับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Replying to"
msgstr "กำลังตอบกลับ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Report"
msgstr "รายงาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "พาร์ทเนอร์ที่ส่งคำขอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "ส่งซ้ำ"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "ส่งอีเมลอีกครั้ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "ส่งโปรแกรมสร้างอีกครั้ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "รีเซ็ตการยืนยัน"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "รีเซ็ตเทมเพลตเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "รีเซ็ตเทมเพลต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "การรีเซ็ตรหัสผ่านของคุณ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "จำกัดการแสดงผลเทมเพลต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "จำกัดรุ่นเทมเพลตอีเมลและการใช้ตัวยึดตำแหน่ง QWEB"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "ไฟล์แนบที่ถูกจำกัด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "ผลลัพธ์ของการตรวจจับภาษาตามเนื้อหา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "ลองใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Revert"
msgstr "เปลี่ยนกลับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "ตรวจสอบเทมเพลตทั้งหมด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "เนื้อหาข้อความแบบ Rich-text"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "ข้อความแบบ Rich-text/HTML"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "เซสชันเสียงเรียกเข้า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "เซสชัน Rtc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "URL เซิร์ฟเวอร์ SFU"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "รหัสเซิร์ฟเวอร์ SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "เซิร์ฟเวอร์ SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "เซิร์ฟเวอร์ SMTP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "พนักงานขาย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Save"
msgstr "บันทึก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Save as Template"
msgstr "บันทึกเป็นเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Save editing"
msgstr "บันทึกการแก้ไข"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "บันทึก GIF ที่ชื่นชอบจาก Tenor API"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "กำหนดเวลา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "กำหนดเวลาและทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_model.js:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity"
msgstr "กำหนดการกิจกรรม"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity On Selected Records"
msgstr "กำหนดการกิจกรรมในบันทึกที่เลือก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Message"
msgstr "ข้อความที่กำหนด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Note"
msgstr "โน๊ตที่กำหนด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule activities to help you get things done."
msgstr "กำหนดเวลากิจกรรมเพื่อช่วยให้คุณทำสิ่งต่างๆ ได้สำเร็จ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Schedule activity"
msgstr "สร้างกำหนดการ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Schedule an Activity"
msgstr "กำหนดเวลากิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity"
msgstr "กำหนดเวลากิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity on selected records"
msgstr "กำหนดเวลากิจกรรมในบันทึกที่เลือก"

#. module: mail
#: model:ir.model,name:mail.model_ir_cron
msgid "Scheduled Actions"
msgstr "การกระทำที่กำหนดไว้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "วันที่ตามกำหนดการ"

#. module: mail
#: model:ir.model,name:mail.model_mail_scheduled_message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Scheduled Message"
msgstr "ข้อความที่กำหนดไว้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "ข้อความที่กำหนด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "วันที่ส่งตามกำหนด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "สคริปต์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Search"
msgstr "ค้นหา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "ค้นหานามแฝง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "ค้นหากลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "ค้นหาเซิร์ฟเวอร์เมลขาเข้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
msgid "Search Message"
msgstr "ค้นหาข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Search Messages"
msgstr "ค้นหาข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Search More..."
msgstr "ค้นหาเพิ่มเติม..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "ค้นหาเซสชัน RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search Sub Channels"
msgstr "ค้นหาช่องย่อย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search button"
msgstr "ปุ่มค้นหา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search by name"
msgstr "ค้นหาตามชื่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search for a GIF"
msgstr "ค้นหา GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a channel..."
msgstr "ค้นหาช่อง..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a user..."
msgstr "ค้นหาผู้ใช้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search in progress"
msgstr "ค้นหาอยู่ระหว่างดำเนินการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search..."
msgstr "ค้นหา..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
msgid "Search: %s"
msgstr "ค้นหา: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Email Changed"
msgstr "การอัปเดตความปลอดภัย: อีเมลมีการเปลี่ยนแปลง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Login Changed"
msgstr "อัปเดตความปลอดภัย: เปลี่ยนการเข้าสู่ระบบแล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Password Changed"
msgstr "อัปเดตความปลอดภัย: เปลี่ยนรหัสผ่านแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "ดูรายละเอียดข้อผิดพลาด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "See all pinned messages."
msgstr "ดูข้อความที่ปักหมุดไว้ทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user)s"
msgstr "มองเห็นโดย %(user)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s and %(user2)s"
msgstr "มองเห็นโดย %(user1)s และ %(user2)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s and %(user3)s"
msgstr "มองเห็นโดย %(user1)s, %(user2)s และ %(user3)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"
msgstr "มองเห็นโดย %(user1)s, %(user2)s, %(user3)s และ %(count)s คนอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and 1 other"
msgstr "มองเห็นโดย %(user1)s, %(user2)s, %(user3)s และอีก 1 คน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by everyone"
msgstr "ทุกคนได้มองเห็น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.xml:0
msgid "Seen by:"
msgstr "มองเห็นโดย:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "เลือกภาษา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Select a user..."
msgstr "เลือกผู้ใช้..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Select duration"
msgstr "เลือกระยะเวลา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "เลือกตัวกรองเนื้อหาที่ใช้สำหรับการกรอง GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Send"
msgstr "ส่ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "ส่งและปิด"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "ส่งอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "ส่งอีเมลเป็น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Send Later"
msgstr "ส่งภายหลัง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Send Mail (%s)"
msgstr "ส่งอีเมล (%s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr "ส่งทันที"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Send a message to followers…"
msgstr "ส่งข้อความถึงผู้ติดตาม..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "ส่งและรับอีเมลผ่านบัญชี Gmail ของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "ส่งและรับอีเมลผ่านบัญชี Outlook ของคุณ"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "ส่งอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "ส่งจดหมายหรือการแจ้งเตือนโดยตรง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Send message"
msgstr "ส่งข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "ที่อยู่ผู้ส่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"ที่อยู่ผู้ส่ง (อาจใช้ตัวยึดตำแหน่งที่นี่) หากไม่ได้ตั้งค่า "
"ค่าเริ่มต้นจะเป็นอีเมลแทนของผู้เขียนหากกำหนดค่าไว้ หรือที่อยู่อีเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "การส่งล้มเหลว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "เซิร์ฟเวอร์ & เข้าสู่ระบบ"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "การดำเนินการเซิร์ฟเวอร์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "ข้อมูลเซิร์ฟเวอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "ชื่อเซิร์ฟเวอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "ลำดับความสำคัญของเซิร์ฟเวอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "ประเภทเซิร์ฟเวอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "ข้อมูลประเภทเซิร์ฟเวอร์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "Server error"
msgstr "เซิร์ฟเวอร์ผิดพลาด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"เซิร์ฟเวอร์ตอบกลับโดยมีข้อยกเว้นดังต่อไปนี้:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "ประเภทเซิร์ฟเวอร์ IMAP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "ประเภทเซิร์ฟเวอร์ POP"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "ตั้งค่าใช้งานเป็น False เพื่อซ่อนช่องโดยไม่ต้องลบออก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "การตั้งค่า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "Uuid ของช่อง Sfu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "URL เซิร์ฟเวอร์ Sfu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Share Screen"
msgstr "แชร์หน้าจอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Shared canned responses"
msgstr "การตอบกลับสำเร็จรูปที่ใช้ร่วมกัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Shared with all users."
msgstr "แชร์กับผู้ใช้ทุกคน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__source
msgid "Shortcut"
msgstr "ทางลัด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr "รหัสภาษาแบบย่อที่ใช้เป็นเป้าหมายสำหรับคำขอการแปล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Show Followers"
msgstr "แสดงผู้ติดตาม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Show a helper message"
msgstr "แสดงข้อความช่วยเหลือ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
msgid "Show activities"
msgstr "แสดงกิจกรรม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.xml:0
msgid "Show all recipients"
msgstr "แสดงผู้รับทั้งหมด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show less"
msgstr "แสดงน้อยลง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show more"
msgstr "แสดงเพิ่มเติม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Show sidebar"
msgstr "แสดงแถบด้านข้าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Show video participants only"
msgstr "แสดงเฉพาะผู้เข้าร่วมวิดีโอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Showing"
msgstr "กำลังแสดง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "การดำเนินการของแถบด้านข้าง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"การดำเนินการของแถบด้านข้างเพื่อทำให้เทมเพลตนี้พร้อมใช้งานในบันทึกของโมเดลเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "ชื่อไซต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Snooze 7d"
msgstr "เลื่อนการเตือน 7 วัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "เอ่ออ... ลองไปเลือก GIF เป็นรายการโปรดดูไหม?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "แหล่งที่มา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "ภาษาต้นฉบับ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "ผู้ใช้เฉพาะ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"ระบุแบบจำลองหากกิจกรรมควรเป็นแบบเฉพาะและไม่พร้อมใช้งานเมื่อจัดการกิจกรรมสำหรับโมเดลอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
msgid "Starred"
msgstr "ติดดาว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "ข้อความที่ติดดาว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Call"
msgstr "เริ่มการโทร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Start a Conversation"
msgstr "เริ่มการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Video Call"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a conversation"
msgstr "เริ่มการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_sidebar_patch.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a meeting"
msgstr "เริ่มการประชุม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "รัฐ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "สถานะ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "เวลาสถานะ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
msgid "Status with time"
msgstr "สถานะตามเวลา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Stay tuned! Enable push notifications to never miss a message."
msgstr "ไม่พลาดทุกการติดตาม! เปิดการแจ้งเตือนแบบพุชเพื่อไม่ให้พลาดข้อความใดๆ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "จำนวนก้าว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Stop Recording"
msgstr "หยุดการบันทึก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop Sharing Screen"
msgstr "หยุดการแชร์หน้าจอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop camera"
msgstr "หยุดการใช้งานกล้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Stop replying"
msgstr "หยุดการตอบกลับ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr "จัดเก็บอีเมลและการตอบกลับไว้ในแชทของแต่ละบันทึก"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "จัดเก็บข้อมูลตัวอย่างลิงก์"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"สตริงที่จัดรูปแบบเพื่อแสดงคีย์โดยมีตัวดัดแปลงตามรูปแบบนี้: "
"shift.ctrl.alt.key เช่น: truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "สตริงที่ได้รับจากคำขอการแปล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sub_channel_ids
msgid "Sub Channels"
msgstr "ช่องย่อย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "เรื่อง (อาจใช้ตัวยึดตำแหน่งที่นี่)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Subject:"
msgstr "เรื่อง:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "สมัครสมาชิกผู้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__substitution
msgid "Substitution"
msgstr "การทดแทน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "ประเภทย่อย"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "ชนิดย่อย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "ข้อแนะนำ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "แนะนำกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "แนะนำกิจกรรมเหล่านี้เมื่อกิจกรรมปัจจุบันถูกทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "สรุป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "สรุป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "รองรับ Authentication ของ Gmail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "รองรับ Authentication ของ Outlook"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "พารามิเตอร์ของระบบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "การแจ้งเตือนระบบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Tab to select"
msgstr "แท็บเพื่อเลือก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "ภาษาเป้าหมาย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "โมเดลเป้าหมาย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "เลขประจำตัวผู้เสียภาษี"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Technical Settings"
msgstr "การตั้งค่าทางเทคนิค"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"ข้อมูลทางเทคนิคเพื่อติดตามโมเดลตั้งแต่เริ่มต้นการแก้ไขเพื่อรองรับพฤติกรรมที่เกี่ยวข้องกับ"
" UX"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "เทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "หมวดหมู่เทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template Description"
msgstr "คำอธิบายเทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "ชื่อไฟล์เทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_name
msgid "Template Name"
msgstr "ชื่อแม่แบบ"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "ตัวอย่างแม่แบบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "ภาษาตัวอย่างเทมเพลต"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "รีเซ็ตเทมเพลต Mixin"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Template creation from composer requires a valid model."
msgstr "การสร้างเทมเพลตจากผู้แต่งต้องใช้โมเดลที่ถูกต้อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering should only be called with a list of IDs. Received "
"“%(res_ids)s” instead."
msgstr ""
"การเรนเดอร์เทมเพลตควรเรียกใช้ด้วยรายการ ID เท่านั้น แต่จะได้รับ "
"“%(res_ids)s” แทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"การแสดงเทมเพลตรองรับเฉพาะ inline_template, qweb หรือ qweb_view (ดู หรือ RAW)"
" ได้รับ %(engine)s แทน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "เทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "คีย์ API ของ Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "คีย์ API ของ Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "ตัวกรองเนื้อหา Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "ข้อจำกัดของ Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "ข้อจำกัดของ Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "ตัวกรองเนื้อหา Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "ทดสอบและยืนยัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "บันทึกการทดสอบ:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "เนื้อหาข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr " "

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "The 'Due Date In' value can't be negative."
msgstr "ค่า 'วันครบกำหนดใน' ไม่สามารถเป็นค่าลบได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
msgid ""
"The 'To-Do' activity type is used to create reminders from the top bar menu "
"and the command palette. Consequently, it cannot be archived or deleted."
msgstr ""
"ประเภทกิจกรรม 'สิ่งที่ต้องทำ' "
"ใช้เพื่อสร้างการแจ้งเตือนจากเมนูแถบด้านบนและแผงคำสั่ง "
"ดังนั้นจึงไม่สามารถเก็บถาวรหรือลบได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
msgid "The Fullscreen mode was denied by the browser"
msgstr "โหมดเต็มหน้าจอถูกปฏิเสธโดยเบราว์เซอร์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_extension_service.js:0
msgid ""
"The Push-to-Talk feature is only accessible within tab focus. To enable the "
"Push-to-Talk functionality outside of this tab, we recommend downloading our"
" %(anchor_start)sextension%(anchor_end)s."
msgstr ""
"ฟีเจอร์ Push-to-Talk สามารถเข้าถึงได้ภายในโฟกัสของแท็บเท่านั้น "
"หากต้องการเปิดใช้งานฟังก์ชัน Push-to-Talk ภายนอกแท็บนี้ "
"เราขอแนะนำให้ดาวน์โหลด %(anchor_start)sextension%(anchor_end)s ของเรา"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"หมายเลขประจำตัวผู้เสียภาษี ค่าที่นี่จะได้รับการตรวจสอบตามรูปแบบประเทศ "
"คุณสามารถใช้ '/' เพื่อระบุว่าพาร์ทเนอร์ไม่ต้องเสียภาษี"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The activity cannot be launched:"
msgstr "ไม่สามารถเริ่มกิจกรรมได้:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"ประเภทกิจกรรม \"%(activity_type_name)s\" เข้ากันไม่ได้กับแผน "
"\"%(plan_name)s\" เนื่องจากถูกจำกัดไว้ที่โมเดล \"%(activity_type_model)s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "ไม่มีเอกสารแนบ %s หรือคุณไม่มีสิทธิ์ในการเข้าถึง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "The attachment %s does not exist."
msgstr "ไม่มีเอกสารแนบ %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.js:0
msgid "The avatar has been updated!"
msgstr "อวาตาร์ได้รับการอัปเดตแล้ว!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "UUID ของช่องจะต้องไม่ซ้ำกัน"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "ประเภทช่องต้องไม่เว้นว่าง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "The conversation is empty."
msgstr "การสนทนานั้นว่างเปล่า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "ผู้ใช้ปัจจุบันสามารถแก้ไขเทมเพลตได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "The duration of voice messages is limited to 1 minute."
msgstr "ระยะเวลาของข้อความเสียงจำกัดอยู่ที่ 1 นาที"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "อีเมลที่ส่งไปที่"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
msgid "The email template(s) have been restored to their original settings."
msgstr "เทมเพลตอีเมลได้รับการคืนค่าเป็นการตั้งค่าเดิมแล้ว"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_push_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "ปลายทางต้องไม่ซ้ำกัน!"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"ไม่สามารถรีเซ็ตเทมเพลตอีเมลต่อไปนี้ได้เนื่องจากไม่พบไฟล์ต้นฉบับที่เกี่ยวข้อง:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "ผู้ติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr "ข้อความล่าสุดที่ได้รับในนามแฝงนี้ทำให้เกิดข้อผิดพลาด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "ไม่สามารถยอมรับที่อยู่ข้อความด้านล่างได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"ไม่สามารถยอมรับที่อยู่ข้อความด้านล่างได้ %(alias_display_name)s\n"
"                 อนุญาตให้เฉพาะ %(contact_description)s ติดต่อได้<br /><br />\n"
"                 กรุณาตรวจสอบให้แน่ใจว่าคุณใช้ที่อยู่ที่ถูกต้อง หรือติดต่อเราที่ %(default_email)s แทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"ไม่สามารถยอมรับที่อยู่ %(alias_display_name)s ข้อความด้านล่างได้\n"
"โปรดลองอีกครั้งในภายหลังหรือติดต่อ %(company_name)s แทน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"The message scheduled on %(model)s(%(id)s) with the following content could "
"not be sent:%(original_message)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__from_message_id
msgid "The message the channel was created from."
msgstr "ข้อความที่ช่องถูกสร้างขึ้นมา"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"โมเดล (ชนิดเอกสาร Odoo) ซึ่งสอดคล้องกับนามแฝงนี้ อีเมลขาเข้าใดๆ "
"ที่ไม่ตอบกลับบันทึกที่มีอยู่จะทำให้เกิดการสร้างบันทึกใหม่ของโมเดลนี้ (เช่น "
"โปรเจ็กต์งาน)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"ชื่อของนามแฝงอีเมล เช่น 'งาน' หากคุณต้องการรับอีเมลสำหรับ "
"<<EMAIL>>"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "ไม่สามารถเปิดใช้แผน \"%(plan_name)s\" ได้:"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "แผน \"%(plan_name)s\" ได้เริ่มต้นแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "ผู้จัดการคิวจะส่งอีเมลหลังวันที่"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The records must belong to the same company."
msgstr "บันทึกต้องเป็นของบริษัทเดียวกัน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_message.py:0
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %(type)s, Operation: %(operation)s)\n"
"\n"
"Records: %(records)s, User: %(user)s"
msgstr ""
"ไม่สามารถดำเนินการตามที่ร้องขอได้เนื่องจากข้อจำกัดด้านความปลอดภัย โปรดติดต่อผู้ดูแลระบบของคุณ\n"
"\n"
"(ประเภทเอกสาร: %(type)s, การดำเนินการ: %(operation)s)\n"
"\n"
"บันทึก: %(records)s, ผู้ใช้: %(user)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "ไม่สามารถใช้เซิร์ฟเวอร์ \"%s\" ได้เนื่องจากถูกเก็บถาวรแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "The subscription preferences were successfully applied."
msgstr "ใช้การตั้งค่าการสมัครสมาชิกเรียบร้อยแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "เทมเพลตเป็นของผู้ใช้รายนี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr "ส่วนต้นของข้อความเท่านั้นที่ใช้เป็นตัวอย่างอีเมล"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "สมาชิกช่องสามารถมีได้เพียงเซสชัน rtc หนึ่งเซสชันเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "นี้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action can only be done on a mail thread models"
msgstr "การดำเนินการนี้สามารถทำได้บนโมเดลเธรดของจดหมายเท่านั้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action cannot be done on transient models."
msgstr "การดำเนินการนี้ไม่สามารถทำได้ในโมเดลชั่วคราว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "This action will send an email."
msgstr "การดำเนินการนี้จะส่งอีเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This channel doesn't have any attachments."
msgstr "ช่องนี้ไม่มีไฟล์แนบใดๆ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This channel doesn't have any pinned messages."
msgstr "ช่องนี้ไม่มีข้อความที่ปักหมุด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "This channel has no thread yet."
msgstr "ช่องนี้ยังไม่มีเธรดเลย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This conversation doesn't have any attachments."
msgstr "การสนทนานี้ไม่มีไฟล์แนบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This conversation doesn't have any pinned messages."
msgstr "การสนทนานี้ไม่มีข้อความที่ปักหมุดไว้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"อีเมลนี้ถูกขึ้นบัญชีดำสำหรับการส่งจดหมายจำนวนมาก คลิกเพื่อยกเลิกบัญชีดำ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "ฟิลด์นี้ไม่คำนึงถึงตัวพิมพ์เล็กและตัวพิมพ์ใหญ่"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr "ฟิลด์นี้ใช้สำหรับคำอธิบายภายในของการใช้งานเทมเพลต"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"ฟิลด์นี้ใช้เพื่อค้นหาที่อยู่อีเมล เนื่องจากฟิลด์อีเมลหลักสามารถใส่สิ่งอื่น ๆ"
" มากกว่าที่อยู่อีเมลที่เคร่งครัด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "This layout seems to no longer exist."
msgstr "ดูเหมือนว่าไม่มีเค้าโครงนี้อยู่แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message_model.js:0
msgid "This message has already been sent."
msgstr "ข้อความนี้ถูกส่งแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"ตัวเลือกนี้จะลบการติดตามอีเมลหลังจากถูกส่งอย่างถาวร "
"รวมถึงเมนูทางเทคนิคในการตั้งค่า เพื่อรักษาพื้นที่จัดเก็บข้อมูลของฐานข้อมูล "
"Odoo ของคุณ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
msgid "This record has an exception activity."
msgstr "บันทึกนี้มีกิจกรรมยกเว้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid ""
"This setting will be applied to all channels using the default notification "
"settings."
msgstr "การตั้งค่านี้จะใช้กับทุกช่องทางที่ใช้การตั้งค่าการแจ้งเตือนเริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__channel_notifications
msgid ""
"This setting will only be applied to channels. Mentions only if not "
"specified."
msgstr "การตั้งค่านี้จะใช้กับช่องเท่านั้น กล่าวถึงเฉพาะในกรณีที่ไม่ได้ระบุ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr "ค่าเหล่านั้นไม่รองรับเป็นตัวเลือกเมื่อแสดงผล: %(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr "ไม่รองรับค่าเหล่านั้นเมื่อโพสต์หรือแจ้งเตือน: %(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "เธรด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread Image"
msgstr "รูปภาพเธรด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread has unread messages"
msgstr "เธรดมีข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Thread image"
msgstr "รูปเธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "เปิดใช้งานเธรด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/thread_actions.js:0
msgid "Threads"
msgstr "เธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_tz
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "โซนเวลา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "To"
msgstr "ถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "ถึง (อีเมล)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "ถึง (คู่ค้า)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
msgid "To :"
msgstr "ถึง:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "To peer:"
msgstr "ถึงเพื่อน:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "To-Do"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "To:"
msgstr "ถึง:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Today"
msgstr "วันนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Today at %(time)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Today:"
msgstr "วันนี้:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Tomorrow"
msgstr "พรุ่งนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Afternoon"
msgstr "พรุ่งนี้ช่วงบ่าย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Morning"
msgstr "พรุ่งนี้ช่วงเช้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Tomorrow:"
msgstr "พรุ่งนี้:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "หัวข้อสนทนาในกลุ่มนี้..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "ติดตามผู้รับ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"ค่าที่ติดตามจะถูกจัดเก็บไว้ในโมเดลที่แยกจากกัน "
"ฟิลด์นี้อนุญาตให้สร้างการติดตามใหม่และสร้างสถิติเกี่ยวกับโมเดลได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "การติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "ค่าการติดตาม"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "ค่าการติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "ค่าที่ติดตาม"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Translate"
msgstr "แปล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "เนื้อหาการแปล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Translation Failure"
msgstr "การแปลล้มเหลว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_from
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "เปิดใช้งาน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "ทริกเกอร์กิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "ลองอีกครั้ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Turn camera on"
msgstr "เปิดกล้อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Turn on notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "โทเค็นการตรวจสอบบัญชี Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "SID บัญชี Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "ประเภท"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "ประเภทของความล่าช้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"ประเภทของการทำงานของเซิร์ฟเวอร์ ค่าต่อไปนี้มีอยู่:\n"
"- 'อัปเดตบันทึก': อัปเดตค่าของบันทึก\n"
"- 'สร้างกิจกรรม': สร้างกิจกรรม (แชท)\n"
"- 'ส่งอีเมล': โพสต์ข้อความ บันทึก หรือส่งอีเมล (สนทนา)\n"
"- 'ส่ง SMS': ส่ง SMS, บันทึกไว้ในเอกสาร (SMS)- 'เพิ่ม/ลบผู้ติดตาม': เพิ่มหรือลบผู้ติดตามในบันทึก (สนทนา)\n"
"- 'สร้างบันทึก': สร้างบันทึกใหม่ด้วยค่าใหม่\n"
"- 'การรันโค้ด': บล็อกของโค้ด Python ที่จะถูกดำเนินการ\n"
"- 'ส่งการแจ้งเตือน Webhook': ส่งคำขอ POST ไปยังระบบภายนอกหรือที่เรียกว่า Webhook\n"
"- 'ดำเนินการคำสั่งที่มีอยู่': กำหนดคำสั่งที่ทำให้เกิดการกระทำของเซิร์ฟเวอร์อื่นๆ\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Type the name of a person"
msgstr "พิมพ์ชื่อของบุคคล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid "Unable to connect to SMTP Server"
msgstr "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ SMTP ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "ไม่สามารถโพสต์ข้อความ โปรดกำหนดค่าที่อยู่อีเมลของผู้ส่ง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Unable to send message, please configure the sender's email address."
msgstr "ไม่สามารถส่งข้อความได้ โปรดกำหนดค่าที่อยู่อีเมลของผู้ส่ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign"
msgstr "ไม่ได้มอบหมาย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign from me"
msgstr "ยกเลิกการมอบหมายจากฉัน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "ยกเลิกการแบล็คลิสต์"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr "เหตุผลในการเลิกบล็อก: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Undeafen"
msgstr "ไม่หูหนวก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Unfollow"
msgstr "เลิกติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Unit"
msgstr "หน่วย"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "หน่วยความล่าช้า"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
msgid "Unknown"
msgstr "ไม่ทราบ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
msgid "Unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Unknown error: %(error)s"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก: %(error)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Unmute"
msgstr "ยกเลิกการปิดเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Unmute Conversation"
msgstr "ปิดเสียงการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Unpin"
msgstr "ยกเลิกการปักหมุด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Conversation"
msgstr "เลิกปักหมุดการสนทนา"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Unpin Message"
msgstr "เลิกปักหมุดข้อความ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Thread"
msgstr "ยกเลิกการปักหมุดเธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__unpin_dt
msgid "Unpin date"
msgstr "เลิกปักหมุดวันที่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "ไม่จำกัดไฟล์แนบ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Unselect person"
msgstr "ยกเลิกการเลือกบุคคล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Unstar all"
msgstr "ลบดาวทั้งหมด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Unsupported report type %s found."
msgstr "พบประเภทรายงาน %s ที่ไม่รองรับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until %s"
msgstr "ถึง %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until I turn it back on"
msgstr "จนกว่าฉันจะเปิดมันอีกครั้ง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Untitled"
msgstr "ไม่มีชื่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "อัปเดตเค้าโครงการส่งเมล"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Update Template"
msgstr "อัปเดตเทมเพลต"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Upload Avatar"
msgstr "อัปโหลดรูปประจำตัว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "อัปโหลดเอกสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload File"
msgstr "อัปโหลดไฟล์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload file"
msgstr "อัปโหลดไฟล์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Upload:"
msgstr "การอัปโหลด:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploaded"
msgstr "อัปโหลด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploading"
msgstr "กำลังอัปโหลด"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"ใช้ 'ผู้ใช้เฉพาะ' เพื่อกำหนดผู้ใช้คนเดิมในกิจกรรมถัดไปเสมอ ใช้ "
"'ผู้ใช้แบบไดนามิก' เพื่อระบุชื่อฟิลด์ของผู้ใช้ที่จะเลือกในบันทึก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "ใช้เซิร์ฟเวอร์อีเมลแบบกำหนดเอง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Use Default"
msgstr "ใช้ค่าเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "ใช้เซิร์ฟเวอร์ Twilio ICE"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "ใช้เซิร์ฟเวอร์ Gmail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Use a local script to fetch your emails and create new records."
msgstr "ใช้สคริปต์ในตัวเครื่องเพื่อดึงอีเมลของคุณและสร้างบันทึกใหม่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "ใช้เซิร์ฟเวอร์ Outlook"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid ""
"Use default from user settings if not specified. This setting will only be "
"applied to channels."
msgstr ""
"ใช้ค่าเริ่มต้นจากการตั้งค่าผู้ใช้หากไม่ได้ระบุ "
"การตั้งค่านี้จะใช้กับช่องเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "ใช้โดเมนอื่นสำหรับนามแฝงอีเมลของคุณ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "ใช้เป็นชุด"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Use message_notify to send a notification to an user."
msgstr "ใช้ message_notify เพื่อส่งการแจ้งเตือนไปยังผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "ใช้เทมเพลต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "ใช้ฟีเจอร์กดเพื่อพูด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "ใช้ใน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "ใช้เป็นบริบทที่ใช้ในการประเมินโดเมนของผู้แต่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"ใช้เพื่อจัดหมวดหมู่ตัวสร้างข้อความ\n"
"'อีเมล': สร้างโดยอีเมลขาเข้า เช่น mailgateway\n"
"'ความคิดเห็น': สร้างโดยการป้อนข้อมูลของผู้ใช้ เช่น ผ่านการพูดคุยหรือการแต่ง\n"
"'อีเมลขาออก': สร้างโดยการส่งจดหมาย\n"
"'การแจ้งเตือน': สร้างโดยระบบ เช่น ติดตามข้อความ\n"
"'ความคิดเห็นอัตโนมัติ': สร้างโดยกลไกการแจ้งเตือนอัตโนมัติ เช่น การรับทราบ\n"
"'การแจ้งเตือนผู้ใช้': สร้างขึ้นสำหรับผู้รับที่เฉพาะเจาะจง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "ใช้เพื่อแสดงสกุลเงินเมื่อติดตามมูลค่าทางการเงิน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "ใช้ในการเรียงลำดับประเภทย่อย"

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "ผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "ฟิลด์ผู้ใช้"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "การแสดงตนของผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "การตั้งค่าผู้ใช้"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "การตั้งค่าผู้ใช้"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "ระดับเสียงการตั้งค่าของผู้ใช้"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "การแจ้งเตือนเฉพาะผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "ผู้ใช้งาน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is a bot"
msgstr "ผู้ใช้เป็นบอท"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is idle"
msgstr "ผู้ใช้ไม่อยู่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is offline"
msgstr "ผู้ใช้ออฟไลน์อยู่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is online"
msgstr "ผู้ใช้กำลังออนไลน์"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "ผู้ใช้ไม่ควรมี GIF รายการโปรดที่ซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "ชื่อผู้ใช้"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"ผู้ใช้จะยังสามารถแสดงเทมเพลตได้\n"
"อย่างไรก็ตาม มีเพียงผู้แก้ไขเทมเพลตเมลเท่านั้นที่สามารถสร้างเทมเพลตไดนามิกใหม่หรือแก้ไขเทมเพลตที่มีอยู่ได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"จำเป็นต้องใช้เซิร์ฟเวอร์อีเมลของคุณเองเพื่อส่ง/รับอีเมลในเวอร์ชันคอมมูนิตี้และองค์กร"
" ผู้ใช้ออนไลน์ได้รับประโยชน์จากเซิร์ฟเวอร์อีเมลที่พร้อมใช้งานแล้ว "
"(@mycompany.odoo.com)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "ถูกต้อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"ไม่สามารถตรวจสอบค่า %(allowed_domains)s สำหรับ `mail.catchall.domain.allowed` ได้\n"
"ควรเป็นรายการโดเมนที่คั่นด้วยเครื่องหมายจุลภาค เช่น example.com และ example.org"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Video Settings"
msgstr "การตั้งค่าวิดีโอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Video player:"
msgstr "เครื่องเล่นวิดีโอ:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
msgid "View"
msgstr "ดู"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "View %s"
msgstr "ดู %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "View Profile"
msgstr "ดูโปรไฟล์"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "View Reactions"
msgstr "ดูการตอบโต้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
msgid "View Thread"
msgstr "ดูเธรด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "ประเภทมุมมอง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "View all activities"
msgstr "ดูกิจกรรมทั้งหมด"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "View or join channels"
msgstr "ดูหรือเข้าร่วมช่อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "เสียง"

#. module: mail
#: model:ir.ui.menu,name:mail.menu_call_settings
msgid "Voice & Video"
msgstr "เสียงและวิดีโอ"

#. module: mail
#: model:ir.actions.client,name:mail.discuss_call_settings_action
msgid "Voice & Video Settings"
msgstr "การตั้งค่าเสียงและวิดีโอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice Detection"
msgstr "การตรวจจับเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Voice Message"
msgstr "ข้อความเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice detection threshold"
msgstr "เกณฑ์การตรวจจับด้วยเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "Voice recording stopped"
msgstr "หยุดการบันทึกเสียงแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice settings"
msgstr "การตั้งค่าเสียง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "ปริมาตร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "ปริมาณต่อพาร์ทเนอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "ปริมาณของพาร์ทเนอร์รายอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""
"ต้องการเติมสีสันให้การสนทนาของคุณด้วย GIF หรือไม่? "
"เปิดใช้งานฟีเจอร์ในการตั้งค่า!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "Warning"
msgstr "คำเตือน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"เราไม่สามารถสร้างนามแฝง %(alias_name)s ได้ เนื่องจากโดเมน "
"%(alias_domain_name)s เป็นของบริษัท %(alias_company_names)s "
"ในขณะที่เอกสารเจ้าของเป็นของบริษัท %(company_name)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"เราไม่สามารถสร้างนามแฝง %(alias_name)s ได้ เนื่องจากโดเมน "
"%(alias_domain_name)s เป็นของบริษัท %(alias_company_names)s "
"ในขณะที่เอกสารเป้าหมายเป็นของบริษัท %(company_name)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "We were not able to fetch value of field '%(field)s'"
msgstr "เราไม่สามารถดึงค่าของฟิลด์ '%(field)s'"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "สัปดาห์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "ยินดีต้อนรับสู่ MyCompany!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""
"ไม่มีอะไรคงอยู่ตลอดไป แต่คุณแน่ใจหรือไม่ว่าต้องการเลิกปักหมุดข้อความนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "What's your name?"
msgstr "คุณชื่ออะไร?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__user_tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""
"เมื่อพิมพ์เอกสารและส่งออก/นำเข้าข้อมูล ค่าเวลาจะถูกคำนวณตามเขตเวลานี้\n"
"หากไม่ได้ตั้งค่าเขตเวลา ระบบจะใช้ UTC (เวลาสากลเชิงพิกัด)\n"
"ที่อื่น ค่าเวลาจะถูกคำนวณตามการชดเชยเวลาของเว็บไคลเอ็นต์ของคุณ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr "เมื่อเลือกการมอบหมาย \"ผู้ใช้เริ่มต้น\" คุณต้องระบุผู้รับผิดชอบ"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"ไม่ว่าจะเก็บสำเนาต้นฉบับแบบฉบับเต็มของอีเมลแต่ละฉบับเพื่อใช้อ้างอิงและแนบไปกับข้อความที่ประมวลผลแต่ละฉบับหรือไม่"
" โดยปกติแล้วจะเพิ่มขนาดฐานข้อมูลข้อความของคุณเป็นสองเท่า"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"ไม่ว่าจะดาวน์โหลดไฟล์แนบหรือไม่ หากไม่ได้เปิดใช้งาน "
"อีเมลขาเข้าจะถูกลบออกจากไฟล์แนบก่อนที่จะดำเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "ไม่ว่าจะแสดงผู้รับทั้งหมดหรือเฉพาะผู้รับที่สำคัญเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Write /field to insert dynamic content"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Write Feedback"
msgstr "เขียนข้อเสนอแนะ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Write your message here..."
msgstr "เขียนข้อความของคุณที่นี่..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Wrong operation name (%s)"
msgstr "ชื่อการดำเนินการไม่ถูกต้อง (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yeah, pin it!"
msgstr "ปักหมุดไว้!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Yes"
msgstr "ใช่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yes, remove it please"
msgstr "ใช่ กรุณาลบออกด้วย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Yesterday"
msgstr "เมื่อวาน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Yesterday at %(time)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Yesterday:"
msgstr "เมื่อวาน:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"คุณกำลังจะออกจากการสนทนากลุ่มนี้ และจะไม่สามารถเข้าถึงได้อีกต่อไป "
"เว้นแต่คุณจะได้รับเชิญอีกครั้ง คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "คุณอยู่ในช่อง %(bold_start)s#%(channel_name)s%(bold_end)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "คุณไม่ได้ติดตาม \"%(thread_name)s\" อีกต่อไป"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"You are not allowed to change the target record of a scheduled message."
msgstr "คุณไม่มีสิทธิ์เปลี่ยนแปลงบันทึกเป้าหมายของข้อความที่กำหนดเวลาไว้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "You are not allowed to send this scheduled message"
msgstr "คุณไม่ได้รับอนุญาตให้ส่งข้อความตามกำหนดเวลานี้"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload an attachment here."
msgstr "คุณไม่ได้รับอนุญาตให้อัปโหลดไฟล์แนบที่นี่"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr "คุณไม่ได้รับอนุญาตให้อัปโหลดไฟล์แนบในช่องนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "คุณเป็นผู้ดูแลช่องนี้ คุณแน่ใจหรือไม่ว่าต้องการออก?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"คุณสามารถทำเครื่องหมายข้อความว่า 'ติดดาว' ได้ "
"และข้อความนั้นจะแสดงในกล่องจดหมายนี้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "You can not write on %(field_name)s."
msgstr "คุณไม่สามารถเขียนใน %(field_name)s ได้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with existing users."
msgstr "คุณสามารถสนทนากับผู้ใช้ที่มีอยู่เท่านั้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with partners that have a dedicated user."
msgstr "คุณสามารถแชทได้เฉพาะกับพาร์ทเนอร์ที่มีผู้ใช้เฉพาะเท่านั้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "คุณสามารถเพิกเฉยต่อข้อความนี้ได้อย่างปลอดภัย"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"คุณไม่สามารถลบกลุ่ม \"ทั้งบริษัท\" ได้ เนื่องจากมีการเรียกใช้งานจากโมดูลอื่น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""
"คุณไม่สามารถใช้สิ่งอื่นใดนอกจากตัวอักษรละตินที่ไม่มีสำเนียงในที่อยู่นามแฝง "
"%(alias_name)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""
"คุณไม่สามารถใช้สิ่งอื่นใดนอกจากตัวอักษรละตินที่ไม่มีสำเนียงในชื่อโดเมน "
"%(domain_name)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "คุณไม่มีสิทธิ์เข้าถึง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"คุณไม่มีสิทธิ์เข้าถึงอีเมลที่ไม่อยู่ในบัญชีแบล็คลิสต์ "
"โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "You have been assigned to %s"
msgstr "คุณได้รับมอบหมายสำหรับ %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "คุณได้รับมอบหมายสำหรับ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js:0
msgid "You have been invited to #%s"
msgstr "คุณได้รับเชิญให้เข้าร่วม #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"คุณสามารถแนบไฟล์เข้ากับเทมเพลตนี้เพื่อเพิ่มลงในอีเมลทั้งหมดที่สร้างจากเทมเพลตนี้ได้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "You may not define a template on an abstract model: %s"
msgstr "คุณไม่สามารถกำหนดเทมเพลตบนโมเดลนามธรรมได้: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""
"คุณแน่ใจหรือว่าต้องการให้ข้อความนี้ปักหมุดไว้ที่ %(conversation)s ตลอดไป?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned %(conversation_name)s"
msgstr "คุณได้ยกเลิกการปักหมุด %(conversation_name)s แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned your conversation with %(user_name)s"
msgstr "คุณได้ยกเลิกการปักหมุดการสนทนาของคุณกับ %(user_name)s แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unsubscribed from %s."
msgstr "คุณยกเลิกการสมัครจาก %s แล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a chat!"
msgstr "คุณได้รับเชิญให้เข้าร่วมการสนทนา!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a meeting!"
msgstr "คุณได้รับเชิญให้เข้าร่วมการประชุม!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "You:"
msgstr "คุณ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr " ของคุณ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr "อีเมลบัญชีของคุณถูกเปลี่ยนจาก %(old_email)s เป็น %(new_email)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account login has been updated"
msgstr "ข้อมูลเข้าสู่ระบบบัญชีของคุณได้รับการอัปเดตแล้ว"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account password has been updated"
msgstr "รหัสผ่านบัญชีของคุณได้รับการอัปเดตแล้ว"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your browser does not support videoconference"
msgstr "เบราว์เซอร์ของคุณไม่รองรับการประชุมทางวิดีโอ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support voice activation"
msgstr "เบราว์เซอร์ของคุณไม่รองรับการเปิดใช้งานด้วยเสียง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support webRTC."
msgstr "เบราว์เซอร์ของคุณไม่รองรับ webRTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Your inbox is empty"
msgstr "กล่องจดหมายของคุณว่างเปล่า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your name"
msgstr "ชื่อของคุณ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (ไม่มีที่อยู่อีเมล)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "addresses linked to registered partners"
msgstr "ที่อยู่ที่เชื่อมโยงกับพาร์ทเนอร์ที่ลงทะเบียน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "หลังจากวันที่เสร็จสิ้น"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "หลังจากหมดเขตกิจกรรมครั้งก่อน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "afternoon"
msgstr "ช่วงบ่าย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "alias"
msgstr "นามแฝง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "alias %(name)s: %(error)s"
msgstr "นามแฝง %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "ไฟล์แนบของอีเมลนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "available bitrate:"
msgstr "บิตเรตที่ใช้ได้:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "back"
msgstr "กลับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"เพราะคุณได้\n"
"                ติดต่อหลายครั้งเกินไปในช่วงไม่กี่นาทีที่ผ่านมา\n"
"                <br/>\n"
"                กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "โดย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "camera"
msgstr "กล้อง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"ไม่สามารถประมวลผลได้ ที่อยู่นี้\n"
"    ใช้เพื่อรวบรวมคำตอบและไม่ควรใช้เพื่อติดต่อโดยตรง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "channels"
msgstr "ช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "clock rate:"
msgstr "อัตราความเร็วนาฬิกา:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "codec:"
msgstr "ตัวแปลงสัญญาณ:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "created this channel."
msgstr "สร้างช่องนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "custom"
msgstr "กำหนดเอง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__days
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "วัน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days overdue:"
msgstr "วันที่ค้างชำระ:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days:"
msgstr "วัน:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "deaf"
msgstr "หูหนวก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "default"
msgstr "ค่าเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__mail_push_device_id
msgid "devices"
msgstr "อุปกรณ์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "document"
msgstr "เอกสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "เสร็จสิ้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down DTLS:"
msgstr "ดาวน์โหลด DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down ICE:"
msgstr "ดาวน์โหลด ICE:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr "เช่น \"ขอให้รับจดหมายข่าวฉบับถัดไป\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "เช่น \"พูดคุยเกี่ยวกับข้อเสนอ\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "เช่น \"อ่านข้อเสนอและพูดคุยเกี่ยวกับรายละเอียด\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "เช่น “อีเมลยินดีต้อนรับ”"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""
"เช่น \"ยินดีต้อนรับสู่ MyCompany\" หรือ \"ยินดีที่ได้รู้จัก {{ object.name "
"}}\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "เช่น \"ตีกลับ\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "เช่น \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "เช่น \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "เช่น \"การแจ้งเตือน\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "เช่น 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "เช่น ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "เช่น รายชื่อติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Discuss Proposal"
msgstr "เช่น พูดคุยเกี่ยวกับข้อเสนอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "เช่น พูดคุยเกี่ยวกับข้อเสนอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
msgid "e.g. Log a note"
msgstr "เช่น เข้าสู่ระบบบันทึก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "เช่น การเริ่มงาน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "เช่น ตารางการประชุม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "เช่น mycompany.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "เช่น การรองรับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "เช่น true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "เช่น user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "เช่น: \"<EMAIL>\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g: Send order confirmation"
msgstr "เช่น: ส่งคำยืนยันการสั่งซื้อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "for"
msgstr "สำหรับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "ได้รับการสร้างขึ้นจาก:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "ได้รับการแก้ไขจาก:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "เพิ่งมอบหมายให้คุณทำกิจกรรมดังต่อไปนี้:"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "in a few seconds"
msgstr "ในเวลาไม่กี่วินาที"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias"
msgstr "นามแฝงที่กำหนดค่าไม่ถูกต้อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias (unknown reference record)"
msgstr "นามแฝงที่กำหนดค่าไว้ไม่ถูกต้อง (บันทึกอ้างอิงที่ไม่รู้จัก)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "invited %s to the channel"
msgstr "เชิญ %s เข้าสู่ช่อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "joined the channel"
msgstr "เข้าร่วมช่อง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "left the channel"
msgstr "ออกจากช่อง"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list"
msgstr "รายการ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list-item"
msgstr "รายการสินค้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "live"
msgstr "ไลฟ์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "media player Error"
msgstr "ข้อผิดพลาดของเครื่องเล่นมีเดีย"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "microphone"
msgstr "ไมโครโฟน"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "model %s does not accept document creation"
msgstr "โมเดล %s ไม่ยอมรับการสร้างเอกสาร"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "monday"
msgstr "วันจันทร์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__months
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "เดือน"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "morning"
msgstr "ช่วงเช้า"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "muted"
msgstr "ปิดเสียง"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "new"
msgstr "ใหม่"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "no connection"
msgstr "ไม่มีการเชื่อมต่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "now"
msgstr "ตอนนี้"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "on"
msgstr "เมื่อ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "on:"
msgstr "เมื่อ:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
msgid "or"
msgstr "หรือ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "or press %(send_keybind)s"
msgstr "หรือกด %(send_keybind)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "other members."
msgstr "สมาชิกคนอื่น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
msgid "props.action.title"
msgstr "props.action.title"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "raising hand"
msgstr "ยกมือ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"ตอบกลับเอกสารที่หายไป (%(model)s,%(thread)s) ถอยกลับไปที่การสร้างเอกสาร"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"ตอบกลับโมเดล %s ที่ไม่ยอมรับการอัปเดตเอกสาร ถอยกลับไปที่การสร้างเอกสาร"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to followers"
msgstr "จำกัดไว้เฉพาะผู้ติดตามเท่านั้น"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to known authors"
msgstr "จำกัดเฉพาะผู้เขียนที่รู้จักเท่านั้น"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "results out of"
msgstr "ผลลัพธ์จาก"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "screen"
msgstr "หน้าจอ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "some specific addresses"
msgstr "ที่อยู่เฉพาะบางแห่ง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "สตัน:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "target model unspecified"
msgstr "ไม่ระบุโมเดลเป้าหมาย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "ทีม"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "template"
msgstr "เทมเพลต"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "เปลี่ยน:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown target model %s"
msgstr "ไม่ทราบโมเดลเป้าหมาย %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up DTLS:"
msgstr "อัปโหลด DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up ICE:"
msgstr "อัปโหลด ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "users"
msgstr "ผู้ใช้"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "view"
msgstr "ดู"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "การจัดการข้อความของ websocket"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__weeks
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "สัปดาห์"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "your alias"
msgstr "นามแฝงของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "“%(member_name)s” ใน “%(channel_name)s”"
