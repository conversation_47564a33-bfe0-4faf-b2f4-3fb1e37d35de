# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercado_pago
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "1494126963"
msgstr "1494126963"

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "APP_USR-..."
msgstr "APP_USR-..."

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Do not have access to fetch token from Mercado Pago"
msgstr "Mercado Pagoからトークンを取得するアクセス権がありません。"

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart
msgid ""
"Enter your Point Smart terminal serial number written on the back of your "
"terminal (after the S/N:)"
msgstr "端末の裏面に記載されている Point Smart 端末のシリアル番号(S/N: の後)を入力します。"

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "Force PDV"
msgstr "強制PDV"

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_bearer_token
msgid ""
"Mercado Pago customer production user token: "
"https://www.mercadopago.com.mx/developers/en/reference"
msgstr ""
"Mercado Pago顧客本番ユーザトークン: "
"https://www.mercadopago.com.mx/developers/en/reference"

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_webhook_secret_key
msgid ""
"Mercado Pago production secret key from integration application: "
"https://www.mercadopago.com.mx/developers/panel/app"
msgstr ""
"Mercado Pago 統合アプリケーションからの本番環境シークレットキー: "
"https://www.mercadopago.com.mx/developers/panel/app"

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart_complet
msgid "Mp Id Point Smart Complet"
msgstr "Mp Id Point Smart Complet"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been canceled"
msgstr "支払が取消されました"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been processed"
msgstr "支払が処理されました"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been rejected"
msgstr "支払が拒否されました"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has to be canceled on terminal"
msgstr "支払は端末上で取消される必要があります"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment not found (canceled/finished on terminal)"
msgstr "支払が見つかりません (取消済/端末上で完了済)"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment status could not be confirmed"
msgstr "支払ステータスが確認できませんでした"

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Please verify your production user token as it was rejected"
msgstr "本番ユーザートークンが拒否されましたので、確認して下さい。"

#. module: pos_mercado_pago
#: model:ir.model,name:pos_mercado_pago.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS支払方法"

#. module: pos_mercado_pago
#: model:ir.model,name:pos_mercado_pago.model_pos_session
msgid "Point of Sale Session"
msgstr "POSセッション"

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_webhook_secret_key
msgid "Production secret key"
msgstr "本番シークレットキー"

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_bearer_token
msgid "Production user token"
msgstr "本番ユーザトークン"

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart
msgid "Terminal S/N"
msgstr "端末 S/N"

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "The terminal serial number is not registered on Mercado Pago"
msgstr "端末のシリアル番号がMercado Pagoに登録されていません"

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Unexpected Mercado Pago response: %s"
msgstr "Mercado Pagoの予想外の応答: %s"

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Unknown payment status"
msgstr "不明な支払ステータス"

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "c2f3662..."
msgstr "c2f3662..."
