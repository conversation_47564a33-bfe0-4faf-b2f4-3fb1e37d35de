<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="crm_lead_view_tree_opportunity_reporting" model="ir.ui.view">
            <field name="name">crm.lead.list.opportunity.reporting</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='%(crm.action_lead_mail_compose)d']" position="replace"/>
                <xpath expr="//button[@name='action_snooze']" position="replace"/>
            </field>
        </record>

        <!-- Opportunities by user and team pivot View -->
        <record id="crm_opportunity_report_view_pivot" model="ir.ui.view">
            <field name="name">crm.opportunity.report.pivot</field>
            <field name="model">crm.lead</field>
            <field name="priority">60</field>
            <field name="arch" type="xml">
                <pivot string="Pipeline Analysis" sample="1">
                    <field name="create_date" interval="month" type="col"/>
                    <field name="stage_id" type="row"/>
                    <field name="prorated_revenue" type="measure"/>
                    <field name="color" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </pivot>
            </field>
        </record>

        <record id="crm_opportunity_report_view_pivot_lead" model="ir.ui.view">
            <field name="name">crm.opportunity.report.view.pivot.lead</field>
            <field name="model">crm.lead</field>
            <field name="priority">60</field>
            <field name="arch" type="xml">
                <pivot string="Leads Analysis" sample="1">
                    <field name="create_date" interval="month" type="row"/>
                    <field name="team_id" type="col"/>
                    <field name="color" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </pivot>
            </field>
        </record>

        <!-- Opportunities by user and team Graph View -->
        <record id="crm_opportunity_report_view_graph" model="ir.ui.view">
            <field name="name">crm.opportunity.report.graph</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <graph string="Pipeline Analysis" sample="1">
                    <field name="stage_id"/>
                    <field name="date_deadline" interval="month"/>
                    <field name="prorated_revenue" type="measure"/>
                    <field name="color" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </graph>
            </field>
        </record>

        <record id="crm_opportunity_report_view_graph_lead" model="ir.ui.view">
            <field name="name">crm.opportunity.report.graph.lead</field>
            <field name="model">crm.lead</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <graph string="Leads Analysis" sample="1">
                    <field name="create_date" interval="month"/>
                    <field name="team_id"/>
                    <field name="color" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </graph>
            </field>
        </record>

        <!-- Opportunities by user and team Search View -->
        <record id="crm_opportunity_report_view_search" model="ir.ui.view">
            <field name="name">crm.lead.search</field>
            <field name="model">crm.lead</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <search string="Opportunities Analysis">
                    <filter string="My Pipeline" name="my"
                            domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter name="filter_opportunity" string="Opportunities" domain="[('type','=','opportunity')]" help="Show only opportunities" groups="crm.group_use_lead"/>
                    <filter name="filter_lead" string="Leads" domain="[('type','=', 'lead')]" help="Show only leads" groups="crm.group_use_lead"/>
                    <separator/>
                    <filter string="Active" name="filter_active"
                            domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="filter_inactive"
                            domain="[('active', '=', False)]"/>
                    <filter string="Won" name="won"
                            domain="[('probability', '=', 100)]"/>
                    <filter string="Lost" name="lost"
                            domain="[('probability', '=', 0), ('active', '=', False)]"/>
                    <field name="team_id" context="{'invisible_team': False}"/>
                    <field name="user_id" string="Salesperson"/>
                    <separator/>
                    <filter name="filter_create_date" date="create_date" default_period="year"/>
                    <filter string="Expected Closing" name="filter_date_deadline" date="date_deadline"/>
                    <filter string="Date Closed" name="date_closed_filter" date="date_closed"/>
                    <group expand="0" string="Extended Filters">
                        <field name="partner_id" filter_domain="[('partner_id','child_of',self)]"/>
                        <field name="stage_id" domain="['|', ('team_id', '=', False), ('team_id', '=', 'team_id')]"/>
                        <field name="campaign_id"/>
                        <field name="medium_id"/>
                        <field name="source_id"/>
                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                        <newline/>
                        <field name="create_date"/>
                        <field name="date_open"/>
                        <field name="date_closed"/>
                    </group>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="1" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'user_id'}" />
                        <filter string="Sales Team" name="saleschannel" context="{'group_by':'team_id'}"/>
                        <filter string="City" name="city" context="{'group_by':'city'}" />
                        <filter string="Country" name="country" context="{'group_by':'country_id'}" />
                        <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Stage" name="stage" context="{'group_by':'stage_id'}"/>
                        <filter string="Campaign" name="compaign" domain="[]" context="{'group_by':'campaign_id'}"/>
                        <filter string="Medium" name="medium" domain="[]" context="{'group_by':'medium_id'}"/>
                        <filter string="Source" name="source" domain="[]" context="{'group_by':'source_id'}"/>
                        <separator orientation="vertical" />
                        <filter string="Creation Date" context="{'group_by':'create_date:month'}" name="month"/>
                        <filter string="Conversion Date" context="{'group_by':'date_conversion:month'}" name="conversion_date" help="Conversion Date from Lead to Opportunity" groups="crm.group_use_lead"/>
                        <filter string="Expected Closing" context="{'group_by':'date_deadline:month'}" name="date_deadline"/>
                        <filter string="Closed Date" context="{'group_by':'date_closed'}" name="date_closed_groupby"/>
                        <filter string="Lost Reason" name="lostreason" context="{'group_by':'lost_reason_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="crm_lead_view_tree_reporting" model="ir.ui.view">
            <field name="name">crm.lead.list.lead.reporting</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_case_tree_view_leads"/>
            <field name="mode">primary</field>
            <field name="priority">24</field>
            <field name="arch" type="xml">
                <xpath expr="field[@name='city']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="field[@name='probability']" position="before">
                    <field name="type" groups="crm.group_use_lead" optional="show"/>
                    <field name="stage_id" optional="show"/>
                </xpath>
                <xpath expr="field[@name='probability']" position="after">
                    <field name="lost_reason_id" optional="hide"/>
                </xpath>
            </field>
        </record>

        <record id="crm_opportunity_report_action" model="ir.actions.act_window">
            <field name="name">Pipeline Analysis</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">graph,pivot,list,form</field>
            <field name="search_view_id" ref="crm.crm_opportunity_report_view_search"/>
            <field name="context">{'search_default_filter_opportunity': True, 'search_default_current': True}</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'graph', 'view_id': ref('crm_opportunity_report_view_graph')}),
                          (0, 0, {'view_mode': 'pivot', 'view_id': ref('crm_opportunity_report_view_pivot')}),
                          (0, 0, {'view_mode': 'list', 'view_id': ref('crm_lead_view_tree_opportunity_reporting')})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data found!
                </p><p>
                    Use this menu to have an overview of your Pipeline.
                </p>
            </field>
        </record>

        <record id="crm_opportunity_report_action_lead" model="ir.actions.act_window">
            <field name="name">Leads Analysis</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">graph,pivot,list</field>
            <field name="search_view_id" ref="crm.crm_opportunity_report_view_search"/>
            <field name="context">{
                'search_default_filter_active': 1,
                'search_default_filter_inactive': 1,
                'search_default_filter_create_date': 1,
            }</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'graph', 'view_id': ref('crm_opportunity_report_view_graph_lead')}),
                          (0, 0, {'view_mode': 'pivot', 'view_id': ref('crm_opportunity_report_view_pivot_lead')}),
                          (0, 0, {'view_mode': 'form', 'view_id': ref('crm_lead_view_form')}),
                          (0, 0, {'view_mode': 'list', 'view_id': ref('crm_lead_view_tree_reporting')}),
                         ]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data found!
                </p><p>
                    This analysis shows you how many leads have been created per month.
                </p>
            </field>
        </record>

</odoo>
