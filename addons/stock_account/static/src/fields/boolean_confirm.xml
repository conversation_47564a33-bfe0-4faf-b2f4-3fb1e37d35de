<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="stock_account.BooleanToggleConfirm">
        <ConfirmCheckBox
            className="'o_field_boolean o_boolean_toggle'"
            id="props.id"
            value="props.record.data[props.name] or false"
            disabled="isReadonly"
            onChange="(value) => this.onChange(value)"
        />
    </t>
</templates>
