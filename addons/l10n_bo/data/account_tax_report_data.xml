<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.bo"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_bo_tax_report_code" model="account.report.column">
                <field name="name">Code</field>
                <field name="expression_label">code</field>
                <field name="figure_type">string</field>
            </record>
            <record id="l10n_bo_tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
                <field name="figure_type">integer</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_bo_tax_report_iva" model="account.report.line">
                <field name="name">Value Added Tax (IVA) - Form 200 v.5</field>
                <field name="code">l10n_bo_tax_report_iva</field>
                <field name="hierarchy_level" eval="0"/>
                <!-- Based on Form 200 v5 - see https://www.impuestos.gob.bo/page/349 -->
                <field name="children_ids">
                    <record id="l10n_bo_tax_report_iva_debit" model="account.report.line">
                        <field name="name">Determination of the Fiscal Debit</field>
                        <field name="code">l10n_bo_tax_report_iva_debit</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_iva_13" model="account.report.line">
                                <field name="name">Sales of taxed goods and/or services in the domestic market, excepting sales taxed at Zero Rate</field>
                                <field name="code">l10n_bo_tax_report_iva_13</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_13_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">13</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_13_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 13</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_14" model="account.report.line">
                                <field name="name">Export of goods and exempt operations</field>
                                <field name="code">l10n_bo_tax_report_iva_14</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_14_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">14</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_14_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 14</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_15" model="account.report.line">
                                <field name="name">Sales taxed at Zero Rate</field>
                                <field name="code">l10n_bo_tax_report_iva_15</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_15_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">15</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_15_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 15</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_505" model="account.report.line">
                                <field name="name">Untaxed sales and operations not subject to IVA</field>
                                <field name="code">l10n_bo_tax_report_iva_505</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_505_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">505</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_505_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 505</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_16" model="account.report.line">
                                <field name="name">Value attributed to retired goods and/or services and private consumption</field>
                                <field name="code">l10n_bo_tax_report_iva_16</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_16_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">16</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_16_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 16</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_17" model="account.report.line">
                                <field name="name">Returns and rescinders performed during the period</field>
                                <field name="code">l10n_bo_tax_report_iva_17</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_17_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">17</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_17_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 17</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_18" model="account.report.line">
                                <field name="name">Discounts, bonuses and rebates obtained during the period</field>
                                <field name="code">l10n_bo_tax_report_iva_18</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_18_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">18</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_18_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 18</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_39" model="account.report.line">
                                <field name="name">Tax Payable corresponding to: (C13+C16+C17+C18) * 13%</field>
                                <field name="code">l10n_bo_tax_report_iva_39</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_39_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">39</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_39_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">0.13 * (l10n_bo_tax_report_iva_13.balance + l10n_bo_tax_report_iva_16.balance + l10n_bo_tax_report_iva_17.balance + l10n_bo_tax_report_iva_18.balance)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_55" model="account.report.line">
                                <field name="name">Adjusted Tax Payable based on refunds</field>
                                <field name="code">l10n_bo_tax_report_iva_debit_donations</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_55_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">55</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_55_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 55</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_19" model="account.report.line">
                                <field name="name">Adjusted Tax Payable based on Reconciliations</field>
                                <field name="code">l10n_bo_tax_report_iva_19</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_19_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">19</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_19_tax_amount_balance" model="account.report.expression">
                                        <field name="label">tax_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 19</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_19_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_19.tax_amount - l10n_bo_tax_report_iva_39.balance</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_1002" model="account.report.line">
                                <field name="name">Total Tax Payable of the period (C39+C55+C19)</field>
                                <field name="code">l10n_bo_tax_report_iva_1002</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_1002_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">1002</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_1002_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_39.balance + l10n_bo_tax_report_iva_debit_donations.balance + l10n_bo_tax_report_iva_19.balance</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_iva_credit" model="account.report.line">
                        <field name="name">Determination of the Tax Credit</field>
                        <field name="code">l10n_bo_tax_report_iva_credit</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_iva_11" model="account.report.line">
                                <field name="name">Total purchases corresponding to taxed and/or non-taxed activities</field>
                                <field name="code">l10n_bo_tax_report_iva_11</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_11_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">11</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_11_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 11</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_26" model="account.report.line">
                                <field name="name">Purchases corresponding to taxed activities</field>
                                <field name="code">l10n_bo_tax_report_iva_26</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_26_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">26</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_26_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 26</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_31" model="account.report.line">
                                <field name="name">Purchases for which it is not possible to discriminate between taxed and non-taxed activities</field>
                                <field name="code">l10n_bo_tax_report_iva_31</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_31_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">31</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_31_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 31</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_27" model="account.report.line">
                                <field name="name">Returns and rescinders received during the period</field>
                                <field name="code">l10n_bo_tax_report_iva_27</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_27_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">27</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_27_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 27</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_28" model="account.report.line">
                                <field name="name">Discounts, bonuses and rebates granted during the period</field>
                                <field name="code">l10n_bo_tax_report_iva_28</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_28_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">28</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_28_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 28</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_114" model="account.report.line">
                                <field name="name">Tax Credit: (C26+C27+C28) * 13%</field>
                                <field name="code">l10n_bo_tax_report_iva_114</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_114_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">114</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_114_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">0.13 * (l10n_bo_tax_report_iva_26.balance + l10n_bo_tax_report_iva_27.balance + l10n_bo_tax_report_iva_28.balance)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_30" model="account.report.line">
                                <field name="name">Adjusted Tax Credit based on Reconciliations</field>
                                <field name="code">l10n_bo_tax_report_iva_30</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_30_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">30</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_30_tax_amount_balance" model="account.report.expression">
                                        <field name="label">tax_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IVA 30</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_30_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_30.tax_amount - l10n_bo_tax_report_iva_114.balance</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_1003" model="account.report.line">
                                <field name="name">Proportional tax credit corresponding to taxed activities C31*(C13+C14)/(C13+C14+C15+C505) * 13%</field>
                                <field name="code">l10n_bo_tax_report_iva_1003</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_1003_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">1003</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_1003_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">0.13 * l10n_bo_tax_report_iva_31.balance * (l10n_bo_tax_report_iva_13.balance + l10n_bo_tax_report_iva_14.balance) / (l10n_bo_tax_report_iva_13.balance + l10n_bo_tax_report_iva_14.balance + l10n_bo_tax_report_iva_15.balance + l10n_bo_tax_report_iva_505.balance)</field>
                                        <field name="subformula">ignore_zero_division</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_1004" model="account.report.line">
                                <field name="name">Total Fiscal Credit for the period (C114+C30+C1003)</field>
                                <field name="code">l10n_bo_tax_report_iva_1004</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_1004_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">1004</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_1004_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_114.balance + l10n_bo_tax_report_iva_30.balance + l10n_bo_tax_report_iva_1003.balance</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_iva_difference" model="account.report.line">
                        <field name="name">Determination of the difference</field>
                        <field name="code">l10n_bo_tax_report_iva_difference</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_iva_693" model="account.report.line">
                                <field name="name">Difference in favor of the Taxpayer (C1004-C1002; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_693</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_693_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">693</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_693_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_1004.balance - l10n_bo_tax_report_iva_1002.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_909" model="account.report.line">
                                <field name="name">Difference in favor of the Tax Authority or determined Tax Amount (C1002-C1004; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_909</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_909_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">909</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_909_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_1002.balance - l10n_bo_tax_report_iva_1004.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_635" model="account.report.line">
                                <field name="name">Tax Credit balance to offset for the previous period (C592 of the Form for the previous period)</field>
                                <field name="code">l10n_bo_tax_report_iva_635</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_635_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">635</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_635_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_648" model="account.report.line">
                                <field name="name">Value Adjustment on the Tax Credit balance for the previous period</field>
                                <field name="code">l10n_bo_tax_report_iva_648</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_648_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">648</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_648_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_1001" model="account.report.line">
                                <field name="name">Tax Balance Determined in favor of the Tax Authority (C909-C635-C648; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_1001</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_1001_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">1001</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_1001_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_909.balance - l10n_bo_tax_report_iva_635.balance - l10n_bo_tax_report_iva_648.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <!-- Note: this tax relief provision ended in December 2021, so is no longer in use.-->
                            <record id="l10n_bo_tax_report_iva_621" model="account.report.line">
                                <field name="name">Payment to account of 50% of paid employer contributions</field>
                                <field name="code">l10n_bo_tax_report_iva_621</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_621_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">621</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_621_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_629" model="account.report.line">
                                <field name="name">Balance in favour of the Tax Authority after offsetting payments on account for employer's contributions (C1001 -C621; If > 0) ((C621 - C1001; If > 0) = 0)</field>
                                <field name="code">l10n_bo_tax_report_iva_629</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_629_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">629</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_629_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_1001.balance - l10n_bo_tax_report_iva_621.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_622" model="account.report.line">
                                <field name="name">Payments to Account already made in Declarations and/or Payment Tickets corresponding to the declared period</field>
                                <field name="code">l10n_bo_tax_report_iva_622</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_622_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">622</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_622_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_640" model="account.report.line">
                                <field name="name">Balance of Payments on Account to offset from the previous period (C747 of the Form for the previous period)</field>
                                <field name="code">l10n_bo_tax_report_iva_640</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_640_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">640</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_640_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_643" model="account.report.line">
                                <field name="name">Balance for Payments on Account in favor of the Taxpayer (C640+C622-C629; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_643</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_643_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">643</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_643_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_640.balance + l10n_bo_tax_report_iva_622.balance - l10n_bo_tax_report_iva_629.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_468" model="account.report.line">
                                <field name="name">Balance in favor of the Tax Authority after offsetting payments on account (C629-C622-C640; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_468</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_468_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">468</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_468_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_629.balance - l10n_bo_tax_report_iva_622.balance - l10n_bo_tax_report_iva_640.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <!-- Note: this fiscal regime ended in December 2021, so is no longer in use.-->
                            <record id="l10n_bo_tax_report_iva_465" model="account.report.line">
                                <field name="name">5% payment for purchases to SIETE-RG taxpayers</field>
                                <field name="code">l10n_bo_tax_report_iva_465</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_465_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">465</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_465_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_466" model="account.report.line">
                                <field name="name">Balance of Payments on Account for 5% of purchases to SIETE-RG taxpayers (C469 of the previous period)</field>
                                <field name="code">l10n_bo_tax_report_iva_466</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_466_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">466</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_466_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_467" model="account.report.line">
                                <field name="name">Balance in favor after offsetting 5% of payments on account for purchases from SIETE-RG taxpayers (C465+C466-C468; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_467</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_467_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">467</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_467_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_465.balance + l10n_bo_tax_report_iva_466.balance - l10n_bo_tax_report_iva_468.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_996" model="account.report.line">
                                <field name="name">Balance in favor of the Tax Authority (C468-C465-C466; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_996</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_996_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">996</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_996_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_468.balance - l10n_bo_tax_report_iva_465.balance - l10n_bo_tax_report_iva_466.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_iva_payable" model="account.report.line">
                        <field name="name">Determination of Tax Payable</field>
                        <field name="code">l10n_bo_tax_report_iva_payable</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_iva_924" model="account.report.line">
                                <field name="name">Unpaid Tax (C996)</field>
                                <field name="code">l10n_bo_tax_report_iva_924</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_924_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">924</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_924_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_996.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_925" model="account.report.line">
                                <field name="name">Value Adjustment on Unpaid Tax</field>
                                <field name="code">l10n_bo_tax_report_iva_925</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_925_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">925</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_925_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_938" model="account.report.line">
                                <field name="name">Interest on Adjusted Unpaid Tax</field>
                                <field name="code">l10n_bo_tax_report_iva_938</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_938_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">938</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_938_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_954" model="account.report.line">
                                <field name="name">Fine for Non-Compliance to Formal Duty (IDF) for overdue declaration</field>
                                <field name="code">l10n_bo_tax_report_iva_954</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_954_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">954</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_954_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_967" model="account.report.line">
                                <field name="name">Fine levied by the IDF for late declaration of a correction in the Tax Amount</field>
                                <field name="code">l10n_bo_tax_report_iva_967</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_967_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">967</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_967_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_955" model="account.report.line">
                                <field name="name">Total Tax Payable (C924+C925+C938+C954+C967)</field>
                                <field name="code">l10n_bo_tax_report_iva_955</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_955_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">955</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_955_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_924.balance + l10n_bo_tax_report_iva_925.balance + l10n_bo_tax_report_iva_938.balance + l10n_bo_tax_report_iva_954.balance + l10n_bo_tax_report_iva_967.balance</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_iva_balance" model="account.report.line">
                        <field name="name">Definitive balance</field>
                        <field name="code">l10n_bo_tax_report_iva_balance</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_iva_592" model="account.report.line">
                                <field name="name">Final Tax Credit Balance in favor of the Taxpayer for the next period (C693+C635+C648-C909; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_592</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_592_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">592</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_592_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_693.balance + l10n_bo_tax_report_iva_635.balance + l10n_bo_tax_report_iva_648.balance - l10n_bo_tax_report_iva_909.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_469" model="account.report.line">
                                <field name="name">Final Balance for Payments on Account in favor of the taxpayer of the 5% for purchases to SIETE-RG contributors for the next period (C467; Yes )</field>
                                <field name="code">l10n_bo_tax_report_iva_469</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_469_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">469</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_469_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_467.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_747" model="account.report.line">
                                <field name="name">Final Balance for Payments on Account in favor of the Taxpayer for the next period (C643-C955; If>0)</field>
                                <field name="code">l10n_bo_tax_report_iva_747</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_747_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">747</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_747_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_643.balance - l10n_bo_tax_report_iva_955.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_iva_646" model="account.report.line">
                                <field name="name">Final balance in favour of the Tax Authority (C996 or (C955-C643) as appropriate; If >0)</field>
                                <field name="code">l10n_bo_tax_report_iva_646</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_iva_646_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">646</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_iva_646_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_iva_955.balance - l10n_bo_tax_report_iva_643.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_bo_tax_report_it" model="account.report.line">
                <field name="name">Transaction Tax (IT) - Form 400 v.5</field>
                <field name="code">l10n_bo_tax_report_it</field>
                <field name="hierarchy_level" eval="0"/>
                <!-- Based on Form 400 v5 - see https://www.impuestos.gob.bo/page/349 -->
                <field name="children_ids">
                    <record id="l10n_bo_tax_report_it_base" model="account.report.line">
                        <field name="name">Determination of the Base Amount and the Tax Amount</field>
                        <field name="code">l10n_bo_tax_report_it_base</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_it_13" model="account.report.line">
                                <field name="name">Total gross income earned and/or received in kind (including exempt income)</field>
                                <field name="code">l10n_bo_tax_report_it_13</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_13_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">13</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_13_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_32.balance + l10n_bo_tax_report_it_24.balance</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_32" model="account.report.line">
                                <field name="name">Exempt income</field>
                                <field name="code">l10n_bo_tax_report_it_32</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_32_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">32</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_32_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IT 32</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_24" model="account.report.line">
                                <field name="name">Taxable base (C13 - C32)</field>
                                <field name="code">l10n_bo_tax_report_it_24</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_24_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">24</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_24_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">IT 24</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_909" model="account.report.line">
                                <field name="name">Determined tax (C24 * 3%)</field>
                                <field name="code">l10n_bo_tax_report_it_909</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_909_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">909</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_909_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_24.balance * 0.03</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_it_balance" model="account.report.line">
                        <field name="name">Determination of the Balance</field>
                        <field name="code">l10n_bo_tax_report_it_balance</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_it_664" model="account.report.line">
                                <field name="name">Paid IUE to offset (1st Instance or “Balance of IUE to offset” of Form 400, C619 of the previous period)</field>
                                <field name="code">l10n_bo_tax_report_it_664</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_664_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">664</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_664_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_1001" model="account.report.line">
                                <field name="name">Tax Balance Determined in favor of the Tax Authority (C909 - C664; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_1001</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_1001_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">1001</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_1001_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_909.balance - l10n_bo_tax_report_it_664.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_622" model="account.report.line">
                                <field name="name">Payments to Account already made in Declarations and/or Payment Tickets corresponding to the declared period</field>
                                <field name="code">l10n_bo_tax_report_it_622</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_622_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">622</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_622_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_640" model="account.report.line">
                                <field name="name">Balance of Payments on Account to offset from the previous period (C747 of Form 400 of the previous period)</field>
                                <field name="code">l10n_bo_tax_report_it_640</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_640_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">640</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_640_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_643" model="account.report.line">
                                <field name="name">Balance for Payments on Account in favor of the Taxpayer (C622+C640-C1001; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_643</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_643_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">643</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_643_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_622.balance + l10n_bo_tax_report_it_640.balance - l10n_bo_tax_report_it_1001.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_996" model="account.report.line">
                                <field name="name">Balance in favor of the Tax Authority (C1001-C622-C640; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_996</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_996_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">996</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_996_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_1001.balance - l10n_bo_tax_report_it_622.balance - l10n_bo_tax_report_it_640.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_it_payable" model="account.report.line">
                        <field name="name">Determination of Tax Payable</field>
                        <field name="code">l10n_bo_tax_report_it_payable</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_it_924" model="account.report.line">
                                <field name="name">Unpaid Tax (C996)</field>
                                <field name="code">l10n_bo_tax_report_it_924</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_924_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">924</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_924_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_996.balance</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_925" model="account.report.line">
                                <field name="name">Value Adjustment on Unpaid Tax</field>
                                <field name="code">l10n_bo_tax_report_it_925</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_925_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">925</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_925_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_938" model="account.report.line">
                                <field name="name">Interest on Adjusted Unpaid Tax</field>
                                <field name="code">l10n_bo_tax_report_it_938</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_938_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">938</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_938_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_954" model="account.report.line">
                                <field name="name">Fine for Non-Compliance to Formal Duty (IDF) for overdue declaration</field>
                                <field name="code">l10n_bo_tax_report_it_954</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_954_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">954</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_954_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_967" model="account.report.line">
                                <field name="name">Fine levied by the IDF for late declaration of a correction in the Tax Amount</field>
                                <field name="code">l10n_bo_tax_report_it_967</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_967_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">967</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_967_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_955" model="account.report.line">
                                <field name="name">Total Tax Payable (C924+C925+C938+C954+C967)</field>
                                <field name="code">l10n_bo_tax_report_it_955</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_955_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">955</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_955_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_924.balance + l10n_bo_tax_report_it_925.balance + l10n_bo_tax_report_it_938.balance + l10n_bo_tax_report_it_954.balance + l10n_bo_tax_report_it_967.balance</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_it_final_balance" model="account.report.line">
                        <field name="name">Definitive balance</field>
                        <field name="code">l10n_bo_tax_report_it_final_balance</field>
                        <field name="children_ids">
                            <record id="l10n_bo_tax_report_it_619" model="account.report.line">
                                <field name="name">Final balance of IUE to offset for the next period (C664-C909; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_619</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_619_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">619</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_619_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_664.balance - l10n_bo_tax_report_it_909.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_747" model="account.report.line">
                                <field name="name">Final Balance for Payments on Account in favor of the Taxpayer for the next period (C643-C955; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_747</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_747_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">747</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_747_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_643.balance - l10n_bo_tax_report_it_955.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_bo_tax_report_it_646" model="account.report.line">
                                <field name="name">Final balance in favour of the Tax Authority (C996 or (C955-C643) as appropriate; If >0)</field>
                                <field name="code">l10n_bo_tax_report_it_646</field>
                                <field name="expression_ids">
                                    <record id="l10n_bo_tax_report_it_646_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">646</field>
                                    </record>
                                    <record id="l10n_bo_tax_report_it_646_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_bo_tax_report_it_955.balance - l10n_bo_tax_report_it_643.balance</field>
                                        <field name="subformula">if_above(BOB(0))</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_bo_tax_report_whh_it" model="account.report.line">
                <field name="name">IT Withholdings - Form 410</field>
                <field name="code">l10n_bo_tax_report_whh_it</field>
                <field name="hierarchy_level" eval="0"/>
                <!-- Based on Form 410 - see https://www.impuestos.gob.bo/page/349 -->
                <field name="children_ids">
                    <record id="l10n_bo_tax_report_whh_it_13" model="account.report.line">
                        <field name="name">Retained tax</field>
                        <field name="code">l10n_bo_tax_report_whh_it_13</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_it_13_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">13</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_it_13_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Ret IT 13</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_bo_tax_report_whh_iue" model="account.report.line">
                <field name="name">IUE Withholdings - Form 570</field>
                <field name="code">l10n_bo_tax_report_whh_iue</field>
                <field name="hierarchy_level" eval="0"/>
                <!-- Based on Form 570 - see https://www.impuestos.gob.bo/page/349 -->
                <field name="children_ids">
                    <record id="l10n_bo_tax_report_whh_iue_13" model="account.report.line">
                        <field name="name">Amount Paid for Services</field>
                        <field name="code">l10n_bo_tax_report_whh_iue_13</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_iue_13_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">13</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_13_base" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Ret IUE 13</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_13_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_bo_tax_report_whh_iue_13.base / 0.845</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_whh_iue_26" model="account.report.line">
                        <field name="name">Amount Paid for Acquisitions</field>
                        <field name="code">l10n_bo_tax_report_whh_iue_26</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_iue_26_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">26</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_26_base" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Ret IUE 26</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_26_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_bo_tax_report_whh_iue_26.base / 0.92</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_whh_iue_1001" model="account.report.line">
                        <field name="name">Amount Subject to Tax (C13 * 0.5)</field>
                        <field name="code">l10n_bo_tax_report_whh_iue_1001</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_iue_1001_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">1001</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_1001_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_bo_tax_report_whh_iue_13.balance * 0.5</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_whh_iue_1002" model="account.report.line">
                        <field name="name">Amount Subject to Tax (C26 * 0.2)</field>
                        <field name="code">l10n_bo_tax_report_whh_iue_1002</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_iue_1002_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">1002</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_1002_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_bo_tax_report_whh_iue_26.balance * 0.5</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_bo_tax_report_whh_iue_909" model="account.report.line">
                        <field name="name">Determined tax (0.25 * (C1001 + C1002))</field>
                        <field name="code">l10n_bo_tax_report_whh_iue_909</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="l10n_bo_tax_report_whh_iue_909_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">909</field>
                            </record>
                            <record id="l10n_bo_tax_report_whh_iue_909_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">0.25 * (l10n_bo_tax_report_whh_iue_1001.balance + l10n_bo_tax_report_whh_iue_1002.balance)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
