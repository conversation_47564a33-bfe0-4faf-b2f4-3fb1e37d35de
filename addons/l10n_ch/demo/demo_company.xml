<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_ch" model="res.partner" forcecreate="1">
        <field name="name">CH Company</field>
        <field name="vat">CHE-530781296TVA</field>
        <field name="street">14 Meierskappelerstrasse</field>
        <field name="city">Risch-Rotkreuz</field>
        <field name="country_id" ref="base.ch"/>

        <field name="zip">6343</field>
        <field name="phone">+41 78 123 45 67</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.chexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_bank_ch" model="res.partner.bank" forcecreate="1">
        <field name="acc_type">iban</field>
        <field name="acc_number">*********************</field>
        <field name="partner_id" ref="base.partner_demo_company_ch"/>
    </record>

    <record id="base.demo_company_ch" model="res.company" forcecreate="1">
        <field name="name">CH Company</field>
        <field name="partner_id" ref="base.partner_demo_company_ch"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_ch')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_ch'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>ch</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_ch')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
