# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_recruitment_survey
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Lao (https://www.transifex.com/odoo/teams/41243/lo/)\n"
"Language: lo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_survey
#: model:mail.template,body_html:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'applicant'\">[applicant name]</t><br><br>\n"
"        <t>\n"
"            You've progressed through the recruitment process and we would like you to answer some questions.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t>\n"
"                    Start the written interview\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the interview for <t t-out=\"format_date(object.deadline)\">[deadline date]</t>.<br><br>\n"
"        </t>\n"
"        <t>\n"
"            We wish you good luck! Thank you in advance for your participation.\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Consult</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_invite__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_user_input__applicant_id
msgid "Applicant"
msgstr ""

#. module: hr_recruitment_survey
#: model:mail.template,name:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Applicant: Interview"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid "Choose an interview form for this job position and you will be able to print/answer this interview from all applicants who apply for this job"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
msgid "Interview Form: %s"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Survey"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interviews"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
msgid "Job Position"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr ""

#. module: hr_recruitment_survey
#: model:mail.template,subject:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Participate to {{ object.survey_id.display_name }} interview"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid "Please answer those questions to help recruitment officers to preprocess your application."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application."
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Please provide an applicant name."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Please summarize your education history: schools, location, diplomas, ..."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Please tell us a bit more about yourself: what are your main activities, ..."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_ids
msgid "Responses"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "See interview report"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "Send Interview"
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Send an interview"
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
msgid "Survey"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description_done:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/survey_user_input.py:0
msgid "The applicant \"%s\" has finished the survey."
msgstr ""

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/wizard/survey_invite.py:0
msgid "The survey %(survey_link)s has been sent to %(partner_link)s"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Were you referred by an employee?"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "What are your main knowledge regarding the job you are applying to?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr ""
