# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Odoo哥 <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"需要用于地理编码（地点）的 API 密钥。\n"
"有关详细信息，请访问 https://developers.google.com/maps/documentation/geocoding/get-api-key  。"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "API:"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute Localization"
msgstr "计算定位"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute based on address"
msgstr "基于地址的计算"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "创建人"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "创建日期"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid "Error with geolocation server: %s"
msgstr "地理定位服务器错误：%s"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "地理编码器"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geo Location"
msgstr "地理位置"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "地理提供商"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "地理位置"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "地理定位日期"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "谷歌地图API密钥"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
msgid "ID"
msgstr "ID"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "Key:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Lat :"
msgstr "纬度 ："

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Long:"
msgstr "经度："

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "名称"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
msgid "No match found for %(partner_names)s address(es)."
msgstr "找不到 %(partner_names)s 地址的匹配项(es)。"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignment"
msgstr "合作伙伴分配"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid "Provider %s is not implemented for geolocation service."
msgstr "未为地理位置服务实现提供程序 %s。"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh"
msgstr "刷新"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh Localization"
msgstr "刷新本地化"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Technical Name"
msgstr "技术名称"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"无法进行地理定位，收到错误：\n"
"%s\n"
"\n"
"谷歌将这个功能作为付费功能。\n"
"您应首先在您的 谷歌 帐户上启用计费。\n"
"然后，转到开发人员控制台，并启用 API：\n"
"地理编码，静态地图，地图Java脚本。\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Updated on:"
msgstr "更新于："

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"有关详细信息，请访问 https://developers.google.com/maps/documentation/geocoding/get-"
"api-key。"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
msgid "Warning"
msgstr "警告"
