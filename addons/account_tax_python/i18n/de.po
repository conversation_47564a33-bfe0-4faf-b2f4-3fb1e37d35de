# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Steuergruppen: Die Steuer ist eine Gruppe von Untersteuern.\n"
"- Fest: Der Steuerbetrag bleibt unabhängig vom Preis gleich.\n"
"- Prozentsatz: Der Steuerbetrag ist ein Prozentsatz des Preises:\n"
"   z. B. 100 * (1 + 10 %) = 110 (nicht im Preis enthalten)\n"
"   z. B. 110 / (1 + 10 %) = 100 (im Preis enthalten)\n"
"- Prozentsatz inkusive Steuern: Der Steuerbetrag ist eine Division des Preises:\n"
"   z. B. 180 / (1 - 10 %) = 200 (nicht im Preis enthalten)\n"
"   z. B. 200 * (1 - 10 %) = 180 (im Preis enthalten)"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__formula
msgid ""
"Compute the amount of the tax.\n"
"\n"
":param base: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: A object representing the product\n"
msgstr ""
"Berechnen Sie den Steuerbetrag.\n"
"\n"
":param base: Gleitkommazahl, tatsächlicher Betrag, auf den die Steuer angewendet wird\n"
":param price_unit: Gleitkommazahl\n"
":param quantity: Gleitkommazahl\n"
":param product: ein Objekt, das das Produkt präsentiert\n"

#. module: account_tax_python
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Custom Formula"
msgstr "Benutzerdefinierte Formel"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula
msgid "Formula"
msgstr "Formel"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula_decoded_info
msgid "Formula Decoded Info"
msgstr "Informationen zur entschlüsselten Formel"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
msgid "Malformed formula '%(formula)s' at position %(position)s"
msgstr "Schlecht formulierte Formel „%(formula)s“ in Position%(position)s"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Steuer"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Steuerberechnung"
