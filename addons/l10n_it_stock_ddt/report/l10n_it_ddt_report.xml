<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="report_ddt_view">
        <t t-call="web.external_layout">
            <t t-set="o" t-value="o.with_context(lang=lang)"/>
            <t t-if="o.move_ids_without_package and o.move_ids_without_package[0].partner_id and o.location_dest_id.usage == 'customer' and o.location_id.usage == 'supplier'">
              <t t-set="delivery_from" t-value="o.partner_id"/>
              <t t-set="delivery_to" t-value="o.move_ids_without_package[0].partner_id"/>
            </t>
            <t t-elif="o.picking_type_id.warehouse_id.partner_id">
              <t t-set="delivery_from" t-value="o.picking_type_id.warehouse_id.partner_id"/>
              <t t-set="delivery_to" t-value="o.partner_id"/>
            </t>
            <t t-else="">
              <t t-set="delivery_from" t-value="o.company_id.partner_id"/>
              <t t-set="delivery_to" t-value="o.partner_id"/>
            </t>
            <div class="page">
                <div class="row">
                    <div class="col-6">
                       <strong>Warehouse Address</strong>
                        <div t-esc="delivery_from"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        <p t-if="delivery_from.vat">VAT: <span t-field="delivery_from.vat"/></p>
                    </div>
                    <div class="col-5 offset-1">
                        <div>
                            <strong>Customer Address</strong>
                            <div t-esc="delivery_to"
                                   t-options='{"widget": "contact", "fields": ["address", "name", "phone", "vat"], "no_marker": True, "phone_icons": True}'/>
                        </div>
                    </div>
                </div>
                <div class="mt16"/>
                <div class="mt64"/>
                <div>
                    <h1>Documento di Trasporto <span t-esc="o.l10n_it_ddt_number"/></h1>
                </div>
                <div class="clearfix"/>
                <div class="mb32"/>
                <div class="row">
                    <div class="col-6">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td>Transportation Reason</td>
                                    <td><span t-field="o.l10n_it_transport_reason"/></td>
                                </tr>
                                <tr>
                                    <td>Transportation Method</td>
                                    <td><span t-field="o.l10n_it_transport_method"/></td>
                                </tr>
                                <tr>
                                    <td>Carrier Condition</td>
                                    <td><span t-field="o.sale_id.incoterm.name"/></td>
                                </tr>
                                <tr>
                                    <td>Carrier</td>
                                    <td><span t-field="o.carrier_id"/></td>
                                </tr>
                            </tbody>
                        </table>
                        <div t-if="o.l10n_it_transport_method_details">
                            <b>Transportation Method Details: </b>
                            <span t-field="o.l10n_it_transport_method_details"/>
                        </div>
                    </div>
                    <div class="col-5 offset-1">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td>Order</td>
                                    <td><span t-field="o.origin"/></td>
                                </tr>
                                <tr>
                                    <td>Picking Number</td>
                                    <td><span t-field="o.name"/></td>
                                </tr>
                                <tr>
                                    <td>Shipping Date</td>
                                    <td><span t-field="o.date_done"/></td>
                                </tr>
                                <tr>
                                    <td>Gross Weight (kg)</td>
                                    <td><span t-field="o.shipping_weight"/></td>
                                </tr>
                                <tr>
                                    <td>Parcels</td>
                                    <td><span t-field="o.l10n_it_parcels"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="mt64"/>
                <div t-if="o.note"><b>Note:</b> <span t-field="o.note"/></div>

                <div class="mt64"/>
                <div class="mt64"/>

                <table class="table table-borderless" name="document_details">
                    <t t-set="display_discount" t-value="any(m.sale_line_id.discount for m in o.move_ids)"/>
                    <thead>
                        <tr>
                            <th><strong>Product</strong></th>
                            <th><strong>Quantity</strong></th>
                            <t t-if="display_discount">
                                <th><strong>Disc.%</strong></th>
                            </t>
                            <th><strong>Total Value</strong></th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="total_value" t-value="0"/>
                        <t t-foreach="o.move_ids" t-as="move">
                            <t t-if="move.quantity">
                                <tr>
                                    <td>
                                        <span t-field="move.product_id"/>
                                    </td>
                                    <td>
                                        <span t-field="move.quantity"/>
                                        <span t-field="move.product_uom" groups="uom.group_uom"/>
                                    </td>
                                    <td t-if="display_discount">
                                        <span t-field="move.sale_line_id.discount"/>
                                    </td>
                                    <td>
                                        <t t-if="move.sale_line_id">
                                            <t t-set="lst_price" t-value="move.sale_line_id.price_reduce_taxinc * move.quantity"/>
                                        </t>
                                        <t t-else="">
                                            <t t-set="lst_price" t-value="move.product_id.lst_price * move.product_uom._compute_quantity(move.quantity, move.product_id.uom_id)"/>
                                        </t>
                                        <span t-esc="lst_price"  t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/>
                                        <t t-set="total_value" t-value="total_value + lst_price"/>
                                    </td>
                                </tr>
                            </t>
                        </t>
                        <tr>
                            <td t-if="display_discount">
                            </td>
                            <td>
                            </td>
                            <td style="text-align:right">
                                <b>Total:</b>
                            </td>
                            <td>
                                <span t-esc="total_value"  t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="mt64"/>
                <div class="mt64"/>
                <table class="table table-borderless">
                    <thead>
                        <tr>
                            <th><span class="fa fa-pencil mt4"/><div class="ml4 d-inline-block"/><strong>Company Signature</strong></th>
                            <th><span class="fa fa-pencil mt4"/><div class="ml4 d-inline-block"/><strong>Carrier Signature</strong></th>
                            <th><span class="fa fa-pencil mt4"/><div class="ml4 d-inline-block"/><strong>Customer Signature</strong></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="col">
                                </div>
                            </td>
                            <td>
                                <div class="col">
                                </div>
                            </td>
                            <td>
                                <div class="col">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </t>
    </template>

    <template id="report_ddt">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-set="lang" t-value="o._get_report_lang()"/>
                <t t-call="l10n_it_stock_ddt.report_ddt_view" t-lang="lang"/>
            </t>
        </t>
    </template>

    <record id="action_report_ddt" model="ir.actions.report">
        <field name="name">DDT report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_it_stock_ddt.report_ddt</field>
        <field name="report_file">report_ddt</field>
        <field name="print_report_name">'DDT - %s - %s' % (object.partner_id.name or '', object.l10n_it_ddt_number)</field>
    </record>
</odoo>
