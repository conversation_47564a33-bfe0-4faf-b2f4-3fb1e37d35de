# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# Guspy12, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-08 06:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_provider_form
msgid "API Key"
msgstr "API Key"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "Cancelled payment with status: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__code
msgid "Code"
msgstr "Codi"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "No s'ha pogut establir la connexió a l'API."

#. module: payment_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_provider__code__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__mollie_api_key
msgid "Mollie API Key"
msgstr "Clau de l'API Mollie"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "No s'ha trobat cap transacció que coincideixi amb la referència %s."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_provider
msgid "Payment Provider"
msgstr "Proveïdor de pagament"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacció de pagament"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "Received data with invalid payment status: %s"
msgstr "Dades rebudes amb un estat de pagament no vàlid:%s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the provider"
msgstr ""

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Mollie gave us the following "
"information: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "El codi tècnic d'aquest proveïdor de pagaments."
