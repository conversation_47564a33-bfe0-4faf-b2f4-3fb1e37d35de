# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Lasse L, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
#: code:addons/resource/models/resource_resource.py:0
msgid "%s (copy)"
msgstr "%s (kopia)"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Frånvaro</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Work Resources</span>"
msgstr "<span class=\"o_stat_text\"> Arbetsresurser </span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span> hours/week</span>"
msgstr "<span> timmar/vecka</span>"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Aktiv"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Eftermiddag"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Arkiverad"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 1-week calendar? All work entries will "
"be lost."
msgstr ""
"Är du säker på att du vill byta till en 1-veckaskalender? Alla arbetsinlägg "
"kommer att gå förlorade."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 2-week calendar? All work entries will "
"be lost."
msgstr ""
"Är du säker på att du vill byta till en 2-veckorskalender? Alla arbetsinlägg"
" kommer att gå förlorade."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Attendances can't overlap."
msgstr "Närvaron får inte överlappa varandra."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Genomsnittligt antal timmar per dag"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Genomsnittligt antal timmar per dag en resurs förväntas arbeta med den här "
"kalendern."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__lunch
msgid "Break"
msgstr "Gå sönder"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Kalender i 2-veckors läge"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Avslutande dagar"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Företag"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__full_time_required_hours
msgid "Company Full Time"
msgstr "Företag heltid"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Date"
msgstr "Datum"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Period på dagen"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Veckodag"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Standard arbetstid"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"Definiera resursens arbetsschema. Om det inte anges kommer resursen att ha "
"helt flexibla arbetstider."

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Definiera arbetstid och schema som skall utgöra underlag för tidsplanering "
"av dina projektdeltagare"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Visningstyp"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_days
msgid "Duration (days)"
msgstr "Varaktighet (dagar)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_hours
msgid "Duration (hours)"
msgstr "Varaktighet (timmar)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Effektivitetsfaktor"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__email
msgid "Email"
msgstr "E-post"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Slutdatum"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Förklaring"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Extern användare med begränsad åtkomst, skapad endast för att dela data."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Första"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "First week"
msgstr "Första veckan"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__flexible_hours
msgid "Flexible Hours"
msgstr "Flexibla tider"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Fredag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Afternoon"
msgstr "Fredag eftermiddag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Lunch"
msgstr "Fredag lunch"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Morning"
msgstr "Fredag morgon"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
msgid "Fully Flexible"
msgstr "Helt flexibel"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr "Anger sekvensen för denna rad vid visning av resurskalendern."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Global frånvaro"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Grupp Av"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Timmar"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Hours per Week"
msgstr "Timmar per vecka"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Mänsklig"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"Om det är tomt är detta en generisk ledighet för företaget. Om en resurs är "
"angiven är ledigheten endast för denna resurs"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Om det aktiva fältet är satt till False/Falskt, kommer det att du kan dölja "
"resursposten utan att ta bort det."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Om det aktiva fältet är inställt på false kan du dölja arbetstiden utan att "
"ta bort den."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"I en kalender med 2-veckorsläge måste alla perioder finnas i sektionerna."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Material"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Måndag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Afternoon"
msgstr "Måndag eftermiddag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Lunch"
msgstr "Måndag lunch"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Morning"
msgstr "Måndag morgon"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Morgon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
msgid "Name"
msgstr "Namn"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Antal timmar att arbeta enligt företagets schema för att betraktas som "
"heltid."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Övriga"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Period"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__phone
msgid "Phone"
msgstr "Telefon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Anledning"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Relaterat användarnamn för resursen för att hantera dess åtkomst."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Resurs"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Mixin för resurser"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Frånvaro för resurs"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Resurs ledihgetsdetalj"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Resursens arbetstid"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Resursens kalender"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Resurser"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Resurser Fritid"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Med resurser kan du skapa och hantera resurser som bör vara inblandade i en "
"specifikt projektfas. Du kan också ställa in resursens verkningsgrad och "
"arbetsbelastning utifrån deras veckoarbetstid."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Saturday"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Sök resurs"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Sökning Arbetsperiod Fritid"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Sök arbetstid"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Sekund"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "Second week"
msgstr "Andra veckan"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Sektion"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__share
msgid "Share User"
msgstr "Dela användare"

#. module: resource
#. odoo-python
#: code:addons/resource/models/res_company.py:0
msgid "Standard 40 hours/week"
msgstr "Standard 40 timmar/vecka"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Startdatum"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Start- och sluttid för arbetet.\n"
"Ett specifikt värde på 24:00 tolkas som 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Startdatum"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Time Off"
msgstr "Startdatum för ledighet"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Söndag"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Växla till 1 veckas kalender"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Växla till 2 veckors kalender"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Tekniskt område för UX-ändamål."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"The current week (from %(first_day)s to %(last_day)s) corresponds to week "
"number %(number)s."
msgstr ""
"Den aktuella veckan (från %(first_day)s till %(last_day)s) motsvarar "
"veckonummer %(number)s."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_leaves.py:0
msgid "The start date of the time off must be earlier than the end date."
msgstr "Ledighetens startdatum måste vara tidigare än slutdatumet."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Detta fält används för att definiera i vilken tidszon resurserna ska arbeta."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Det här fältet används för att beräkna den förväntade tidsåtgången för en "
"arbetsorder på det här produktionsgrupp. Om en arbetsorder till exempel tar "
"en timme och effektivitetsfaktorn är 100 % är den förväntade tidsåtgången en"
" timme. Om effektivitetsfaktorn är 200 % kommer den förväntade tidsåtgången "
"att vara 30 minuter."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Torsdag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Afternoon"
msgstr "Torsdag eftermiddag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Lunch"
msgstr "Torsdag lunch"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Morning"
msgstr "Torsdag morgon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
msgid "Time Off"
msgstr "Frånvaro"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Time Off Detail"
msgstr "Ledighet detalj"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Tidstyp"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "Tidseffektiviteten måste vara strikt positiv"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
msgid "Timezone"
msgstr "Tidszon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Offset tidsskillnad"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Tisdag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Afternoon"
msgstr "Tisdag eftermiddag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Lunch"
msgstr "Tisdag lunch"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Morning"
msgstr "Tisdag morgon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Typ"

#. module: resource
#: model:ir.model,name:resource.model_res_users
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Användare"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Onsdag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Afternoon"
msgstr "Onsdag eftermiddag"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Lunch"
msgstr "Onsdag lunch"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Morning"
msgstr "Onsdag morgon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Veckonummer"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__flexible_hours
msgid ""
"When enabled, it will allow employees to work flexibly, without relying on "
"the company's working schedule (working hours)."
msgstr ""
"När det är aktiverat kommer det att tillåta anställda att arbeta flexibelt, "
"utan att förlita sig på företagets arbetsschema (arbetstid)."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Om detta ska beräknas som ledig tid eller som arbetstid (t.ex. utbildning)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Arbetsdetaljer"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Arbeta från"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Arbeta till"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Arbetstimmar"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Working Hours of %s"
msgstr "Arbetstider för %s"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Schedules"
msgstr "Arbetsscheman"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Arbetstid"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "You can't delete section between weeks."
msgstr "Du kan inte radera sektioner mellan veckor."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "first"
msgstr "första"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "other week"
msgstr "andra veckan"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "second"
msgstr "sekund"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "this week"
msgstr "denna vecka"
