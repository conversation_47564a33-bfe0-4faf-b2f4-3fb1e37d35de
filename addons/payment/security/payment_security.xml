<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Providers -->

    <record id="payment_provider_company_rule" model="ir.rule">
        <field name="name">Access providers in own companies only</field>
        <field name="model_id" ref="payment.model_payment_provider"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <!-- Transactions -->

    <record id="transaction_company_rule" model="ir.rule">
        <field name="name">Access transactions in own companies only</field>
        <field name="model_id" ref="payment.model_payment_transaction"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <!-- Tokens -->

    <record id="payment_token_user_rule" model="ir.rule">
        <field name="name">Users can access only their own tokens</field>
        <field name="model_id" ref="payment.model_payment_token"/>
        <field name="domain_force">[('partner_id', '=', user.partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user')),
                                    (4, ref('base.group_portal')),
                                    (4, ref('base.group_public'))]"/>
    </record>

    <record id="payment_token_company_rule" model="ir.rule">
        <field name="name">Access tokens in own companies only</field>
        <field name="model_id" ref="payment.model_payment_token"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <!-- Wizards -->

    <record id="payment_capture_wizard_rule" model="ir.rule">
        <field name="name">Payment Capture Wizard</field>
        <field name="model_id" ref="model_payment_capture_wizard"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
     </record>

</odoo>
