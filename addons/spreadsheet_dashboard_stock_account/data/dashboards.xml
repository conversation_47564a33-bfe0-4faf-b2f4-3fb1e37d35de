<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="spreadsheet_dashboard_warehouse_metrics" model="spreadsheet.dashboard">
        <field name="name">Warehouse Metrics</field>
        <field name="spreadsheet_binary_data" type="base64" file="spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json"/>
        <field name="main_data_model_ids" eval="[(4, ref('stock.model_stock_quant'))]"/>
        <field name="sample_dashboard_file_path">spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json</field>
        <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_logistics"/>
        <field name="group_ids" eval="[Command.link(ref('stock.group_stock_manager'))]"/>
        <field name="sequence">300</field>
        <field name="is_published">True</field>
    </record>

</odoo>
