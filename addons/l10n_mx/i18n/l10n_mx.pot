# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 07:50+0000\n"
"PO-Revision-Date: 2023-09-07 07:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_res_bank__l10n_mx_edi_code
msgid "ABM Code"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_mx
#: model:uom.uom,name:l10n_mx.product_uom_activity
msgid "Activity"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"All our contractual relations will be governed exclusively by Mexico law."
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_bank
msgid "Bank"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_account_setup_bank_manual_config__l10n_mx_edi_clabe
#: model:ir.model.fields,field_description:l10n_mx.field_res_partner_bank__l10n_mx_edi_clabe
msgid "CLABE"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can "
"ESCUELA KEMPER URGATE become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to ESCUELA "
"KEMPER URGATE in its entirety and does not include any costs relating to the"
" legislation of the country in which the client is located."
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_mx
#: model_terms:ir.ui.view,arch_db:l10n_mx.res_config_settings_view_form
msgid "Create your electronic invoices automatically (CFDI format)"
msgstr ""

#. module: l10n_mx
#: model:account.account.tag,name:l10n_mx.tag_credit_balance_account
msgid "Credit Balance Account"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_factor_type__cuota
msgid "Cuota"
msgstr ""

#. module: l10n_mx
#: model:account.report,name:l10n_mx.diot_report
#: model:account.report.line,name:l10n_mx.diot_report_line
msgid "DIOT"
msgstr ""

#. module: l10n_mx
#: model:account.account.tag,name:l10n_mx.tag_debit_balance_account
msgid "Debit Balance Account"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"ESCUELA KEMPER URGATE undertakes to do its best to supply performant "
"services in due time in accordance with the agreed timeframes. However, none"
" of its obligations can be considered as being an obligation to achieve "
"results. ESCUELA KEMPER URGATE cannot under any circumstances, be required "
"by the client to appear as a third party in the context of any claim for "
"damages filed against the client by an end consumer."
msgstr ""

#. module: l10n_mx
#. odoo-python
#: code:addons/l10n_mx/models/template_mx.py:0
msgid "Effectively Paid"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_factor_type__exento
msgid "Exento"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_account_tax__l10n_mx_factor_type
msgid "Factor Type"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_tax_type__ieps
msgid "IEPS"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_tax_type__isr
msgid "ISR"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_tax_type__iva
msgid "IVA"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, ESCUELA KEMPER URGATE reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_importation_16
msgid "Importation 16%"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"In order for it to be admissible, ESCUELA KEMPER URGATE must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""

#. module: l10n_mx
#: model:uom.uom,name:l10n_mx.product_uom_job
msgid "Job"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_res_config_settings__module_l10n_mx_edi
msgid "Mexican Electronic Invoicing"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_account_tax__l10n_mx_factor_type
msgid ""
"Mexico: 'TipoFactor' is an attribute for CFDI 4.0. This indicates the factor"
" type that is applied to the base of the tax."
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, ESCUELA KEMPER URGATE reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"ESCUELA KEMPER URGATE will be authorized to suspend any provision of "
"services without prior warning in the event of late payment."
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_paid_0
msgid "Paid 0%"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_paid_16
msgid "Paid 16%"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_paid_16_non_cred
msgid "Paid 16% - Non-Creditable"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_paid_8
msgid "Paid 8 %"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_paid_8_non_cred
msgid "Paid 8 % - Non-Creditable"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_refunds
msgid "Refunds"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_account_tax__l10n_mx_tax_type
msgid "SAT Tax Type"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr ""

#. module: l10n_mx
#: model:uom.category,name:l10n_mx.product_uom_categ_service
msgid "Service"
msgstr ""

#. module: l10n_mx
#: model:uom.uom,name:l10n_mx.product_uom_service_unit
msgid "Service Unit"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_account_setup_bank_manual_config__l10n_mx_edi_clabe
#: model:ir.model.fields,help:l10n_mx.field_res_partner_bank__l10n_mx_edi_clabe
msgid ""
"Standardized banking cipher for Mexico. More info wikipedia.org/wiki/CLABE"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_tax__l10n_mx_factor_type__tasa
msgid "Tasa"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_res_bank__l10n_mx_edi_code
msgid ""
"Three-digit number assigned by the ABM to identify banking institutions (ABM"
" is an acronym for Asociación de Bancos de México)"
msgstr ""

#. module: l10n_mx
#: model:account.report.column,name:l10n_mx.diot_report_withheld
msgid "Withheld"
msgstr ""

#. module: l10n_mx
#: model_terms:res.company,invoice_terms_html:l10n_mx.demo_company_mx
msgid "You should update this document to reflect your T&amp;C."
msgstr ""
