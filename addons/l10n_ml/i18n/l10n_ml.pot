# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ml
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:44+0000\n"
"PO-Revision-Date: 2023-11-30 10:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ml
#: model:ir.model,name:l10n_ml.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_purchases_deduction_adjustment
msgid "Additional deduction following adjustment"
msgstr ""

#. module: l10n_ml
#: model:account.report.column,name:l10n_ml.account_tax_report_ml_balance
msgid "Base"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_purchases_credit_reported
msgid "Credit to be carried forward from previous months"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_credit_report_deductions
msgid "Credit to report coming from deductions"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_deductible
msgid "Deductible"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_export
msgid "Export"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_gross_pay
msgid "Gross VAT to pay"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_to_pay
msgid "Net VAT to pay"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales
msgid "Outgoing"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_reimbursement
msgid "Reimbursement Asked"
msgstr ""

#. module: l10n_ml
#. odoo-python
#: code:addons/l10n_ml/models/template_ml_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_ml
#. odoo-python
#: code:addons/l10n_ml/models/template_ml.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_sd
msgid "Self Delivery"
msgstr ""

#. module: l10n_ml
#: model:account.report.column,name:l10n_ml.account_tax_report_ml_tax
msgid "Tax"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_purchases_taxable
msgid "Taxable"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_18
msgid "Taxable operations at 18%"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_5
msgid "Taxable operations at 5%"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_deductions
msgid "Total deductions"
msgstr ""

#. module: l10n_ml
#: model:account.report,name:l10n_ml.account_tax_report_ml
msgid "VAT Report"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_credit_report
msgid "VAT credit to report"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_refund_regularisation
msgid "VAT refund following adjustment"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_purchases_withheld
msgid "VAT withheld by clients"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_sd_18
msgid "at 18%"
msgstr ""

#. module: l10n_ml
#: model:account.report.line,name:l10n_ml.account_tax_report_line_ml_sales_sd_5
msgid "at 5%"
msgstr ""
