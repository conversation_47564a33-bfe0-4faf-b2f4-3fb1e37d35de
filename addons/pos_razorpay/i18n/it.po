# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_razorpay
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__all
msgid "All"
msgstr "Tutti"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__bharatqr
msgid "BHARATQR"
msgstr "BHARATQR"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/razorpay_pos_request.py:0
msgid "Cannot decode Razorpay POS response"
msgstr "Impossibile decodificare risposta POS Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Impossibile elaborare transazioni con importo negativo."

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__card
msgid "Card"
msgstr "Carta di credito"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid ""
"Choose allow payment mode: \n"
" All/Card/UPI or QR"
msgstr ""
"Scegli modalità di pagamento da abilitare: \n"
" Tutte/Carta/UPI o QR"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Impossibile connettersi al server Odoo, controlla la tua connessione "
"internet e riprova."

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid ""
"Device Serial No \n"
" ex: 7000012300"
msgstr ""
"N. di serie dispositivo \n"
" ad es.: 7000012300"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Payment has been queued. You may choose to wait for the payment to initiate "
"on terminal or proceed to cancel this transaction"
msgstr ""
"Il pagamento è in attesa. Puoi scegliere se aspettare che il pagamento venga"
" avviato sul terminale oppure annullare la transazione"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metodi di pagamento punto vendita"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pagamenti punto vendita"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid "Razorpay API Key"
msgstr "Chiave API Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid "Razorpay Allowed Payment Modes"
msgstr "Modalità di pagamento consentite Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid "Razorpay Device Serial No"
msgstr "N. di serie dispositivo Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Razorpay Error"
msgstr "Errore Razorpay"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment cancel request expected errorCode not found in the "
"response"
msgstr ""
"La richiesta di annullamento del pagamento tramite POS Razorpay ha rilevato "
"un codice errore non trovato nella risposta"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment request expected errorCode not found in the response"
msgstr ""
"La richiesta di pagamento tramite POS Razorpay ha rilevato un codice errore "
"non trovato nella risposta"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment status request expected errorCode not found in the "
"response"
msgstr ""
"La richiesta dello stato del pagamento tramite POS Razorpay ha rilevato un "
"codice errore non trovato nella risposta"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction canceled successfully"
msgstr "Transazione POS Razorpay annullata con successo"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction failed"
msgstr "Transazione POS Razorpay non riuscita"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_reverse_ref_no
msgid "Razorpay Reverse Reference No."
msgstr "N. di riferimento inversione Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Razorpay Test Mode"
msgstr "Modalità di prova Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_username
msgid "Razorpay Username"
msgstr "Nome utente Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Reference number mismatched"
msgstr "Numero di riferimento non corrispondente"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "This Payment Terminal is only valid for INR Currency"
msgstr "Il terminale di pagamento è valido esclusivamente per la valuta INR"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Transaction failed due to inactivity"
msgstr "Transazione non riuscita a causa dell'inattività"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Turn it on when in Test Mode"
msgstr "Attivalo in modalità di prova"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__upi
msgid "UPI"
msgstr "UPI"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid ""
"Used when connecting to Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"
msgstr ""
"Utilizzato al momento della connessione a Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_username
msgid ""
"Username(Device Login) \n"
" ex: **********"
msgstr ""
"Nome utente (Accesso dispositivo) \n"
" ad es.: **********"
