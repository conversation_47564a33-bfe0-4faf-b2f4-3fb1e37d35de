# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_check
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-11 15:22+0000\n"
"PO-Revision-Date: 2025-02-11 15:22+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid "<span>Journal Entry</span>"
msgstr "<span>Asiento Contable</span>"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid "<span>Operations</span>"
msgstr "<span>Operaciones</span>"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid "<span>Payment</span>"
msgstr "<span>Pago</span>"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid "<span>Reconciled move</span>"
msgstr "<span>Asiento conciliado</span>"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"A payment with any Third Party Check or Own Check payment methods needs an "
"outstanding account"
msgstr ""
"Un pago con cualquiera de los métodos de pago Cheque de terceros o Cheque "
"propio requiere una cuenta de liquidez"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid "A second payment has been created: "
msgstr "Se ha creado un segundo pago: "

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de plan contable"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_l10n_latam_check
msgid "Account payment check"
msgstr "Cheque"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad  de Excepción"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid "All selected checks must be on the same journal and on hand"
msgstr "Todos los cheques seleccionados deben estan en el mismo Diario y a mano"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid "All the selected checks must be posted"
msgstr "Todos los cheques seleccionados deben estar publicados"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid "All the selected checks must use the same currency"
msgstr "Todos los cheques seleccionados deben ser de la misma moneda"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment__amount
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__amount
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__amount
msgid "Amount"
msgstr "Importe"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__bank_id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__bank_id
msgid "Bank"
msgstr "Banco"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_l10n_latam_payment_mass_transfer_form
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__check_ids
msgid "Check"
msgstr "Cheque"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid "Check %(check_number)s - %(suffix)s"
msgstr "Cheque %(check_number)s - %(suffix)s"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/l10n_latam_check.py:0
msgid "Check Operations"
msgstr "Operaciones del Cheque"

#. module: l10n_latam_check
#: model:ir.actions.act_window,name:l10n_latam_check.action_view_l10n_latam_payment_mass_transfer
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_third_party_check_tree
msgid "Check Transfer"
msgstr "Transferir Cheque"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_move_line__l10n_latam_check_ids
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment__l10n_latam_new_check_ids
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment_register__l10n_latam_move_check_ids
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_form_inherited
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_register_form
msgid "Checks"
msgstr "Cheques"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid "Checks %s delivered"
msgstr "Cheques %s entregados"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid "Checks %s received"
msgstr "Cheques %s recibidos"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_l10n_latam_payment_mass_transfer
msgid "Checks Mass Transfers"
msgstr "Transferencia Masiva de Cheques"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment__l10n_latam_move_check_ids
msgid "Checks Operations"
msgstr "Cheques"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__payment_method_code
msgid "Code"
msgstr "Código"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__company_id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__company_id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__company_id
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Company"
msgstr "Compañía"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_l10n_latam_payment_mass_transfer_form
msgid "Create Transfers"
msgstr "Crear Transferencia"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__create_uid
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__create_uid
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__create_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__create_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__currency_id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__current_journal_id
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_third_party_checks_search
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_third_party_check_tree
msgid "Current Journal"
msgstr "Diario Actual"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_own_check_tree
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_third_party_check_operations_tree
msgid "Customer"
msgstr "Cliente"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__partner_id
msgid "Customer/Vendor"
msgstr "Cliente/Proveedor"

#. module: l10n_latam_check
#: model:ir.model.fields.selection,name:l10n_latam_check.selection__l10n_latam_check__issue_state__debited
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Debited"
msgstr "Debitado"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__destination_journal_id
msgid "Destination Journal"
msgstr "Diario de Destino"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__display_name
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__display_name
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__display_name
msgid "Display Name"
msgstr "Nombre Mostrado"

#. module: l10n_latam_check
#: model:account.payment.method,name:l10n_latam_check.account_payment_method_in_third_party_checks
#: model:account.payment.method,name:l10n_latam_check.account_payment_method_out_third_party_checks
msgid "Existing Third Party Checks"
msgstr "Cheque de Terceros Existente"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: l10n_latam_check
#: model:ir.model.fields.selection,name:l10n_latam_check.selection__l10n_latam_check__issue_state__handed
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Handed"
msgstr "Entregado"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__id
msgid "ID"
msgstr ""

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si se encuentra seleccionado, hay nuevos mensajes que requieren tu atención."

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__message_has_error
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__issue_state
msgid "Issue State"
msgstr "Estado de Emisión"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__issuer_vat
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__issuer_vat
msgid "Issuer Vat"
msgstr "CUIT del Emisor"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"It seems you're trying to move a check with a date (%(date)s) prior to last "
"operation done with the check (%(last_operation)s). This may be wrong, "
"please double check it. By continue, the last operation on the check will "
"remain being %(last_operation)s"
msgstr ""

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_journal
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__original_journal_id
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__journal_id
msgid "Journal"
msgstr "Diario"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment__l10n_latam_check_warning_msg
msgid "L10N Latam Check Warning Msg"
msgstr ""

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__write_uid
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__write_uid
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__write_uid
msgid "Last Updated by"
msgstr "Última Actualización por"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__write_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__write_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__write_date
msgid "Last Updated on"
msgstr "Última Actualización el"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Late Activities"
msgstr "Actividades tardías"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"Manual: Pague o cobre con cualquier método fuera de Odoo.\n"
"Payment Providers: Cada proveedor de pagos tiene su propio método de pago. Solicite una transacción desde/hacia una tarjeta gracias a un token de pago guardado por el socio al comprar o suscribirse en línea..\n"
"Check: Pague las cuentas con cheques e imprímalo desde Odoo.\n"
"Batch Deposit: Cobre varios cheques de clientes a la vez generando y enviando un ingreso por lotes a su banco. Módulo account_batch_payment es necesario.\n"
"SEPA Credit Transfer: Pague en la zona SEPA enviando un fichero de transferencia SEPA a su banco. Módulo account_sepa es necesario.\n"
"SEPA Direct Debit: Cobre en la zona SEPA gracias a un mandato que su socio le habrá otorgado. Módulo account_sepa es necesario.\n"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid ""
"Marking a check as void will cancel the check and generate a new entry that "
"will re-open the debt."
msgstr ""
"Marcar un cheque como anulado cancelará el cheque y generará un nuevo "
"asiento que reabrirá la deuda."

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__communication
msgid "Memo"
msgstr ""

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mi fecha límite de actividad"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_account_payment_register__l10n_latam_new_check_ids
msgid "New Checks"
msgstr "Cheques Nuevos"

#. module: l10n_latam_check
#: model:account.payment.method,name:l10n_latam_check.account_payment_method_new_third_party_checks
msgid "New Third Party Checks"
msgstr "Nuevo cheque de Terceros Existente"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Próximo evento del calendario de actividades"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de siguiente actividad"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__name
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__name
msgid "Number"
msgstr "Número"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren acción"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_third_party_checks_search
msgid "On hand"
msgstr ""

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_form_inherited
msgid "Open"
msgstr "Abrir"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__operation_ids
msgid "Operation"
msgstr "Operación"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"Other checks were found with same number, issuer and bank. Please double "
"check you are not encoding the same check more than once. List of other "
"payments/checks: %s"
msgstr ""
"Se encontraron otros cheques con el mismo número, emisor y banco. Verifique "
"que no esté codificando el mismo cheque más de una vez. Lista de otros "
"pagos/cheques: %s"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__outstanding_line_id
msgid "Outstanding Line"
msgstr "Línea de Liquidez"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_chart_template.py:0
msgid "Outstanding Payments"
msgstr "Pagos pendientes"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_chart_template.py:0
msgid "Outstanding Receipts"
msgstr "Ingresos pendientes"

#. module: l10n_latam_check
#: model:account.payment.method,name:l10n_latam_check.account_payment_method_own_checks
#: model:ir.actions.act_window,name:l10n_latam_check.action_own_check
#: model:ir.ui.menu,name:l10n_latam_check.menu_own_check
msgid "Own Checks"
msgstr "Cheques Propios"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Partner"
msgstr "Contacto"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_payment_register
msgid "Pay"
msgstr "Registrar pago"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_payment_register
msgid "Pay"
msgstr "Pagar"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__payment_id
msgid "Payment"
msgstr "Pago"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_own_check_tree
msgid "Payment Currency"
msgstr "Divisa del pago"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__payment_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_mass_transfer__payment_date
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__payment_date
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Payment Date"
msgstr "Fecha de Pago"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__payment_method_line_id
msgid "Payment Method"
msgstr "Método de pago"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_payment_register_check__payment_register_id
msgid "Payment Register"
msgstr "Registro de pago"

#. module: l10n_latam_check
#: model:ir.model,name:l10n_latam_check.model_l10n_latam_payment_register_check
msgid "Payment register check"
msgstr "Registro de pago de cheque"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
#: model:ir.model,name:l10n_latam_check.model_account_payment
msgid "Payments"
msgstr "Pagos"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__rating_ids
msgid "Ratings"
msgstr ""

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_chart_template.py:0
msgid "Rejected Third Party Checks"
msgstr "Cheques de Terceros Rechazados"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: l10n_latam_check
#: model:account.payment.method,name:l10n_latam_check.account_payment_method_return_third_party_checks
msgid "Return Third Party Checks"
msgstr "Cheques de terceros devueltos"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid "Selected checks \"%s\" are not posted"
msgstr "Los cheques seleccionados \"%s\" no están publicados"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"Some checks are already in hand and can't be received again. Checks: %s"
msgstr ""
"Algunos cheques ya están en mano y no se pueden volver a recibir. Cheques: "
"%s"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"Some checks are not anymore in journal, it seems it has been moved by "
"another payment."
msgstr ""
"Algunos cheques ya no están en el diario, parece que han sido movidos por "
"otro pago."

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "State"
msgstr "Estado"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/l10n_latam_check.py:0
msgid "The amount of the check must be greater than 0"
msgstr "El monto del cheque debe ser mayor a 0"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"The amount of the payment  does not match the amount of the selected check. "
"Please try to deselect and select the check again."
msgstr ""
"El monto del pago no coincide con el monto del cheque seleccionado. Intente "
"deseleccionar y seleccionar el cheque nuevamente."

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"The currency of the payment and the currency of the check must be the same."
msgstr "La moneda del pago y la moneda del cheque deben ser la misma."

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__currency_id
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_payment_register_check__currency_id
msgid "The payment's currency."
msgstr "Moneda del pago."

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid ""
"The register payment wizard should only be called on account.payment "
"records."
msgstr ""
"El asistente de registro de pagos sólo debe llamarse en account.payment "
"registros."

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_chart_template.py:0
#: model:ir.actions.act_window,name:l10n_latam_check.action_third_party_check
#: model:ir.ui.menu,name:l10n_latam_check.menu_third_party_check
msgid "Third Party Checks"
msgstr "Cheques de Terceros"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py:0
msgid "This payment has been created from: "
msgstr "Este pago fue creado desde: "

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.l10n_latam_check_view_form
msgid "Void Check"
msgstr "Anular Cheque"

#. module: l10n_latam_check
#: model:ir.model.fields.selection,name:l10n_latam_check.selection__l10n_latam_check__issue_state__voided
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_search
msgid "Voided"
msgstr "Anulado"

#. module: l10n_latam_check
#: model:ir.model.fields,field_description:l10n_latam_check.field_l10n_latam_check__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: l10n_latam_check
#: model:ir.model.fields,help:l10n_latam_check.field_l10n_latam_check__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: l10n_latam_check
#. odoo-python
#: code:addons/l10n_latam_check/models/account_payment.py:0
msgid ""
"You can't cancel or re-open a payment with checks if some check has been debited or been voided. Checks:\n"
"%s"
msgstr ""
"No puede cancelar o reabrir un pago con cheques si algún cheque ha sido debitado o anulado. Cheques:\n"
"%s"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_register_form
msgid ""
"You can't use checks when paying invoices of different partners or same "
"partner without grouping"
msgstr ""
"No puede utilizar cheques al pagar facturas de diferentes partners o del "
"mismo partner sin agrupar"

#. module: l10n_latam_check
#: model_terms:ir.ui.view,arch_db:l10n_latam_check.view_account_payment_form_inherited
msgid "open"
msgstr "abrir"
