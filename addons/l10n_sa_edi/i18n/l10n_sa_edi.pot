# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_sa_edi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-22 15:24+0000\n"
"PO-Revision-Date: 2024-08-22 15:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/res_config_settings.py:0
msgid ""
"\n"
"Building Number: %(building_number)s, Plot Identification: %(plot_identification)s\n"
"Neighborhood: %(neighborhood)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- Finish the Onboarding procees for journal %s by requesting the CSIDs and "
"completing the checks."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "- Invoice lines should have at least one Tax applied."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- No Private Key was generated for company %s. A Private Key is mandatory in"
" order to generate Certificate Signing Requests (CSR)."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- No Serial Number was assigned for journal %s. A Serial Number is mandatory"
" in order to generate Certificate Signing Requests (CSR)."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- Please, make sure either the Reversed Entry or the Reversal Reason are "
"specified when confirming a Credit/Debit note"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- Please, make sure the invoice date is set to either the same as or before "
"Today."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "- Please, set the following fields on the Customer: %(missing_fields)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "- Please, set the following fields on the Supplier: %(missing_fields)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"- The company VAT identification must contain 15 digits, with the first and "
"last digits being '3' as per the BR-KSA-39 and BR-KSA-40 of ZATCA KSA "
"business rule."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "- You cannot post invoices where the Seller is the Buyer"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__700
msgid "700 Number"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"<b>\n"
"                                In order to be able to submit Invoices to ZATCA, the following steps need to be completed:\n"
"                            </b>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid "<i class=\"fa fa-warning me-2\"/> Warning"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "<strong>Exchange Rate</strong>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "<strong>Subtotal (SAR)</strong>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "<strong>Total (SAR)</strong>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "<strong>VAT Amount (SAR)</strong>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid "API Mode"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_res_company__l10n_sa_additional_identification_number
#: model:ir.model.fields,help:l10n_sa_edi.field_res_partner__l10n_sa_additional_identification_number
#: model:ir.model.fields,help:l10n_sa_edi.field_res_users__l10n_sa_additional_identification_number
msgid "Additional Identification Number for Seller/Buyer"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "Additional Identification Number is required for commercial partners"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Additional Identification Scheme is required for the Buyer if tax exemption "
"reason is either VATEX-SA-HEA or VATEX-SA-EDU, and its value must be NAT"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Additional Identification Scheme is required for the Seller, and must be one"
" of CRN, MOM, MLS, SAG or OTH"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_res_company__l10n_sa_additional_identification_scheme
#: model:ir.model.fields,help:l10n_sa_edi.field_res_partner__l10n_sa_additional_identification_scheme
#: model:ir.model.fields,help:l10n_sa_edi.field_res_users__l10n_sa_additional_identification_scheme
msgid "Additional Identification scheme for Seller/Buyer"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "Are you sure you wish to re-onboard the Journal?"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_partner__l10n_sa_edi_building_number
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_users__l10n_sa_edi_building_number
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_company_form
msgid "Building Number"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "Building Number for the Buyer is required on Standard Invoices"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_compliance_csid_json
msgid "CCSID JSON"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.l10n_sa_edi_otp_wizard_view_form
msgid "Cancel"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid ""
"Cannot request a Production CSID before completing the Compliance Checks"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Cannot request a Production CSID before requesting a CCSID first"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_tax.py:0
msgid "Cannot set a tax to Retention if the amount is greater than or equal 0"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "City"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__crn
msgid "Commercial Registration Number"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"Complete the Compliance Checks\n"
"                                    <i class=\"fa fa-check text-success ms-1\" invisible=\"not l10n_sa_compliance_checks_passed\"/>"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_compliance_csid_json
msgid ""
"Compliance CSID data received from the Compliance CSID API in dumped json "
"format"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_compliance_checks_passed
msgid "Compliance Checks Done"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Compliance checks can only be run for companies operating from KSA"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Could not complete Compliance Checks for the following file:"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "Could not generate Invoice UBL content: %s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Could not generate PCSID values:\n"
"%(error)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Could not generate signed XML values:\n"
"%(error)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Could not obtain Compliance CSID: %s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Could not obtain Production CSID: %s"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "Country"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_tax__l10n_sa_is_retention
msgid "Determines whether or not a tax counts as a Withholding Tax"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_bank_statement_line__l10n_sa_uuid
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_move__l10n_sa_uuid
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_payment__l10n_sa_uuid
msgid "Document UUID (SA)"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Errors:"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_tax__l10n_sa_exemption_reason_code
msgid "Exemption Reason Code"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/wizard/account_move_reversal.py:0
msgid ""
"For Credit/Debit notes issued in Saudi Arabia, you need to specify a Reason"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/wizard/account_debit_note.py:0
msgid "For debit notes issued in Saudi Arabia, you need to specify a Reason"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__gcc
msgid "GCC ID"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_latest_submission_hash
msgid ""
"Hash of the latest submitted invoice to be used as the Previous Invoice Hash"
" (KSA-13)"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "ICV"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_additional_identification_number
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_partner__l10n_sa_additional_identification_number
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_users__l10n_sa_additional_identification_number
msgid "Identification Number (SA)"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_additional_identification_scheme
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_partner__l10n_sa_additional_identification_scheme
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_users__l10n_sa_additional_identification_scheme
msgid "Identification Scheme"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_move.py:0
msgid "Invoice Successfully Submitted to ZATCA"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Invoice could not be cleared:\n"
"%s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"Invoice could not be reported:\n"
"%s"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_bank_statement_line__l10n_sa_chain_index
#: model:ir.model.fields,help:l10n_sa_edi.field_account_move__l10n_sa_chain_index
#: model:ir.model.fields,help:l10n_sa_edi.field_account_payment__l10n_sa_chain_index
msgid ""
"Invoice index in chain, set if and only if an in-chain XML was submitted and"
" did not error"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Invoice submission to ZATCA returned errors"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_move.py:0
msgid "Invoice was Accepted by ZATCA (with Warnings)"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_move.py:0
msgid "Invoice was rejected by ZATCA"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__iqa
msgid "Iqama Number"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_tax__l10n_sa_is_retention
msgid "Is Retention"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "JSON response from ZATCA could not be decoded"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_journal
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__journal_id
msgid "Journal"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Journal could not be onboarded"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"Journal could not be onboarded. Please make sure the Company "
"VAT/Identification Number are correct."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_api_mode
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_config_settings__l10n_sa_api_mode
msgid "L10N Sa Api Mode"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_csr
msgid "L10N Sa Csr"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_edi_building_number
msgid "L10N Sa Edi Building Number"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_edi_plot_identification
msgid "L10N Sa Edi Plot Identification"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_latest_submission_hash
msgid "Latest Submission Hash"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__mls
msgid "MLSD License"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__mom
msgid "Momra License"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "N/A"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__nat
msgid "National ID"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "Neighborhood"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "Neighborhood for the Buyer is required on Standard Invoices"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "Neighborhood for the Seller is required on Standard Invoices"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__l10n_sa_otp
msgid "OTP"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__l10n_sa_otp
msgid ""
"OTP required to get a CCSID. Can only be acquired through the Fatoora "
"portal."
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "Onboard Journal"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "Onboard the Journal by completing each step"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_csr_errors
msgid "Onboarding Errors"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid ""
"Once you change the submission mode to <strong>Production</strong>, you cannot change it anymore.\n"
"                            Be very careful, as any invoice submitted to ZATCA in Production mode will be accounted for\n"
"                            and might lead to <strong>Fines &amp; Penalties</strong>."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__oth
msgid "Other ID"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_production_csid_validity
msgid "PCSID Expiration"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_production_csid_json
msgid "PCSID JSON"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__l10n_sa_renewal
msgid "PCSID Renewal"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "PIH"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__pas
msgid "Passport ID"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Please, generate a CSR before requesting a CCSID"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid ""
"Please, make a request to obtain the Compliance CSID and Production CSID "
"before sending documents to ZATCA"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid ""
"Please, make sure all the following fields have been correctly set on the "
"Company:%(fields)s"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Please, set a valid OTP to be used for Onboarding"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.l10n_sa_edi_otp_wizard_view_form
msgid ""
"Please, set the OTP you received from ZATCA in the input below then "
"validate."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_partner__l10n_sa_edi_plot_identification
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_users__l10n_sa_edi_plot_identification
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_company_form
msgid "Plot Identification"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_company__l10n_sa_api_mode__prod
msgid "Production"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_production_csid_json
msgid ""
"Production CSID data received from the Production CSID API in dumped json "
"format"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_production_csid_validity
msgid "Production CSID expiration date"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid ""
"Production certificate has expired, please renew the PCSID before proceeding"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "QR"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "Re-Onboard"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_move_reversal_inherit_l10n_sa_edi
msgid "Reason"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "Renew Production CSID"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.l10n_sa_edi_otp_wizard_view_form
msgid "Request"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_l10n_sa_edi_otp_wizard
msgid "Request ZATCA OTP"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.actions.act_window,name:l10n_sa_edi.l10n_sa_edi_otp_wizard_act_window
msgid "Request a CSID"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"Request a Compliance Certificate (CCSID)\n"
"                                    <i class=\"fa fa-check text-success ms-1\" invisible=\"not l10n_sa_compliance_csid_json\" groups=\"base.group_system\"/>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"Request a Production Certificate (PCSID)\n"
"                                    <i class=\"fa fa-check text-success ms-1\" invisible=\"not l10n_sa_production_csid_json\" groups=\"base.group_system\"/>"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__sag
msgid "Sagia License"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_company__l10n_sa_api_mode__sandbox
msgid "Sandbox"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_serial_number
msgid "Serial Number"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Server returned an unexpected error: %(error)s"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid ""
"Set a Serial Number for your device\n"
"                                    <i class=\"fa fa-check text-success ms-1\" invisible=\"not l10n_sa_serial_number\"/>"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid "Set whether the system should use the Production API"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "Simplified Tax Invoice"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_company__l10n_sa_api_mode__preprod
msgid "Simulation (Pre-Production)"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_compliance_checks_passed
msgid "Specifies if the Compliance Checks have been completed successfully"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_res_company__l10n_sa_api_mode
#: model:ir.model.fields,help:l10n_sa_edi.field_res_config_settings__l10n_sa_api_mode
msgid "Specifies which API the system should use"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid "State / Country subdivision"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "State..."
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "Street"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_tax__l10n_sa_exemption_reason_code
msgid "Tax Exemption Reason Code (ZATCA)"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__res_partner__l10n_sa_additional_identification_scheme__tin
msgid "Tax Identification Number"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "Tax Invoice"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_csr
msgid ""
"The Certificate Signing Request that is submitted to the Compliance API"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid ""
"The Production CSID is still valid. You can only renew it once it has "
"expired."
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "The Production certificate is valid until"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_move.py:0
msgid ""
"The invoice was accepted by ZATCA, but returned warnings. Please, check the "
"response below:"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_move.py:0
msgid "The invoice was rejected by ZATCA. Please, check the response below:"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_res_company__l10n_sa_private_key
msgid "The private key used to generate the CSR and obtain certificates"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_journal__l10n_sa_serial_number
msgid "The serial number of the Taxpayer solution unit. Provided by ZATCA"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model,name:l10n_sa_edi.model_account_edi_xml_ubl_21_zatca
msgid "UBL 2.1 (ZATCA)"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_account_bank_statement_line__l10n_sa_uuid
#: model:ir.model.fields,help:l10n_sa_edi.field_account_move__l10n_sa_uuid
#: model:ir.model.fields,help:l10n_sa_edi.field_account_payment__l10n_sa_uuid
msgid "Universally unique identifier of the Invoice"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_bank_statement_line__l10n_sa_invoice_signature
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_move__l10n_sa_invoice_signature
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_payment__l10n_sa_invoice_signature
msgid "Unsigned XML Signature"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.l10n_sa_edi_otp_wizard_view_form
msgid "Use an OTP to request for a CSID"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,help:l10n_sa_edi.field_l10n_sa_edi_otp_wizard__l10n_sa_renewal
msgid ""
"Used to decide whether we should call the PCSID renewal API or the CCSID API"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_edi_format.py:0
msgid ""
"VAT is required when Identification Scheme is set to Tax Identification "
"Number"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-29
msgid ""
"VATEX-SA-29 Financial services mentioned in Article 29 of the VAT "
"Regulations."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-29-7
msgid ""
"VATEX-SA-29-7 Life insurance services mentioned in Article 29 of the VAT."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-30
msgid ""
"VATEX-SA-30 Real estate transactions mentioned in Article 30 of the VAT "
"Regulations."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-32
msgid "VATEX-SA-32 Export of goods."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-33
msgid "VATEX-SA-33 Export of Services."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-34-1
msgid "VATEX-SA-34-1 The international transport of Goods."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-34-2
msgid "VATEX-SA-34-1 The international transport of Passengers."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-34-3
msgid ""
"VATEX-SA-34-3 Services directly connected and incidental to a Supply of "
"international passenger transport."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-34-4
msgid "VATEX-SA-34-4 Supply of a qualifying means of transport."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-34-5
msgid ""
"VATEX-SA-34-5 Any services relating to Goods or passenger transportation, as"
" defined in article twenty five of these Regulations."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-35
msgid "VATEX-SA-35 Medicines and medical equipment."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-36
msgid "VATEX-SA-36 Qualifying metals."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-edu
msgid "VATEX-SA-EDU Private education to citizen."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-hea
msgid "VATEX-SA-HEA Private healthcare to citizen."
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields.selection,name:l10n_sa_edi.selection__account_tax__l10n_sa_exemption_reason_code__vatex-sa-oos
msgid "VATEX-SA-OOS Not subject to VAT."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "Warnings:"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid ""
"You can select the API used for submissions down below. There are three modes available: Sandbox, Pre-Production and Production.\n"
"                            Once you have selected the correct API, you can start the Onboarding process by going to the Journals and checking the options under the ZATCA tab."
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/res_company.py:0
msgid ""
"You cannot change the ZATCA Submission Mode once it has been set to "
"Production"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/wizard/l10n_sa_edi_otp_wizard.py:0
msgid "You need to provide an OTP to be able to request a CCSID"
msgstr ""

#. module: l10n_sa_edi
#. odoo-python
#: code:addons/l10n_sa_edi/models/account_journal.py:0
msgid "You need to request the CCSID first before you can proceed"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.view_account_journal_form
msgid "ZATCA"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid "ZATCA E-Invoicing Settings"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_res_company__l10n_sa_private_key
msgid "ZATCA Private key"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_journal__l10n_sa_chain_sequence_id
msgid "ZATCA account.move chain sequence"
msgstr ""

#. module: l10n_sa_edi
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_bank_statement_line__l10n_sa_chain_index
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_move__l10n_sa_chain_index
#: model:ir.model.fields,field_description:l10n_sa_edi.field_account_payment__l10n_sa_chain_index
msgid "ZATCA chain index"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.res_config_settings_view_form
msgid "ZATCA specific settings for Saudi eInvoicing"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.sa_partner_address_form
msgid "ZIP"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
msgid "not(//ancestor-or-self::cac:AdditionalDocumentReference[cbc:ID='QR'])"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
msgid "not(//ancestor-or-self::cac:Signature)"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
msgid "not(//ancestor-or-self::ext:UBLExtensions)"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "urn:oasis:names:specification:ubl:dsig:enveloped:xades"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
msgid "urn:oasis:names:specification:ubl:signature:1"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.export_sa_zatca_ubl_extensions
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_CreditNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_DebitNoteType_zatca
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.ubl_21_InvoiceType_zatca
msgid "urn:oasis:names:specification:ubl:signature:Invoice"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "فاتورة ضريبية"
msgstr ""

#. module: l10n_sa_edi
#: model_terms:ir.ui.view,arch_db:l10n_sa_edi.arabic_english_invoice
msgid "فاتورة ضريبية مبسطة"
msgstr ""
