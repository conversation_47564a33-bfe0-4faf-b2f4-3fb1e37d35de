# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_gelato
# 
# Translators:
# Lasse L, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:24+0000\n"
"PO-Revision-Date: 2025-02-09 01:00+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_gelato
#: model:mail.template,body_html:sale_gelato.order_status_update
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <!-- Order in transit body -->\n"
"                    <t t-if=\"ctx.get('tracking_data')\">\n"
"                        We are glad to inform you that your order is in transit.\n"
"                        <t t-if=\"len(ctx['tracking_data']) == 1\">\n"
"                            <t t-set=\"tracking_url\" t-value=\"list(ctx['tracking_data'].keys())[0]\"/>\n"
"                            Your tracking number is <a t-attf-href=\"tracking_url\" t-out=\"ctx['tracking_data'][tracking_url]\"/>.\n"
"                            <br/><br/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Your tracking numbers are:\n"
"                            <ul>\n"
"                                <li t-foreach=\"ctx['tracking_data']\" t-as=\"tracking_url\">\n"
"                                    <a t-attf-href=\"{{tracking_url}}\" t-out=\"ctx['tracking_data'][tracking_url]\"/>\n"
"                                </li>\n"
"                            </ul>\n"
"                        </t>\n"
"                    </t>\n"
"                    <!-- Order delivered body -->\n"
"                    <t t-if=\"ctx.get('order_delivered')\">\n"
"                        We are glad to inform you that your order has been delivered.\n"
"                        <br/><br/>\n"
"                    </t>\n"
"                    Thank you,\n"
"                    <t t-if=\"object.user_id.name\" data-o-mail-quote-container=\"1\">\n"
"                        <br/>\n"
"                        <t t-out=\"object.user_id.name or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </t>\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid ""
"<i title=\"All print images are set\" invisible=\"gelato_missing_images\" class=\"fa fa-check-circle text-success fs-3 me-3\"/>\n"
"                        <i title=\"Some print images are missing\" invisible=\"not gelato_missing_images\" class=\"fa fa-question-circle text-danger fs-3 me-3\"/>"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.res_config_settings_form
msgid "API Key"
msgstr "API-nyckel"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_res_config_settings
msgid "Config Settings"
msgstr "Inställningar"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/utils.py:0
msgid "Could not establish the connection to the Gelato API."
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/product_template.py:0
msgid "Could not synchronize with Gelato"
msgstr ""

#. module: sale_gelato
#: model:delivery.carrier,name:sale_gelato.express_delivery
#: model:ir.model.fields.selection,name:sale_gelato.selection__delivery_carrier__gelato_shipping_service_type__express
msgid "Express Delivery"
msgstr ""

#. module: sale_gelato
#: model:product.template,name:sale_gelato.express_delivery_product_product_template
msgid "Express Delivery (Gelato)"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_res_config_settings__module_sale_gelato
#: model:ir.model.fields.selection,name:sale_gelato.selection__delivery_carrier__delivery_type__gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_product_easy_form
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_product_normal_form
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid "Gelato"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_res_company__gelato_api_key
#: model:ir.model.fields,field_description:sale_gelato.field_res_config_settings__gelato_api_key
msgid "Gelato API Key"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_product__gelato_image_ids
#: model:ir.model.fields,field_description:sale_gelato.field_product_template__gelato_image_ids
msgid "Gelato Print Images"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_template__gelato_product_uid
msgid "Gelato Product UID"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_product__gelato_product_uid
msgid "Gelato Product Uid"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_delivery_carrier__gelato_shipping_service_type
msgid "Gelato Shipping Service Type"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_product__gelato_template_ref
#: model:ir.model.fields,field_description:sale_gelato.field_product_template__gelato_template_ref
msgid "Gelato Template Reference"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_res_company__gelato_webhook_secret
#: model:ir.model.fields,field_description:sale_gelato.field_res_config_settings__gelato_webhook_secret
msgid "Gelato Webhook Secret"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/controlers/main.py:0
msgid ""
"Gelato could not proceed with the fulfillment of order %(order_reference)s: "
"%(gelato_message)s"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/controlers/main.py:0
msgid "Gelato has returned order %(reference)s."
msgstr ""

#. module: sale_gelato
#: model:mail.template,name:sale_gelato.order_status_update
msgid "Gelato: Order status update"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_document__is_gelato
msgid "Is Gelato"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.res_config_settings_form
msgid "Manage Delivery Methods"
msgstr ""

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_product_product__gelato_missing_images
#: model:ir.model.fields,field_description:sale_gelato.field_product_template__gelato_missing_images
msgid "Missing Print Images"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/product_template.py:0
msgid "Missing product variants and images have been successfully created."
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.res_config_settings_form
msgid "Place orders through Gelato's print-on-demand service"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid "Print Images"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/product_document.py:0
msgid "Print images must be set on products before they can be ordered."
msgstr ""

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_product_document
msgid "Product Document"
msgstr "Produktdokument"

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_product_easy_form
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_product_normal_form
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid "Product UID"
msgstr ""

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: sale_gelato
#: model:ir.model.fields,field_description:sale_gelato.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Leverantör"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_sale_order
msgid "Sales Order"
msgstr "Försäljningsorder"

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_sale_order_line
msgid "Sales Order Line"
msgstr "Orderrad"

#. module: sale_gelato
#: model:mail.template,description:sale_gelato.order_status_update
msgid "Sent to the customer when Gelato updates the status of an order"
msgstr ""

#. module: sale_gelato
#: model:ir.model,name:sale_gelato.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Leveranssätt"

#. module: sale_gelato
#: model:delivery.carrier,name:sale_gelato.standard_delivery
#: model:ir.model.fields.selection,name:sale_gelato.selection__delivery_carrier__gelato_shipping_service_type__normal
msgid "Standard Delivery"
msgstr ""

#. module: sale_gelato
#: model:product.template,name:sale_gelato.standard_delivery_product_product_template
msgid "Standard Delivery (Gelato)"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/product_template.py:0
msgid "Successfully synchronized with Gelato"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid "Synchronize"
msgstr "Synkronisera"

#. module: sale_gelato
#: model:ir.model.fields,help:sale_gelato.field_product_product__gelato_template_ref
#: model:ir.model.fields,help:sale_gelato.field_product_template__gelato_template_ref
msgid "Synchronize to fetch variants from Gelato"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.product_template_form
msgid "Template Reference"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/delivery_carrier.py:0
msgid "The delivery method is not available for this order."
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/delivery_carrier.py:0
msgid "The following required address fields are missing: %s"
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/sale_order.py:0
msgid "The order has been successfully passed on Gelato."
msgstr ""

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/sale_order.py:0
msgid ""
"The order with reference %(order_reference)s was not sent to Gelato.\n"
"Reason: %(error_message)s"
msgstr ""

#. module: sale_gelato
#: model_terms:ir.ui.view,arch_db:sale_gelato.res_config_settings_form
msgid "Webhook Secret"
msgstr "Webhook hemlighet"

#. module: sale_gelato
#. odoo-python
#: code:addons/sale_gelato/models/sale_order.py:0
msgid ""
"You cannot mix Gelato products with non-Gelato products in the same order."
msgstr ""

#. module: sale_gelato
#: model:mail.template,subject:sale_gelato.order_status_update
msgid "{{ object.reference }}"
msgstr ""
