# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-06 14:58+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_type.py:0
msgid "%s hours available"
msgstr "%s時間利用可能 "

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "休暇累積プランレベル"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_attendance
msgid "Attendance"
msgstr "勤怠"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__attendance
msgid "Attendances"
msgstr "勤怠"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Available"
msgstr "処理可能"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__calendar
msgid "Calendar"
msgstr "カレンダー"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Days"
msgstr "日"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr "時間外労働の控除"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "破棄"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "時間外労働"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr "時間外労働時間利用可能"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid "Frequency Hourly Source"
msgstr "頻度 毎時ソース"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Hours"
msgstr "時間"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid ""
"If the source is set to \"Calendar\", the amount of worked hours will be "
"computed based on the Employee's working schedule. Otherwise, the amount of "
"worked hours will be based on Attendance records."
msgstr ""
"ソースが "
"\"カレンダ\"に設定されている場合、労働時間数は従業員の勤務スケジュールに基づいて計算されます。そうでない場合、勤務時間は勤怠記録に基づき計算されます。"

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "休暇割当申請"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr "このタイプの休暇が承認されると、勤務時間の超過が差し引かれます。"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"Only an Officer or Administrator is allowed to edit the allocation duration "
"in this status."
msgstr "このステータスでは、割当期間の編集は、役員または管理者のみが行うことができます。"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr "残業控除"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr "残業申請"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "保存"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_accrual_level_view_form
msgid "Source"
msgstr "情報源"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr "この割当を申請するには、従業員の時間外労働時間が不足しています。"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to extend this leave."
msgstr "この休暇を延長するには、従業員の時間外労働時間が不足しています。"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to request this leave."
msgstr "この休暇を申請するには、従業員の時間外労働時間が不足しています。"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr "この休暇を申請するには、従業員の時間外労働時間が不足しています。"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "休暇"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "休暇割当"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "休暇タイプ"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "合計残業時間"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "You do not have enough extra hours to request this leave"
msgstr "この休暇を申請するには、時間外労働時間が不足しています。"
