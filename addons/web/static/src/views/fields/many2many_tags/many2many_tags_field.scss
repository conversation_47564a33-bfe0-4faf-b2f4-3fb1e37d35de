.o_tag_popover {
    max-width: 150px;
}

.o_tag_popover label {
    line-height: $o-line-height-base;
}

.o_field_widget.o_field_many2many_tags {
    flex-flow: row wrap;
    margin: var(--Tags-margin, 0);

    .o_tags_input {
        padding: 1px 0;
    }

    .o_field_many2many_selection {
        flex: 1 0 50px;

        .o_input {
            height: 100%;
            border: none;
        }
    }

    .badge {
        flex: 0 0 auto;
        display: flex;

        &.dropdown {
            cursor: pointer;
        }

        a {
            color: inherit;
        }

        .o_badge_text, .o_tag_badge_text {
            @include o-text-overflow(inline-block);
            max-width: var(--Tag-max-width, 200px);
        }
    }

    .dropdown-item-selected > a {
        font-weight: bold !important;
    }
}

.o_field_widget.o_field_many2many_tags, .o_field_widget.o_field_many2many_tags_avatar {
    .o_field_many2many_selection .o_input {
        --o-input-background-color: transparent;
    }
}

.o_list_view .o_field_widget.o_field_many2many_tags {
    .o_tags_input {
        border: 0;
        padding: 0;
    }

    .o_field_many2many_selection {
        flex-basis: 40px;
    }
}

.o_form_view {
    .o_group, .o_inner_group {
        .o_field_tags {
            width: 100%;
        }
    }

    &:not(.o_field_highlight) {
        .o_field_many2many_selection {
            .o_dropdown_button {
                visibility: hidden;
            }

            &:hover, &:focus-within {
                .o_dropdown_button {
                    visibility: visible;
                }
            }
        }
    }
}

.o_form_statusbar .o_field_tags {
    align-self: center;
}
