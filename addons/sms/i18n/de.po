# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# Ungültige Empfänger"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "# Gültige Empfänger"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid ""
"%(count)s out of the %(total)s selected SMS Text Messages have successfully "
"been resent."
msgstr ""
"%(count)s von %(total)s ausgewählten SMS-Nachrichten konnten erfolgreich "
"erneut versandt werden."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "%s invalid recipients"
msgstr "%s ungültige Empfänger"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "******-555-555"
msgstr "******-555-555"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid ", fits in"
msgstr ", passt in"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> An error occurred with your account. Please "
"contact the support for more information..."
msgstr ""
"<i class=\"fa fa-warning\"/> Bei Ihrem Konto ist ein Fehler aufgetreten. "
"Bitte kontaktieren Sie den Support für weitere Informationen ..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> You cannot send SMS while your account is not "
"registered."
msgstr ""
"<i class=\"fa fa-warning\"/> Sie können keine SMS versenden, wenn Ihr Konto "
"nicht registriert ist."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Register"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Registrieren"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Set Sender Name"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Name des Absenders festlegen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Kontextaktion</span>\n"
"                                <span class=\"o_stat_text\">hinzufügen</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "<span class=\"o_stat_text\">Preview</span>"
msgstr "<span class=\"o_stat_text\">Vorschau</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Kontextaktion</span>\n"
"                                <span class=\"o_stat_text\">entfernen</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "<span class=\"text-warning\" invisible=\"not no_record\">No records</span>"
msgstr ""
"<span class=\"text-warning\" invisible=\"not no_record\">Keine "
"Datensätze</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                The message will be sent as an SMS to the recipients of the template\n"
"                                and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                The SMS will not be sent, it will only be posted as an\n"
"                                internal note in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                The SMS will be sent as an SMS to the recipients of the\n"
"                                template and it will also be posted as an internal note\n"
"                                in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                Die Nachricht wird als E-Mail an die Empfänger der\n"
"                                Vorlage gesendet und erscheint nicht im Nachrichtenverlauf.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                Die Nachricht wird als interne Notiz hinterlassen, die für\n"
"                                interne Benutzer im Nachrichtenverlauf sichtbar ist.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                Die Nachricht wird als Nachricht im Datensatz hinterlassen\n"
"                                und alle Follower werden benachrichtigt. Sie erscheint im Nachrichtenverlauf.\n"
"                            </span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "<span> or to specify the country code.</span>"
msgstr "<span> eingestellt oder den Ländercode bestimmt haben.</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"<strong>Invalid number:</strong>\n"
"                        <span> make sure to set a country on the </span>"
msgstr ""
"<strong>Ungültige Nummer:</strong>\n"
"                        <span> Vergewissern Sie sich, dass sie ein Land für </span>"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_tracker_sms_uuid_unique
msgid "A record for this UUID already exists"
msgstr "Es besteht bereits eine UUID für diesen Datensatz"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__account_id
msgid "Account"
msgstr "Konto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr ""
"Hinzufügen einer kontextbezogenen Aktion auf dem zugehörigen Modell, um "
"einen SMS-Verfasser mit dieser Vorlage zu öffnen"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__uuid
msgid "Alternate way to identify a SMS record, used for delivery reports"
msgstr ""
"Alternatives Verfahren zur Identifizierung eines SMS-Datensatzes, verwendet "
"für Zustellungsberichte"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS"
msgstr "Ein Fehler ist beim SMS-Versand aufgetreten"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS on “%(record_name)s”"
msgstr "Beim Senden einer SMS an „%(record_name)s“ ist ein Fehler aufgetreten"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"An unknown error occurred. Please contact Odoo support if this error "
"persists."
msgstr ""
"Ein unbekannter Fehler ist aufgetreten. Bitte kontaktieren Sie den Odoo-"
"Support, wenn der Fehler weiterhin besteht."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "Gilt für"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid ""
"Are you sure you want to reset these sms templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Sind Sie sicher, dass Sie diese SMS-Vorlagen auf ihre ursprüngliche "
"Konfiguration zurücksetzen wollen? Änderungen und Übersetzungen gehen dann "
"verloren."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Auf der schwarzen Liste"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Telefon auf der schwarzen Liste ist Mobiltelefon"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Telefon auf der schwarzen Liste Ist Festnetz"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "Nachrichtentext"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "Guthaben kaufen"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Buy credits."
msgstr "Guthaben kaufen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_cancel
msgid "Can Cancel"
msgstr "Darf abbrechen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_resend
msgid "Can Resend"
msgstr "Darf erneut senden"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "Wählen Sie eine Sprache:"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid "Choose a template..."
msgstr "Eine Vorlage auswählen ..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "Wählen Sie ein Beispiel"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_code.py:0
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Choose your sender name"
msgstr "Wählen Sie einen Namen für den Absender"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "Schließen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "Schreibmodus"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "Kontakt"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "Inhalt"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_country_not_supported
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "Land nicht unterstützt"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_registration_needed
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "Länderspezifische Registrierung erforderlich"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Country-specific registration required."
msgstr "Länderspezifische Registrierung erforderlich."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: sms
#: model:iap.service,unit_name:sms.iap_service_sms
msgid "Credits"
msgstr "Guthaben"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "Kunde: automatische SMS"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "Hallo {{ object.display_name }} dies ist eine automatische SMS."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Delivered"
msgstr "Zugestellt"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "Verwerfen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__display_name
#: model:ir.model.fields,field_description:sms.field_sms_tracker__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "Dokument-Follower"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "Dokument-ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "Dokument-IDs"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model_description
msgid "Document Model Description"
msgstr "Beschreibung des Dokumentmodells"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "Name des Dokumentmodells"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "Duplizieren"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Partner bearbeiten"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "E-Mail-Thread"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/phone_field/phone_field.js:0
msgid "Enable SMS"
msgstr "SMS aktivieren"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Enter a phone number to get an SMS with a verification code."
msgstr ""
"Geben Sie eine Telefonnummer ein, um eine SMS mit einem Verifizierungscode "
"zu erhalten."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "Fehler"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Error Message"
msgstr "Fehlermeldung"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_expired
msgid "Expired"
msgstr "Abgelaufen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "Fehlertyp"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Fehlertyp"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Feld zum Speichern der bereinigten Telefonnummer. Hilft, die Suche und den "
"Vergleich zu beschleunigen."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""
"Datei, aus der die Vorlage stammt. Verwendet, um beschädigte Vorlagen "
"wiederherzustellen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Following numbers are not correctly encoded: %s"
msgstr "Die folgenden Zahlen sind nicht korrekt codiert: %s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "Hat nicht ausreichend Guthaben"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "Hat SMS-Fehler"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "Hat nichtregistriertes Konto"

#. module: sms
#: model:ir.model,name:sms.model_iap_account
msgid "IAP Account"
msgstr "IAP-Konto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__id
#: model:ir.model.fields,field_description:sms.field_sms_tracker__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Wenn die bereinigte Rufnummer auf der schwarzen Liste steht, erhält der "
"Kontakt keine Massen-SMS mehr, egal von welcher Liste"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Alle ignorieren"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "In der Warteschlange"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Zeigt an, ob eine bereinigte Rufnummer auf der schwarzen Liste eine "
"Mobilfunknummer ist. Hilft bei der Unterscheidung, welche Nummer auf der "
"schwarzen Liste steht, wenn es in einem Modell sowohl ein Handy- als auch "
"ein Telefonfeld gibt."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Zeigt an, ob eine bereinigte Rufnummer auf der schwarzen Liste eine "
"Teleofnnummer ist. Hilft bei der Unterscheidung, welche Nummer auf der "
"schwarzen Liste steht, wenn es in einem Modell sowohl ein Handy- als auch "
"ein Telefonfeld gibt."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr ""
"Gibt an, ob der Verfasser der SMS einen spezifischen Empfänger anspricht"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Unzureichendes Guthaben"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "Ungültiges Ziel"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Invalid phone number. Please make sure to follow the international format, "
"i.e. a plus sign (+), then country code, city code, and local phone number. "
"For example: ******-555-555"
msgstr ""
"Ungültige Telefonnummer. Bitte achten Sie darauf, dass Sie das "
"internationale Format einhalten, d. h. ein Pluszeichen (+), dann die "
"Landesvorwahl, die Ortsvorwahl und die lokale Telefonnummer. Zum Beispiel: "
"******-555-555"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Invalid recipient number. Please update it."
msgstr "Ungültige Empfängernummer. Bitte aktualisieren Sie sie."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "Ist gültig"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "Notiz im Dokument hinterlassen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "Sprache"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: sms
#: model:ir.model,name:sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "SMS mit Mailing/SMS-Verfolgungsmodellen verbinden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "Mail-Nachricht"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__mail_notification_id
msgid "Mail Notification"
msgstr "Mail-Benachrichtigung"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "Mail-Thread SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__to_delete
msgid "Marked for deletion"
msgstr "Zur Löschung markiert"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "Nachricht"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "Benachrichtigungen über Nachrichten"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Fehlende Nummer"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "Modelle"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "Name"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "Kein Datensatz"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "Nicht erlaubt"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "Nicht geliefert"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__note
msgid "Note only"
msgstr "Nur Notiz"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Note that this is not required, if you don't set a sender name, your SMS "
"will be sent from a short code."
msgstr ""
"Beachten Sie, dass dies nicht erforderlich ist. Wenn Sie keinen "
"Absendernamen angeben, wird Ihre SMS von einem Kurzzeichen gesendet."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "Benachrichtigung"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Benachrichtigungstyp"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "Nummer"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "Nummernfeld"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr ""
"Anzahl der Empfänger, die die SMS erhalten, wenn sie im Massenmodus "
"versendet wird, ohne den Wert für die Aktive Domain anzuwenden"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "Abgemeldet"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Optionale Übersetzung (ISO-Code) zur Auswahl beim E-Mail-Versand. Falls es "
"keinen Eintrag gibt, wird die englische Version verwendet. Es sollte sich "
"normalerweise um einen Platzhalterausdruck handeln, der die passende Sprache"
" enthält, z. B. {{ object.partner_id.lang }}."

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "Ausgehende SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "Partner"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Telefon auf der schwarzen Liste"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__phone_number
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
msgid "Phone Number"
msgstr "Telefonnummer"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"Phone number of the recipient. If changed, it will be recorded on "
"recipient's profile."
msgstr ""
"Telefonnummer des Empfängers. Wenn diese geändert wird, wird sie in das "
"Profil des Empfängers aufgenommen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Telefon/Mobil"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "In einem Dokument hinterlassen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "Vorschau von"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid "Proceed"
msgstr "Fortfahren"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__process
msgid "Processing"
msgstr "Verarbeitung"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "In Warteschlange setzen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "Begründung"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "Empfänger"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
msgid "Recipient Name"
msgstr "Empfängername"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "Empfängernummer"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "Empfänger"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "Empfänger (Nummern)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "Empfänger (Partner)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "Referenz des Datensatzes"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
msgid "Register"
msgstr "Anmelden"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_phone.py:0
msgid "Register Account"
msgstr "Konto registrieren"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Register now."
msgstr "Jetzt registrieren"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Register your SMS account"
msgstr "Registrieren Sie Ihr SMS-Konto"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_rejected
msgid "Rejected"
msgstr "Abgelehnt"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "Zugehöriges Dokumentmodell"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "Entfernen Sie die kontextbezogene Aktion des zugehörigen Modells"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "Rendering-Modell"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
msgid "Resend"
msgstr "Erneut senden"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "Benachrichtigung erneut versenden"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_reset_action
msgid "Reset SMS Template"
msgstr "SMS-Vorlage zurücksetzen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Reset Template"
msgstr "Vorlage zurücksetzen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "Erneut versuchen"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.xml:0
#: code:addons/sms/static/src/core/notification_model_patch.js:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "SMS"
msgstr "SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS ("
msgstr "SMS ("

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__comment
msgid "SMS (with note)"
msgstr "SMS (mit Notiz)"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__sms
msgid "SMS (without note)"
msgstr "SMS (ohne Notiz)"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_phone
msgid "SMS Account Registration Phone Number Wizard"
msgstr "Assistent für Telefonnummern zur Registrierung von SMS-Konten"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_sender
msgid "SMS Account Sender Name Wizard"
msgstr "Assistent für Absendername für SMS-Konten"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_code
msgid "SMS Account Verification Code Wizard"
msgstr "Assistent für Verifizierungscode für SMS-Konten"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failure: %(modelName)s"
msgstr "SMS-Fehler: %(modelName)s"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failures"
msgstr "SMS-Fehler"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id_int
msgid "SMS ID"
msgstr "SMS-ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "SMS-Nummer"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "SMS-Vorschau"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS Pricing"
msgstr "SMS-Preisgestaltung"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "SMS erneut versenden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "SMS-Status"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "SMS-Vorlage"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "Vorschau der SMS-Vorlage"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_reset
msgid "SMS Template Reset"
msgstr "Zurücksetzung der SMS-Vorlage"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "SMS-Vorlagen"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_template_reset.py:0
msgid "SMS Templates have been reset"
msgstr "SMS-Vorlagen wurden zurückgesetzt"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_tracker_ids
msgid "SMS Trackers"
msgstr "SMS-Tracker"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "SMS-Inhalt"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "SMS template model of %(action_name)s does not match action model."
msgstr ""
"SMS-Vorlagenmodell %(action_name)s entspricht nicht dem Aktionsmodell."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__sms_tracker_id
msgid "SMS trackers"
msgstr "SMS-Tracker"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__sms_uuid
msgid "SMS uuid"
msgstr "SMS-UUID"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
msgid "SMS: SMS Queue Manager"
msgstr "SMS: SMS-Warteschlangenmanager"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "Bereinigte Nummer"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "SMS-Vorlagen suchen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Send & Close"
msgstr "Senden & Schließen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "Jetzt senden"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "SMS versenden"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "Send SMS (%s)"
msgstr "SMS versenden (%s)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_method
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_method
msgid "Send SMS As"
msgstr "SMS versenden als"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Assistent zum Senden von SMS"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "SMS im Stapel versenden"

#. module: sms
#: model:iap.service,description:sms.iap_service_sms
msgid "Send SMS to your contacts directly from your database."
msgstr "Versenden Sie SMS an Ihre Kontakte direkt aus der Datenbank."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "Eine SMS versenden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "Sofort senden"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "An Nummern senden"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Send verification code"
msgstr "Verifizierungscode versenden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_iap_account__sender_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__sender_name
msgid "Sender Name"
msgstr "Absender Name"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "Sendefehler"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "Sending SMS can only be done on a mail.thread or a transient model"
msgstr ""
"Der Versand von SMS kann nur mit einem mail.thread- oder Übergangsmodell "
"erfolgen."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__pending
msgid "Sent"
msgstr "Gesendet"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "Serveraktion"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "Serverfehler"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Set sender name"
msgstr "Absendername festlegen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "Ein Konto einrichten"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "Seitenleistenaktion"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Aktion der Seitenleiste, um diese Vorlage für alle Datensätze des "
"zugehörigen Dokumentmodells verfügbar zu machen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "Einzelmodus"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Skip for now"
msgstr "Jetzt überspringen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "SMS erneut versenden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "SMS-Vorlage"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "Gespeicherte Empfängernummer"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Success"
msgstr "Erfolgreich"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__template_ids
msgid "Template"
msgstr "Vorlage"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__template_fs
msgid "Template Filename"
msgstr "Dateiname der Vorlage"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "Vorlagenvorschau"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "Vorlagenvorschau Sprache"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "Vorlagen"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"The SMS Service is currently unavailable for new users and new accounts "
"registrations are suspended."
msgstr ""
"Der SMS-Dienst ist derzeit für neue Benutzer nicht verfügbar und die "
"Registrierung neuer Konten ist ausgesetzt."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "The SMS Text Messages could not be resent."
msgstr "Die SMS-Nachricht konnte nicht erneut versendet werden."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The content of the message violates rules applied by our providers."
msgstr ""
"Der Inhalt der Nachricht verstößt gegen die von unseren Anbietern "
"angewandten Regeln."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The destination country is not supported."
msgstr "Das Zielland wird nicht unterstützt."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The number you're trying to reach is not correctly formatted."
msgstr ""
"Die Nummer, die Sie zu erreichen versuchen, ist nicht korrekt formatiert."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Dokumenttyp, mit dem diese Vorlage genutzt werden kann"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The verification code is incorrect."
msgstr "Der Verifizierungscode ist nicht korrekt."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "There are no SMS Text Messages to resend."
msgstr "Es gibt keine erneut zu versendenden SMS-Nachrichten."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This SMS has been removed as the number was already used."
msgstr "Diese SMS wurde entfernt, da die Nummer bereits verwendet wurde."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"This account already has an existing sender name and it cannot be changed."
msgstr ""
"Dieses Konto hat bereits einen bestehenden Absendernamen und kann nicht "
"geändert werden."

#. module: sms
#: model:ir.model.fields,help:sms.field_iap_account__sender_name
msgid "This is the name that will be displayed as the sender of the SMS."
msgstr "Dies ist der Name, der als der Absender der SMS angezeigt wird."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Diese Rufnummer steht auf der schwarzen Liste für SMS-Marketing. Klicken, um"
" sie von der schwarzen Liste zu entfernen."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This phone number/account has been banned from our service."
msgstr ""
"Diese Telefonnummer bzw. dieses Konto wurde von unserem Dienst gesperrt."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
msgid "Try Again"
msgstr "Erneut versuchen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "Typ"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Art der Serveraktion. Die folgenden Werte sind verfügbar:\n"
"- „Einen Datensatz aktualieren“: Aktualisieren Sie die Werte eines Datensatzes.\n"
"- „Aktivität erstellen“: Erstellen Sie eine Aktivität (Dialog)\n"
"- „E-Mail versenden“: Hinterlassen Sie eine Nachricht, oder Notiz oder senden Sie eine E-Mail (Dialog)\n"
"- „SMS versenden“: Senden Sie eine SMS, protokollieren Sie diese in Dokumenten (SMS), log them on documents (SMS)\n"
"- „Follower hinzufügen/entfernen“: Fügen Sie einem Datensatz Follower hinzu oder entfernen Sie sie (Dialog)\n"
"- „Datensatz erstellen“: Erstellen Sie einen neuen Datensatz mit neuen Werten\n"
"- „Code ausführen“: ein Block Python-Code, der ausgeführt wird\n"
"- „Webhook-Benachrichtigung versenden“: Senden Sie eine Anfrage zur VERÖFFENTLICHUNG an ein externes System, auch bekannt als Webhook\n"
"- „Vorhandene Aktionen ausführen“: Legen Sie eine Aktion fest, die mehrere andere Serveraktionen auslöst\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__uuid
msgid "UUID"
msgstr "UUID"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_sms_uuid_unique
msgid "UUID must be unique"
msgstr "UUID muss eindeutig sein."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__unknown
msgid "Unknown error"
msgstr "Unbekannter Fehler"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Nichtregistriertes Konto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "Vorlage verwenden"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "Schwarze Liste verwenden"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Wird verwendet, um den Nachrichtengenerator zu kategorisieren\n"
"„email“: generiert durch eine eingehende E-Mail, z. B. von mailgateway\n"
"„comment“: generiert durch Benutzereingaben, z. B. über Dialog oder Editor\n"
"„email_outgoing“: generiert durch ein Mailing\n"
"„notification“: vom System generiert, z. B. durch Verfolgungsnachrichten\n"
"„auto_comment“: generiert durch einen automatischen Benachrichtigungsmechanismus, z. B. eine Bestätigung\n"
"„user_notification“: für einen bestimmten Empfänger generiert"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__verification_code
msgid "Verification Code"
msgstr "Verifizierungscode"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "Anzahl sichtbarer Datensätze"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Warning"
msgstr "Warnung"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "We were not able to find your account in our database."
msgstr "Wir können Ihr Konto nicht in unserer Datenbank finden."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"We were not able to reach you via your phone number. If you have requested "
"multiple codes recently, please retry later."
msgstr ""
"Wir konnten Sie unter Ihrer Telefonnummer nicht erreichen. Wenn Sie in "
"letzter Zeit mehrere Codes angefordert haben, versuchen Sie es bitte später "
"erneut."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr ""
"Ob dieses Modell Nachrichten und Benachrichtigungen per SMS unterstützt"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__to_delete
msgid ""
"Will automatically be deleted, while notifications will not be deleted in "
"any case."
msgstr ""
"Werden automatisch gelöscht, während Benachrichtigungen in jedem Fall nicht "
"gelöscht werden."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Falsches Zahlenformat"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_resend.py:0
msgid "You do not have access to the message and/or related document."
msgstr ""
"Sie haben keinen Zugriff auf die Nachricht und/oder das zugehörige Dokument."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have an eligible IAP account."
msgstr "Sie haben kein zulässiges IAP-Konto."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have enough credits on your IAP account."
msgstr "Sie haben nicht genügend Guthaben in Ihrem IAP-Konto."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You tried too many times. Please retry later."
msgstr "Sie haben es zu oft versucht. Bitte versuchen Sie es später erneut."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr ""
"Ihre SMS muss mindestens ein Zeichen enthalten, das kein Leerzeichen ist"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_account_code.py:0
msgid "Your SMS account has been successfully registered."
msgstr "Ihr SMS-Konto wurden erfolgreich registriert."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain "
"alphanumeric characters."
msgstr ""
"Ihr Absendername muss zwischen 3 und 11 Zeichen lang sein und darf nur "
"alphanumerische Zeichen enthalten."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.\n"
"                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully."
msgstr ""
"Ihr Absendername muss zwischen 3 und 11 Zeichen lang sein und darf nur alphanumerische Zeichen enthalten.\n"
"                        Er muss zum Namen Ihres Unternehmens passen und darf nach der Registrierung nicht mehr geändert werden. Wählen Sie ihn also sorgfältig aus."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Your sms account has not been activated yet."
msgstr "Ihr SMS-Konto wurde noch nicht aktivivert."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "characters"
msgstr "Zeichen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "z. B. +1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "z. B. Kalendererinnerung"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "z. B. Kontakt"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "z. B. en_US oder {{ object.partner_id.lang }}"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "out of"
msgstr "von"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients have an invalid phone number and will not receive this text "
"message."
msgstr ""
"Empfängern haben eine ungültige Telefonnummer und werden diese SMS nicht "
"erhalten."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "Datensatz:"
