# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_group
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid ") <span invisible=\"not send_email\">and send them an email</span>."
msgstr ")<span invisible=\"not send_email\"> 並向他們發送電子郵件</span>。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "<br/> You'll be notified as soon as some new content is posted."
msgstr "<br/>有新內容發佈時，你便會即時收到通知。"

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_subscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                You have requested to be subscribed to the mailing list <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>\n"
"                <br/><br/>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                你好！<br/><br/>\n"
"                你已要求訂閱郵寄清單 <strong t-out=\"object.name or ''\"/>。\n"
"                <br/><br/>\n"
"                請到以下網址確認： <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>\n"
"                <br/><br/>\n"
"                如果這是錯誤，或你沒有請求此操作，請忽略此訊息。\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_unsubscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                You have requested to be unsubscribed to the mailing list <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>.\n"
"                <br/><br/>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                你好！<br/><br/>\n"
"                你已要求取消訂閱郵寄清單 <strong t-out=\"object.name or ''\"/>。\n"
"                <br/><br/>\n"
"                請到以下網址確認： <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>\n"
"                <br/><br/>\n"
"                如果這是錯誤，或你沒有請求此操作，請忽略此訊息。\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_guidelines
msgid ""
"<div>\n"
"                <p>Hello <t t-out=\"object.partner_id.name or ''\"/>,</p>\n"
"                <p>Please find below the guidelines of the <t t-out=\"object.mail_group_id.name\"/> mailing list.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"/></p>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"                <p><t t-out=\"object.partner_id.name or ''\"/> 你好！</p>\n"
"                <p>請參閱下方有關 <t t-out=\"object.mail_group_id.name\"/> 郵寄清單的使用指引。</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"/></p>\n"
"            </div>\n"
"        "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"日期\" title=\"日期\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"別名\" title=\"別名\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_name
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"別名\" title=\"別名\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr "<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"收件人\" title=\"收件人\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr "<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"上一個訊息\" title=\"上一個訊息\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr "<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"下一個訊息\" title=\"下一個訊息\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr "<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"顯示附件\" title=\"顯示附件\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr "<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"顯示回覆\" title=\"顯示回覆\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"隱藏附件\" title=\"隱藏附件\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"隱藏回覆\" title=\"隱藏回覆\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "<span class=\"bg-warning\">Pending</span>"
msgstr "<span class=\"bg-warning\">待處理</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">Whitelisted</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Banned</span>"
msgstr ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">已列入白名單</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">已封鎖</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<span class=\"mx-2\">-</span>"
msgstr "<span class=\"mx-2\">-</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "<span>By thread</span>"
msgstr "<span>依據對話串</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<span>No Mail Group yet.</span>\n"
"                    <br/>"
msgstr ""
"<span>未有郵件組別。</span>\n"
"                    <br/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "當建立別名的新記錄時，一個Python字典被提供作為預設值."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Accept"
msgstr "接受"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__accepted
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Accepted"
msgstr "已接受"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__action
msgid "Action"
msgstr "動作"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__active
msgid "Active"
msgstr "啟用"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Add this email address to white list of people and accept all pending "
"messages from the same author."
msgstr "將此電郵地址新增至人員白名單，並接受來自同一作者的所有待處理訊息。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Alias"
msgstr "別名"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_contact
msgid "Alias Contact Security"
msgstr "別名聯絡人安全"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain_id
msgid "Alias Domain"
msgstr "別名域"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain
msgid "Alias Domain Name"
msgstr "別名域名"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_full_name
msgid "Alias Email"
msgstr "別名電子郵件"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_name
msgid "Alias Name"
msgstr "別名"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_status
msgid "Alias Status"
msgstr "別名狀態"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_status
msgid "Alias status assessed on the last message received."
msgstr "根據最後收到的一則訊息評估的別名狀態。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_model_id
msgid "Aliased Model"
msgstr "別名的模型"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "All messages of this group"
msgstr "此群組所有訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Allowed Emails"
msgstr "允許的電子郵件"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Alone we can do so little, together we can do so much"
msgstr "一個人的力量有限，團隊的力量無堅不摧"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__allow
msgid "Always Allow"
msgstr "總是允許"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "An email with instructions has been sent."
msgstr "一封包含說明的電子郵件已發送。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Archived"
msgstr "已封存"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "Archives"
msgstr "歸檔"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__attachment_ids
msgid "Attachments"
msgstr "附件"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_id
msgid "Author"
msgstr "作者"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_moderation
msgid "Author Moderation Status"
msgstr "作者管理狀態"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr "訊息的作者。如果未設定，email_from 可能儲存一個不符合任何合作夥伴記錄的電郵地址。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_group_id
msgid "Authorized Group"
msgstr "獲授權群組"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify
msgid "Automatic notification"
msgstr "自動通知"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__ban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Ban"
msgstr "封禁"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Ban the author of the message ("
msgstr "封禁該訊息的作者（"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Ban this email address and reject all pending messages from the same author "
"and send an email to the author"
msgstr "封禁此電郵地址，拒絕同一作者的所有待處理郵件，並向該作者發送電子郵件"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__ban
msgid "Banned"
msgstr "已封禁"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Banned Emails"
msgstr "已封禁電郵"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "By date"
msgstr "日期"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__can_manage_group
msgid "Can Manage"
msgstr "可管理"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__can_manage_group
msgid "Can manage the members"
msgstr "可管理群組成員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_child_ids
msgid "Children"
msgstr "子項"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Choose or configure a custom domain"
msgstr "選擇或配置自訂域"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_subscribe
msgid "Confirm subscription to {{ object.name }}"
msgstr "確認訂閱 {{ object.name }}"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_unsubscribe
msgid "Confirm unsubscription to {{ object.name }}"
msgstr "確認取消訂閱 {{ object.name }}"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__body
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__body
msgid "Contents"
msgstr "內容"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid "Create a Mail Group"
msgstr "建立郵件群組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Create a new group"
msgstr "建立新群組"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_date
msgid "Created on"
msgstr "建立於"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__is_moderator
msgid "Current user is a moderator of the group"
msgstr "目前使用者是該群組的管理員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自訂彈回訊息"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_defaults
msgid "Default Values"
msgstr "預設值"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__description
msgid "Description"
msgstr "說明"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Discard"
msgstr "捨棄"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__email
msgid "Email"
msgstr "電郵"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Email %s is invalid"
msgstr "電郵 %s 無效"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_email
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Email Alias"
msgstr "電郵別名"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__email_from_normalized
msgid "Email From"
msgstr "電郵來自"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr "發送人的電郵地址。當找不到符合的合作夥伴時，這個欄位就會被設定，並在聊天工具中取代 author_id 欄位。"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "電子郵件網域，例如 <EMAIL> 之內的「example.com」"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails"
msgstr "電子郵件"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails waiting an action for this group"
msgstr "等待對此群組執行操作的電子郵件"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__public
msgid "Everyone"
msgstr "每一個人"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Follow-Ups"
msgstr "催款"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "From"
msgstr "由"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__mail_group_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Group"
msgstr "組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Group By"
msgstr "分組依據"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Group Message"
msgstr "群組訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Group Name"
msgstr "群組名稱"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Group message can only be linked to mail group. Current model is %s."
msgstr "群組訊息只可連結至郵件群組。目前模型是 %s。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines_msg
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Guidelines"
msgstr "使用指引"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_guidelines
msgid "Guidelines of group {{ object.mail_group_id.name }}"
msgstr "{{ object.mail_group_id.name }} 群組使用指引"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Hello"
msgstr "你好"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__id
msgid "ID"
msgstr "識別碼"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上級記錄ID支援別名(例如:專案支援任務建立別名)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設置後，此內容將自動發送給未經授權的用戶，而不是默認訊息。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__image_128
msgid "Image"
msgstr "圖片"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Invalid action for URL generation (%s)"
msgstr "產生網址操作無效 (%s)"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_moderation.py:0
msgid "Invalid email address “%s”"
msgstr "電郵地址無效：%s"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr "非法或者過期的確認鏈接。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Allowed"
msgstr "允許的"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Banned"
msgstr "是已封禁"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__is_group_moderated
msgid "Is Group Moderated"
msgstr "群組是否受管理"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_member
msgid "Is Member"
msgstr "是成員"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Join"
msgstr "加入"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Leave"
msgstr "退出"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "Let people subscribe to your list online or manually add them here."
msgstr "讓人們線上訂閱你的清單，或在此處手動加入訂閱用戶。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "基於本地部件的來件檢測"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Mail Group"
msgstr "郵件群組"

#. module: mail_group
#: model:res.groups,name:mail_group.group_mail_group_manager
msgid "Mail Group Administrator"
msgstr "郵件群組管理員"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_subscribe
msgid "Mail Group: Mailing List Subscription"
msgstr "郵件群組：訂閱郵寄清單"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_unsubscribe
msgid "Mail Group: Mailing List Unsubscription"
msgstr "郵件群組：取消訂閱郵寄清單"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_guidelines
msgid "Mail Group: Send Guidelines"
msgstr "郵件群組：發送使用指引"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_action
#: model:ir.ui.menu,name:mail_group.mail_group_menu
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Mail Groups"
msgstr "郵件群組"

#. module: mail_group
#: model:ir.actions.server,name:mail_group.ir_cron_mail_notify_group_moderators_ir_actions_server
msgid "Mail List: Notify group moderators"
msgstr "郵件清單：通知群組管理員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_message_id
msgid "Mail Message"
msgstr "郵件訊息"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_member
msgid "Mailing List Member"
msgstr "郵寄清單成員"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message
msgid "Mailing List Message"
msgstr "郵寄清單訊息"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_moderation
msgid "Mailing List black/white list"
msgstr "郵寄清單黑/白名單"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.portal_breadcrumbs_group
msgid "Mailing Lists"
msgstr "郵寄清單"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid ""
"Mailing groups are communities that like to discuss a specific topic "
"together."
msgstr "郵寄群組是喜歡一起討論某個主題的社群。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Mailing-List:"
msgstr "郵寄清單："

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Member"
msgstr "成員"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_member_action
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Members"
msgstr "成員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_count
msgid "Members Count"
msgstr "成員數目"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Members of this group"
msgstr "此群組的成員"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__members
msgid "Members only"
msgstr "只限成員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__mail_group_message_id
msgid "Message"
msgstr "訊息"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_reject_action
msgid "Message Rejection Explanation"
msgstr "拒絕訊息理據"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_action
msgid "Messages"
msgstr "訊息"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_count
msgid "Messages Count"
msgstr "訊息數目"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_last_month_count
msgid "Messages Per Month"
msgstr "每月訊息數量"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Messages are pending moderation"
msgstr "訊息正等待審核"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Messages that need an action"
msgstr "需執行動作的訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Moderate Messages"
msgstr "管理訊息"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation
msgid "Moderate this group"
msgstr "管理此群組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderated"
msgstr "受管理"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderator_id
msgid "Moderated By"
msgstr "管理人"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_ids
msgid "Moderated Emails"
msgstr "受管理電郵"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_count
msgid "Moderated emails count"
msgstr "受管理電郵數目"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderated emails in this group"
msgstr "此群組的受管理電郵"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Moderated group must have moderators."
msgstr "受管理群組必須有管理員。"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_moderation_action
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderation"
msgstr "貼文審核"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_tree
msgid "Moderation Lists"
msgstr "管理列表"

#. module: mail_group
#: model:ir.ui.menu,name:mail_group.mail_group_moderation_menu
msgid "Moderation Rules"
msgstr "管理規則"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderations"
msgstr "管理動作"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_moderator
msgid "Moderator"
msgstr "版主"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderator_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderators"
msgstr "版主"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Moderators must have an email address."
msgstr "版主必須擁有電郵地址。"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_1
msgid "My Company News"
msgstr "我公司的最新消息"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__name
msgid "Name"
msgstr "名稱"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"Need to unsubscribe? <br/>It's right here! <span class=\"oi fa-2x oi-arrow-"
"down float-end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""
"需要取消訂閱？<br/>請看這裏 <span class=\"oi fa-2x oi-arrow-down float-end\" "
"role=\"img\" aria-label=\"\" title=\"請閱讀這個！\"/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_guidelines
msgid ""
"Newcomers on this moderated group will automatically receive the guidelines."
msgstr "此受管理群組的新成員，將會自動收到使用指引。"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "No Members in this list yet!"
msgstr "此清單未有成員。"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid "No Messages in this list yet!"
msgstr "此清單未有訊息。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email_normalized
msgid "Normalized Email"
msgstr "已常規化電郵"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from_normalized
msgid "Normalized From"
msgstr "常規化由"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify_msg
msgid "Notification message"
msgstr "通知訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Notify Members"
msgstr "通知成員"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_count
msgid "Number of message in this group"
msgstr "此群組訊息數目"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid ""
"Only an administrator or a moderator can send guidelines to group members."
msgstr "只限管理員或版主可向群組成員發送使用指引。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Only members can send email to the mailing list."
msgstr "只限成員才可向郵寄清單發送電子郵件。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Only selected groups of users can send email to the mailing list."
msgstr "只限已選取群組的使用者，才可向郵寄清單發送電子郵件。"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "所有的進來的信件都將附上一條潛在商機（記錄）選配的ID，即使它們不曾回覆過它。如果設定了，這個將完全阻止新記錄的建立。"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_parent_id
msgid "Parent"
msgstr "母項"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_model_id
msgid "Parent Model"
msgstr "母項模型"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "母項記錄對話串識別碼"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"載有該別名的母項模型。載有別名參照的模型，不一定是 alias_model_id 提供的模型，例如：專案（parent_model）與任務（model）"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__partner_id
msgid "Partner"
msgstr "合作夥伴"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_partner_ids
msgid "Partners Member"
msgstr "合作夥伴成員"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_ids
msgid "Pending Messages"
msgstr "待處理訊息"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Pending Messages Count"
msgstr "待處理訊息數目"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr "待審核"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr "自動通知管理員"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__ban
msgid "Permanent Ban"
msgstr "永久封禁"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"使用郵件閘道在文件上發佈訊息的政策。\n"
"- 所有人：每個人都可發帖\n"
"- 合作夥伴：只限已驗證合作夥伴\n"
"- 關注者：只限相關文件的關注者，或正關注頻道的成員\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Post to:"
msgstr "張貼到："

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_date
msgid "Posted"
msgstr "已張貼"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_mode
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Privacy"
msgstr "私隱"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_2
msgid "Public Mailing List"
msgstr "公共郵寄清單"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/wizard/mail_group_message_reject.py:0
msgid "Re: %s"
msgstr "回應：%s"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_force_thread_id
msgid "Record Thread ID"
msgstr "記錄對話串識別碼"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Reference"
msgstr "參考"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__reject
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Reject"
msgstr "拒絕"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message_reject
msgid "Reject Group Message"
msgstr "拒絕群組訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject Silently"
msgstr "靜默拒絕"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject the message"
msgstr "拒絕訊息"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__rejected
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Rejected"
msgstr "已拒絕"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Remove message with explanation"
msgstr "移除訊息並附註理據"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Responsible Users"
msgstr "負責用戶"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "Search Group Message"
msgstr "搜尋群組訊息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_member_view_search
msgid "Search Mail Group Member"
msgstr "搜尋郵件群組成員"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Search Mail group"
msgstr "搜尋郵件群組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Search Moderation List"
msgstr "搜尋審核列表"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__groups
msgid "Selected group of users"
msgstr "已選用戶群組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Send"
msgstr "發送"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Ban"
msgstr "傳送並封禁"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Reject"
msgstr "傳送並拒絕"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__send_email
msgid "Send Email"
msgstr "發送電子郵件"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Send Guidelines"
msgstr "發送使用指引"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message_reject__send_email
msgid "Send an email to the author of the message"
msgstr "向訊息作者發送電子郵件"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr "向新訂閱者發送使用指引"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_guidelines
msgid "Sent to people who subscribed to a mailing group with group guidelines"
msgstr "發送給訂閱了設有群組使用指引的郵寄群組用戶"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_unsubscribe
msgid "Sent to people who unsubscribed from a mailing group"
msgstr "發送給從郵寄群組取消訂閱的人"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderation_status
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__status
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Status"
msgstr "狀態"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Stay in touch with our Community"
msgstr "與我們的社區保持聯繫"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__subject
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__subject
msgid "Subject"
msgstr "主題"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Subscribe"
msgstr "訂閱"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_subscribe
msgid "Subscription confirmation to a mailing group"
msgstr "郵寄群組訂閱確認"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid ""
"Template \"mail_group.mail_template_guidelines\" was not found. No email has"
" been sent. Please contact an administrator to fix this issue."
msgstr "找不到範本 mail_group.mail_template_guidelines。尚未發送任何電子郵件。請聯絡管理員以解決此問題。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Thank you!"
msgstr "謝謝！"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The \"Authorized Group\" is missing."
msgstr "「獲授權群組」缺漏。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "The email"
msgstr "該電郵"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "The email \"%s\" is not valid."
msgstr "電郵「%s」無效。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The group of the message do not match."
msgstr "訊息群組不符。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The guidelines description is empty."
msgstr "使用指引的描述留空了。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The guidelines description is missing."
msgstr "使用指引的描述缺漏。"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相應於這個別名對應的模型(Odoo單據種類)。任何一封不屬於對某個已存在的記錄的到來信件，將導致此模組中新記錄的建立(例如，一個新專案任務)。"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "郵箱別名，例如填寫 jobs 表示你想捕捉寄往 <EMAIL> 的電郵。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The notification message is missing."
msgstr "通知訊息缺漏。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The partner can not be found."
msgstr "找不到該合作夥伴。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "The record of the message should be the group."
msgstr "訊息的記錄應該是該群組。"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "This email is already subscribed."
msgstr "此電子郵件已訂閱。"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "This email is not subscribed."
msgstr "此電子郵件未訂閱。"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "This message can not be moderated"
msgstr "此訊息無法審核"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_member_unique_partner
msgid "This partner is already subscribed to the group"
msgstr "此合作夥伴已訂閱群組"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Those messages can not be moderated: %s."
msgstr "無法審核這些訊息： %s。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "To Review"
msgstr "待審查"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Unsubscribe"
msgstr "取消訂閱"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Unsubscribe:"
msgstr "取消訂閱："

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid ""
"When people send an email to the alias of the list, they will appear here."
msgstr "有人向清單別名發送電子郵件時，將在此處顯示。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Whitelist"
msgstr "白名單"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__allow
msgid "Whitelisted"
msgstr "已列入白名單"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Wrong status (%s)"
msgstr "錯誤的狀態 (%s)"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_moderation_mail_group_email_uniq
msgid "You can create only one rule for a given email address in a group."
msgstr "群組中每個電郵地址，只可建立一項規則。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr "你有訊息需要審核，請繼續進行。"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "_______________________________________________"
msgstr "_______________________________________________"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "alias"
msgstr "別名"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "and send an email to the author ("
msgstr "並向作者發送電子郵件（"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid "attachments"
msgstr "附件"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "by"
msgstr "由"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. \"Newsletter\""
msgstr "例：最新消息"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. mycompany.com"
msgstr "例：mycompany.com"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "group"
msgstr "群組"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "has been"
msgstr "已經"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"members<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"成員<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"交通\" title=\"交通\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "messages / month"
msgstr "訊息 / 月"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "more replies"
msgstr "更多的回覆"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "replies"
msgstr "回覆"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "subscribed to"
msgstr "已訂閱"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "the list"
msgstr "清單"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "unsubscribed from"
msgstr "已取消訂閱"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "your email..."
msgstr "你的電郵地址⋯"
