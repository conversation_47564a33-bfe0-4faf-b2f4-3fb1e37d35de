# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_repair
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_repair
#: model:ir.model.fields,field_description:purchase_repair.field_repair_order__purchase_count
msgid "Count of generated POs"
msgstr ""

#. module: purchase_repair
#: model:ir.model.fields,field_description:purchase_repair.field_purchase_order__repair_count
msgid "Count of source repairs"
msgstr ""

#. module: purchase_repair
#: model:ir.model,name:purchase_repair.model_purchase_order
msgid "Purchase Order"
msgstr "Satın Alma Sifarişi"

#. module: purchase_repair
#. odoo-python
#: code:addons/purchase_repair/models/repair_order.py:0
#: model_terms:ir.ui.view,arch_db:purchase_repair.view_repair_order_form_inherit
msgid "Purchase Orders"
msgstr "Satın Alma Sifarişi"

#. module: purchase_repair
#: model:ir.model,name:purchase_repair.model_repair_order
msgid "Repair Order"
msgstr ""

#. module: purchase_repair
#: model_terms:ir.ui.view,arch_db:purchase_repair.purchase_order_form_inherit
msgid "Repair Orders"
msgstr ""

#. module: purchase_repair
#. odoo-python
#: code:addons/purchase_repair/models/purchase_order.py:0
msgid "Repair Source of %s"
msgstr ""
