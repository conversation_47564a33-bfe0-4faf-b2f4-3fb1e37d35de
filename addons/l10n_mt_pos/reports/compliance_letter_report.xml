<odoo>
    <template id="report_compliance_letter_template">
        <t t-call="web.basic_layout">
            <div class="page px-4">
                <h3 class="text-center mb-4">
                    Odoo Point Of Sale application, version
                    <t t-esc="version"/>
                </h3>
                <div class="mb-1"><t t-esc="date"/></div>
                <div class="mb-1"><t t-esc="name"/></div>
                <div class="mb-1"><t t-esc="address"/></div>
                <div class="mb-4"><t t-esc="vat"/></div>
                <div class="mb-3">Dear Sir/Madam,</div>
                <div class="ps-3 mb-4" style="line-height: 1.5;">
                    Odoo S.A., (Belgium) is a registered company with the Trade and Companies Register of Nivelles with VAT number BE0477472701, having its registered office at Chaussée de Namur, 40, 1367 Grand-Rosière, Belgium ("Odoo"). Odoo provides Enterprise Resource Planning (ERP) cloud and on-premise applications to customers worldwide, incorporating a browser-based Point-of-Sale (POS) application.
                </div>
                <div class="ps-3 mb-3" style="line-height: 1.5;">
                    Odoo hereby declares that the POS application in the version
                    <t t-esc="version"/> provides the following functions and controls:
                </div>
                <ol class="ps-5 py-1 mb-4">
                    <li class="mb-2">Monitor cash register adjustments and easily verify cash contents at the end of the day</li>
                    <li class="mb-2">Keep track of daily sales and totals for every payment type</li>
                    <li class="mb-2">View all past orders as well as search by customer, product, cashier, or date</li>
                    <li class="mb-2">Advertise your current promotions, hours of operation, and upcoming events on your printed receipts</li>
                    <li class="mb-2">Set customer prices or offer percentage-based discounts on either a single product or the entire order</li>
                    <li class="mb-2">Payments are directly integrated into Odoo Accounting, making bookkeeping simple and reliable</li>
                    <li class="mb-2">Generate and print invoices for your business customers</li>
                    <li class="mb-2">Cash, checks, and credit card payment methods are available. New types of payments can also be added</li>
                    <li class="mb-2">Quickly find products by their name or barcode with the built-in search</li>
                    <li class="mb-2">Register customers' VAT numbers and apply them to invoices</li>
                    <li class="mb-2">Point-of-sale transactions cannot be tampered with through the application</li>
                </ol>
                <div class="ps-3 mb-4">Signed this day: <t t-esc="date"/></div>
                <div class="text-center mb-2">Odoo SA, BE0477472701</div>
                <div class="text-center">40 Chaussée de Namur, 1367 Grand-Rosière, Belgium</div>
            </div>
        </t>
    </template>
    <record id="report_compliance_letter" model="ir.actions.report">
        <field name="name">Compliance Letter</field>
        <field name="model">res.company</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_mt_pos.report_compliance_letter_template</field>
        <field name="report_file">l10n_mt_pos.report_compliance_letter_template</field>
        <field name="binding_model_id" ref="web.model_res_company"/>
        <field name="binding_type">report</field>
        <field name="print_report_name">'Compliance Letter'</field>
    </record>
</odoo>
