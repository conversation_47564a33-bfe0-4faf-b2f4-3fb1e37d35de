# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_rs
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-01 08:54+0000\n"
"PO-Revision-Date: 2022-12-01 08:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_001_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_001
msgid "001 - Turnover of goods and services exempt from VAT with the right to deduct previous tax"
msgstr "001 - Promet dobara i usluga koji je oslobođen PDV sa pravom na odbitak prethodnog poreza"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_002_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_002
msgid "002 - Turnover of goods and services exempt from VAT without the right to deduct previous tax"
msgstr "002 - Promet dobara i usluga koji je oslobođen PDV bez prava na odbitak prethodnog poreza"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_003_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_003
msgid "003 - Turnover of goods and services at the general rate"
msgstr "003 - Promet dobara i usluga po opštoj stopi"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_004_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_004
msgid "004 - Turnover of goods and services at the special rate"
msgstr "004 - Promet dobara i usluga po posebnoj stopi"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_005_formula
#: model:account.report.line,name:l10n_rs.tax_report_line_005
msgid "005 - Total (001 + 002 + 003 + 004)"
msgstr "005 - ZBIR (001 + 002 + 003 + 004)"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_006_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_006
msgid "006 - Previous tax paid upon import"
msgstr "006 - Prethodni porez plaćen prilikom uvoza"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_007_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_007
msgid "007 - VAT Compensation paid to the farmer"
msgstr "007 - PDV Nadoknada plaćena poljoprivredniku"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_008_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_008
msgid "008 - Previous tax, except for the previous tax with item no. 6. and 7"
msgstr "008 - Prethodni porez, osim prethodnog poreza sa red. br. 6. i 7"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_009_formula
#: model:account.report.line,name:l10n_rs.tax_report_line_009
msgid "009 - Total (006 + 007 + 008)"
msgstr "009 - ZBIR (006 + 007 + 008)"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_103_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_103
msgid "103 - Turnover of goods and services at the general rate"
msgstr "103 - Promet roba i usluga po opštem kursu"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_104_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_104
msgid "104 - Turnover of goods and services at the special rate"
msgstr "104 - Promet roba i usluga po posebnoj stopi"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_105_formula
#: model:account.report.line,name:l10n_rs.tax_report_line_105
msgid "105 - Total (103 + 104)"
msgstr "105 - Ukupno (103 + 104)"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_106_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_106
msgid "106 - Previous tax paid upon import"
msgstr "106 - Prethodni porez plaćen pri uvozu"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_107_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_107
msgid "107 - VAT Compensation paid to the farmer"
msgstr "107 - PDV Naknada plaćena poljoprivredniku"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_108_tag
#: model:account.report.line,name:l10n_rs.tax_report_line_108
msgid "108 - Previous tax, except for the previous tax with item no. 6. and 7"
msgstr "108 - Prethodni porez, osim prethodnog poreza sa br. 6. i 7"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_109_formula
#: model:account.report.line,name:l10n_rs.tax_report_line_109
msgid "109 - Total (106 + 107 + 108)"
msgstr "109 - Ukupno (106 + 107 + 108)"

#. module: l10n_rs
#: model:account.report.expression,report_line_name:l10n_rs.tax_report_line_110_formula
#: model:account.report.line,name:l10n_rs.tax_report_line_110
msgid "110 - Amount of VAT in tax period (105 - 109)"
msgstr "110 - Iznos PDV-a u poreskom periodu (105 - 109)"

#. module: l10n_rs
#: model:account.report.line,name:l10n_rs.tax_report_title_operations
msgid "Amount of fee without VAT"
msgstr "Iznos naknade bez PDV-a"

#. module: l10n_rs
#: model:account.report.column,name:l10n_rs.tax_report_vat_balance
msgid "Balance"
msgstr "Balans"

#. module: l10n_rs
#: model_terms:ir.ui.view,arch_db:l10n_rs.report_invoice_document_inherit
msgid "Company ID:"
msgstr "ID kompanije:"

#. module: l10n_rs
#: model:account.report.line,name:l10n_rs.tax_report_title_VAT_turnover
#: model:account.report.line,name:l10n_rs.tax_report_title_operations_turnover
msgid "I. Trade of goods and services"
msgstr "I. Trgovina robom i uslugama"

#. module: l10n_rs
#: model:account.report.line,name:l10n_rs.tax_report_title_VAT_previous_tax
#: model:account.report.line,name:l10n_rs.tax_report_title_operations_previous_tax
msgid "II. Previous Tax"
msgstr "II. Prethodni porez"

#. module: l10n_rs
#: model:account.report.line,name:l10n_rs.tax_report_title_VAT_liability
msgid "III. Tax liability"
msgstr "III. Poreska obaveza"

#. module: l10n_rs
#: model:ir.model,name:l10n_rs.model_account_move
msgid "Journal Entry"
msgstr "Sadrzaj dnevnika"

#. module: l10n_rs
#: model:ir.ui.menu,name:l10n_rs.account_reports_rs_statements_menu
msgid "Serbia"
msgstr "Srbija"

#. module: l10n_rs
#: model:ir.model.fields,field_description:l10n_rs.field_account_bank_statement_line__l10n_rs_turnover_date
#: model:ir.model.fields,field_description:l10n_rs.field_account_move__l10n_rs_turnover_date
#: model:ir.model.fields,field_description:l10n_rs.field_account_payment__l10n_rs_turnover_date
msgid "Turnover Date"
msgstr "Datum obrta"

#. module: l10n_rs
#: model:account.report.line,name:l10n_rs.tax_report_title_VAT
msgid "VAT"
msgstr "PDV"

#. module: l10n_rs
#: model:account.report,name:l10n_rs.tax_report_vat
msgid "VAT Report"
msgstr "Izveštaj o PDV-u"
