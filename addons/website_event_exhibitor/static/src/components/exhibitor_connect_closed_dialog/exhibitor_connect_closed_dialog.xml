<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

  <t t-name="website_event_exhibitor.ExhibitorConnectClosedDialog">
    <Dialog size="'md'" header="false" footer="false" >
        <div class="modal-header p-1 m-0">
            <button type="button" class="btn-close" t-on-click.stop="() => this.props.close()"></button>
        </div>
        <div class="o_wesponsor_js_connect_modal_main container">
            <div class="row mt-2">
                <t t-if="! sponsorData.event_is_ongoing">
                    <div class="col-12 alert alert-warning text-center" role="alert"
                        t-if="sponsorData.event_is_done">
                        Event <span t-out="sponsorData.event_name" class="fw-bold"/> is over.

                        <br/>
                        <span>Join us next time to meet <b t-out="sponsorData.name"/>!</span>
                    </div>
                    <div class="col-12 alert alert-warning text-center" role="alert"
                        t-else="">
                        <span t-out="sponsorData.name" class="fw-bold"/> is not available right now.<br />
                        Event <span t-out="sponsorData.event_name" class="fw-bold"/>
                        <span t-if="sponsorData.event_start_today">
                            starts in
                            <span t-if="sponsorData.event_start_remaining &gt;= 1" t-out="formatEventStartRemaining"/>
                            <t t-else="">
                                a few seconds
                            </t>.
                        </span>
                        <span class="my-0" t-else="">
                            starts on <span t-out="sponsorData.event_date_begin_located"/>
                        </span>
                    </div>
                </t>
                <div class="col-12 alert alert-warning text-center" role="alert"
                    t-else="">
                    <span t-out="sponsorData.name" class="fw-bold"/> is not available right now.<br />
                    Come back between
                    <strong>
                        <t t-out="sponsorData.hour_from_str"/>
                        -
                        <t t-out="sponsorData.hour_to_str"/>
                    </strong> (<span t-out="sponsorData.event_date_tz"/>)
                    to meet them!
                </div>
                <div class="col-2" t-if="sponsorData.website_image_url">
                    <img class="img" style="max-width: 100%;"
                        t-att-src="sponsorData.website_image_url"
                        t-att-alt="sponsorData.name"/>
                </div>
                <div class="col-10">
                    <div class="d-flex align-items-top mb-3">
                        <div class="d-flex flex-column me-2">
                            <div class="mb4">
                                <h4 class="d-inline" t-out="sponsorData.name"/>
                                <span class="badge text-bg-primary ms-2"
                                    t-out="sponsorData.sponsor_type_name"/>
                            </div>
                            <span class="text-muted" t-if="sponsorData.subtitle" t-out="sponsorData.subtitle"/>
                            <span t-if="sponsorData.url">
                                <i class="fa fa-globe me-2"/><a t-att-href="sponsorData.url"><span t-out="sponsorData.url"/></a>
                            </span>
                            <span t-if="sponsorData.email">
                                <i class="fa fa-envelope me-2"/><a t-att-mailto="sponsorData.email"><span t-out="sponsorData.email"/></a>
                            </span>
                            <span t-if="sponsorData.phone">
                                <i class="fa fa-phone me-2"/><span t-out="sponsorData.phone"/>
                            </span>
                        </div>
                        <img t-if="sponsorData.country_flag_url"
                            class="img ms-auto"
                            style="max-height: 36px;"
                            t-att-src="sponsorData.country_flag_url"
                            t-att-alt="sponsorData.country_name"/>
                    </div>
                </div>
                <div class="col-12" t-if="sponsorData.website_description" t-out="sponsorData.website_description"/>
            </div>
        </div>
    </Dialog>
  </t>

</templates>
