# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""
"&amp;#128075; <br/>\n"
"    Bienvenue à l'app To-do !"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Ajouter une check-list\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Ajouter un séparateur\n"
"                    (/<span style=\"font-style: italic;\">separateur</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Utiliser\n"
"                    /<span style=\"font-style: italic;\">titre</span>\n"
"                    pour convertir un texte en un titre\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            En dessous de cette liste, essayez\n"
"            <span style=\"font-weight: bolder;\">des commandes</span>\n"
"            en\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">tapant</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font style=\"background-color: #017E84; color: white\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Sélectionnez le texte pour\n"
"            <font style=\"background-color: #017E84; color: white\">mettre en évidence</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">barrer</span>\n"
"            ou\n"
"            <span style=\"font-weight: bolder;\">styler</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">.</span>\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Access your personal pipeline with your to-dos and assigned tasks by going to the Project app and clicking\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        There, your to-dos are listed as\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks.</font>\n"
"        </span>\n"
"        Any task you create privately will also be included in your to-dos. Essentially, they are interchangeable.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Accédez à votre pipeline personnel avec vos to-dos et tâches assignées en allant dans l'application Projet et en cliquant sur\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Mes Tâches</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Vos to-dos y sont répertoriées en tant que\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">tâches privées.</font>\n"
"        </span>\n"
"        Toute tâche que vous créez en privé sera également incluse dans vos to-dos. Elles sont donc interchangeables.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        By default, to-dos are only visible to you. You can share them with other users by adding them as\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignees</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Par défaut, les to-dos ne sont visibles que par vous. Vous pouvez les partager avec d'autres utilisateurs en les ajoutant en tant qu'\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignés</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Customize the stages from the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kanban view</font>\n"
"        </span>\n"
"        to reflect your preferred workflow.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Personnalisez les étapes à partir de la\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">vue Kanban</font>\n"
"        </span>\n"
"        pour correspondre à votre flux de travail préféré.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        If you want to assign your to-do to a specific project, open the ⚙️ menu and click\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convert to Task</font>.\n"
"        </span>\n"
"        This action will make it visible to other users.\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Si vous souhaitez assigner votre to-do à un projet spécifique, ouvrez le menu ⚙️ et cliquez sur\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convertir en Tâche</font>.\n"
"        </span>\n"
"        Cette action la rendra visible aux autres utilisateurs.\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your work, take notes on the go, and create tasks based on them.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Utilisez-les pour gérer votre travail, prendre des notes à la volée et créer des tâches en fonction de ces notes.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Wherever you are, use the magic keyboard shortcut to add yourself a reminder &amp;#128161;\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Où que vous soyez, utilisez le raccourci clavier pour créer un rappel &amp;#128161;\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cochez cette case pour indiquer que c'est "
"fait</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cliquez n'importe où et commencez "
"simplement à écrire</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Appuyez sur Ctrl+Z/⌘+Z pour annuler toute "
"modification</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cette to-do privée vous permet de vous amuser un peu.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Prêt à l'essayer ?</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr "<span style=\"font-size: 14px;\">Essayez ceci</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr "Ajouter to-do"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
msgid "Add a To-Do"
msgstr "Ajouter une to-do"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr "Ajouter des détails sur votre to-do..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "Archivé"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Assigné à"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Assignés"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr "Par étiquettes assignées"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr "Par étapes personnelles"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr "choisir des étiquettes à partir du projet sélectionné"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Clôturé"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Clôturé le"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Convert to Task"
msgstr "Convertir en tâche"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Convert to-dos into tasks"
msgstr "Convertir des to-dos en tâches"

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr "Créer une activité et une to-do en même temps"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create to-dos from anywhere"
msgstr "Créer des to-dos depuis n'importe où"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Créée par"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Créée le"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Supprimer"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Ignorer"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Date d'échéance"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Activités futures"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Regrouper par"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr "Salut"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""
"Organisez votre travail en utilisant des mémos et des to-do lists.\n"
"Vos tâches sont privées par défaut, mais vous pouvez choisir de les partages avec d'autres personnes en les ajoutant en tant qu'assignés."

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Activités en retard"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Manage your to-dos and assigned tasks from a single place"
msgstr "Gérez vos to-dos et tâches assignées à partir d'un seul endroit"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr "Aucune to-do trouvée. Créons-en une !"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Note"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr "Onboarding to-do déjà généré pour ces utilisateurs"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Ouvert"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Organize your to-dos however you want"
msgstr "Organisez vos tâches comme vous le souhaitez"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr "Rappel de..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Set Cover Image"
msgstr "Ajouter l'image de couverture"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant. "

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Étape"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Starred"
msgstr "Favoris"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Résumé"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Étiquettes"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Tâche"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
msgid "To-Do"
msgstr "À faire"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr "To-do"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr "Titre to-do"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do..."
msgstr "À faire..."

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr "To-dos"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Activités du jour"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr "To-dos"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_control_panel.xml:0
msgid "Toggle chatter"
msgstr "Chatter"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "Type Here..."
msgstr "Écrivez ici..."

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Untitled to-do"
msgstr "To-do sans titre"

#. module: project_todo
#: model:ir.model,name:project_todo.model_res_users
msgid "User"
msgstr "Utilisateur"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr "En utilisant l'éditeur"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Welcome %s!"
msgstr "Bienvenue %s !"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr "Qui a accès à quoi ?"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
msgid "Your to-do has been successfully added to your pipeline."
msgstr "Votre to-do a été ajouté avec succès à votre pipeline."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "convert-todo"
msgstr "convertir-todo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr "par ex. Envoyer des invitations"

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr "menu charger to-dos"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "todo-access"
msgstr "todo-accès"
