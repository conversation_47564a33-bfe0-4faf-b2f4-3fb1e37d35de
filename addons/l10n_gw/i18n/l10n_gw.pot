# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_gw
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:31+0000\n"
"PO-Revision-Date: 2023-11-30 10:31+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_gw
#: model:ir.model,name:l10n_gw.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_gw
#: model:account.report.column,name:l10n_gw.account_tax_report_gw_balance
msgid "Base"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_purchases_exempt
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_sales_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_sales_export
msgid "Export"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_purchases_import
msgid "Import"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_purchases
msgid "Incoming"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_net
msgid "Net VAT"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_simplified_5
msgid "Operations at 5% under simplified regime"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_sales
msgid "Outgoing"
msgstr ""

#. module: l10n_gw
#. odoo-python
#: code:addons/l10n_gw/models/template_gw_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_gw
#. odoo-python
#: code:addons/l10n_gw/models/template_gw.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_gw
#: model:account.report.column,name:l10n_gw.account_tax_report_gw_tax
msgid "Tax"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_withholding
msgid "Tax withheld"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_purchases_taxable
msgid "Taxable"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_sales_10
msgid "Taxable operations at 10%"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_sales_19
msgid "Taxable operations at 19%"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_credit
msgid "VAT Credit"
msgstr ""

#. module: l10n_gw
#: model:account.report,name:l10n_gw.account_tax_report_gw
msgid "VAT Report"
msgstr ""

#. module: l10n_gw
#: model:account.report.line,name:l10n_gw.account_tax_report_line_gw_to_pay
msgid "VAT to pay"
msgstr ""
