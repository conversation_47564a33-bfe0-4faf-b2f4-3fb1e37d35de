<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_7" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="name"><PERSON>, <PERSON></field>
            <field name="anonymous_name"><PERSON></field>
        </record>
        <record id="im_livechat.livechat_channel_session_7_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_7_member_portal" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_7"/>
        </record>

        <record id="livechat_channel_session_7_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-6, minutes=26)" name="date"/>
        </record>
        <record id="livechat_channel_session_7_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Hi, I need a software to easily manage my stock, and generate sales orders.</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-6, minutes=27)" name="date"/>
        </record>
        <record id="livechat_channel_session_7_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Joel Willis, you'll need our Inventory and Sales application to do so. You can try them for 15 days, FOR FREE :)</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-6, minutes=28)" name="date"/>
        </record>
        <record id="livechat_channel_session_7_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Good to hear, thanks!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-6, minutes=29)" name="date"/>
        </record>
        <record id="livechat_channel_session_7_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-6, minutes=50)" name="date"/>
        </record>
        <record id="livechat_channel_session_7_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_7</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_7"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_7_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_7')], 5, 'LIVECHAT_7', None, 'Super Job')"/>
    </data>
</odoo>
