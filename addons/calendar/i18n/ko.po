# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "# 회의"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(time_start)s의 %(date_start)s에서 \n"
"%(time_end)s (%(timezone)s)의 %(date_end)s로"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "(%(start)s 에서 %(end)s) (%(timezone)s)에서 %(day)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "%s has accepted the invitation"
msgstr "%s님이 초대를 수락했습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "%s has declined the invitation"
msgstr "%s님이 초대를 거부했습니다."

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        안녕하세요, <t t-out=\"object.common_name or ''\">Ready Mat</t>님.<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\">Jesse Brown</t>님과의 예약 일정이 변경되었음을 알려드립니다.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                예약 일정이 업데이트되었습니다.\n"
"            </t>\n"
"            <strong t-out=\"object.event_id.appointment_type_id.name or ''\">데모 예약</strong> 일정이 다음 날짜로 예약되었습니다:\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>님과의 예약 일정이 변경되었습니다.\n"
"            <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> 관련 일정은 다음 날짜로 예약되었습니다:\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            미팅 날짜가 업데이트 되었습니다.\n"
"            <strong t-out=\"object.event_id.name or ''\">프로젝트 제안 후속 조치</strong>와 관련된 미팅을 <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>님이 다음과 같이 예약하셨습니다:\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            수락</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            거절</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            보기</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">화요일</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4일</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">2021년 5월</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>행사 내용</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>장소: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(지도 보기)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>일시: <t t-out=\"object.recurrence_id.get_recurrence_name()\">매주, 3가지 행사</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>소요 시간: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0시간 30분</t></li>\n"
"                </t>\n"
"                <li>참석자\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">귀하</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        참여 방법:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Odoo 메일 및 채팅 앱을 통해 참석</t>\n"
"                        <t t-else=\"\"> 다음 링크를 통해 참석</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>행사 설명\n"
"                    <t t-out=\"object.event_id.description\">제품 및 서비스의 신규 가격 책정을 위한 내부 회의.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    감사합니다.\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br/><br/>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-if=\"not object.event_id.user_id.active\">\n"
"                You have been invited by Customer to the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        안녕하세요, <t t-out=\"object.common_name or ''\">Wood Corner</t>님.<br/><br/>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-if=\"not object.event_id.user_id.active\">\n"
"                <strong t-out=\"object.event_id.name or ''\">프로젝트 제안에 대한 후속 조치</strong> 미팅에 초대되셨습니다.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>님께서 귀하를 <strong t-out=\"object.event_id.name or ''\">프로젝트 제안에 대한 후속 조치</strong> 미팅에 초대하셨습니다.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <strong t-out=\"object.event_id.name or ''\">프로젝트 제안에 대한 후속 조치</strong> 미팅이 예약되었습니다.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            수락</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            거절</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">보기</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">화요일</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>일\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">2021년 5월</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>행사 내용</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>위치: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(지도 보기)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>일시: <t t-out=\"object.recurrence_id.get_recurrence_name()\">매주, 3가지 행사</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>소요 시간: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0시간 30분</t></li>\n"
"                </t>\n"
"                <li>참석자\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">귀하</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        참여 방법:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Odoo 메일 및 채팅 앱을 통한 참여</t>\n"
"                        <t t-else=\"\"> 웹사이트 방문을 통한 참여</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>행사 설명:\n"
"                    <t t-out=\"object.event_id.description\">제품 및 서비스의 신규 가격 책정을 위한 내부 회의.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    감사합니다.\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Tuesday</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">May 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europe/Brussels</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"/>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                How to Join:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                                <t t-else=\"\"> Join at</t><br/>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                <a target=\"_blank\" t-if=\"not is_online or is_online and object.location != object.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">(View Map)</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr/>\n"
"        <p placeholder=\"Enter your message here\"><br/></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">화요일</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>일\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">2021년 5월</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europe/Brussels</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"/>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>행사 세부 내용</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>행사 설명:\n"
"                            <t t-out=\"object.description\">제품 및 서비스의 신규 가격 책정을 위한 내부 회의.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                참가 방법:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Odoo 메일 및 채팅 앱을 통한 참가</t>\n"
"                                <t t-else=\"\"> 다음 링크를 통해 참가</t><br/>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>장소: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                <a target=\"_blank\" t-if=\"not is_online or is_online and object.location != object.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">(지도 보기)</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>일시: <t t-out=\"object.recurrence_id.get_recurrence_name()\">매주, 3가지 행사</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>기간:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0시간 30분</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr/>\n"
"        <p placeholder=\"Enter your message here\"><br/></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br/><br/>\n"
"        This is a reminder for the below event:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        안녕하세요, <t t-out=\"object.common_name or ''\">Gemini Furniture</t>님.<br/><br/>\n"
"        다음 행사에 대해 안내 드립니다:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            수락</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            거절</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            보기</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">화요일</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>일\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">2021년 5월</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>행사 세부 내용</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>장소: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(지도 보기)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>일시: <t t-out=\"object.recurrence_id.get_recurrence_name()\">매주, 3가지 행사 진행</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>소요 시간: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0시간 30분</t></li>\n"
"                </t>\n"
"                <li>참가자\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">귀하</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        참여 방법:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\">Odoo 메일 및 채팅을 통한 참여</t>\n"
"                        <t t-else=\"\">웹사이트에서 참여</t><br/>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>행사 설명:\n"
"                    <t t-out=\"object.event_id.description\">제품 및 서비스의 신규 가격 책정을 위한 내부 회의.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-calendar me-1\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr "<i class=\"fa fa-calendar me-1\" aria-label=\"미팅\" role=\"img\" title=\"미팅\"/>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-plus\"/> <span>Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/> <span>Odoo 미팅</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span class=\"fa fa-plus\"/><span> Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/><span> Odoo 미팅</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-times\"/><span> Clear meeting</span>"
msgstr "<span class=\"fa fa-times\"/><span> 미팅 삭제</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Repeat on</span>"
msgstr ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">반복됨: </span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Google Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">구글 캘린더</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Outlook Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">Outlook 캘린더</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"oi oi-arrow-right\"/><span> Join video call</span>"
msgstr "<span class=\"oi oi-arrow-right\"/><span> 화상 통화 참여</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span invisible=\"allday\" style=\"white-space: pre;\"> or </span>"
msgstr "<span invisible=\"allday\" style=\"white-space: pre;\"> 또는 </span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> Attendees</span>"
msgstr "<span> 참석자</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> 시간</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "이 페이지를 <strong>저장</strong>하고 여기로 돌아와 기능을 설정합니다."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "사용자는 동일한 연락처를 두 번 가질 수 없습니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Accept"
msgstr "수락"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__accepted_count
msgid "Accepted Count"
msgstr "허용된 수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "추가 작업"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr "캘린더 화면 열기와 같은 특정한 작업이 활동에서 진행되도록 하거나 문서 업로드가 되면 자동 완료로 표시되게 합니다."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "활성화"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "활동"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "활동"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "활동 혼합"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "활동 유형"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "활동 계획 마법사"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add attendees..."
msgstr "참석자 추가..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Add description"
msgstr "설명 추가"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add title"
msgstr "제목 추가"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "추가 메시지"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr "미리 알림용으로 알림과 함께 전송되는 추가 메시지"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_menu_patch.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
msgid "All Day"
msgstr "하루 종일"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "All Day, %(day)s"
msgstr "하루 종일, %(day)s"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
msgid "All events"
msgstr "모든 일정"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "보관됨"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "참석자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "참석자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendees_count
msgid "Attendees Count"
msgstr "참석자 수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_status
msgid "Attending?"
msgstr "참석하시나요?"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "사용 가능"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "사용 가능/사용 중"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__awaiting_count
msgid "Awaiting Count"
msgstr "대기 수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "기본 일정"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Busy"
msgstr "바쁨"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "일별"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
msgid "Bye-bye, record!"
msgstr "안녕, 기록!"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_event_menu
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "캘린더"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "일정 알림"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "일정표 참석자 정보"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_default_privacy
#: model:ir.model.fields,field_description:calendar.field_res_users_settings__calendar_default_privacy
#: model_terms:ir.ui.view,arch_db:calendar.res_users_form_view
#: model_terms:ir.ui.view,arch_db:calendar.res_users_form_view_calendar_default_privacy
msgid "Calendar Default Privacy"
msgstr "캘린더 기본 개인정보 보호"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__record
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "행사 일정표"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "캘린터 필터"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "초대 일정표"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "회의 일정표"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_popover_delete_wizard
msgid "Calendar Popover Delete Wizard"
msgstr "캘린더 팝업창 삭제 마법사"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_provider_config
msgid "Calendar Provider Configuration Wizard"
msgstr "캘린더 공급업체 설정 마법사"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Calendar Settings"
msgstr "캘린더 설정"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date Updated"
msgstr "캘린더: 업데이트된 날짜"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
msgid "Calendar: Event Reminder"
msgstr "일정표 : 행사 미리 알림"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "캘린더: 업데이트된 행사"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "캘린더: 회의 초대"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "캘린더: 알림"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Cancel"
msgstr "취소"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"행사에 속한 사람이 주최자 한 명 뿐인지 확인하세요. 즉, 주최자를 제외하고 모든 사람이 행사를 거부했는지\n"
"         확인하시기 바랍니다 (주최자 외 다른 참석자가 있는 경우만)."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "확인 완료"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__external_calendar_provider
msgid "Choose an external calendar to configure"
msgstr "설정할 외부 캘린더 선택"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr "반복되는 다른 일정에 대해 수행할 작업을 선택하십시오. 날짜 또는 시간이 수정되면 모든 일정을 갱신할 수 없습니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client ID"
msgstr "고객 ID"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client Secret"
msgstr "고객 비밀키"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "색상"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "공통 이름"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "설정"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
msgid "Confirm"
msgstr "승인"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
msgid "Connect"
msgstr "연결"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.js:0
msgid "Connect your Calendar"
msgstr "캘린더 연계"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "Contact Attendees"
msgstr "참석자 문의"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "개수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "작성자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "작성일자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_attendee
msgid "Current Attendee"
msgstr "현재 참가자"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__custom
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__custom
msgid "Custom"
msgstr "사용자 정의"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__daily
msgid "Daily"
msgstr "일별"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "날짜"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "날짜"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "일"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "일"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "일"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "일"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Decline"
msgstr "거절"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__declined_count
msgid "Declined Count"
msgstr "거부 수"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Default"
msgstr "기본"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Default Privacy"
msgstr "기본 개인정보 보호"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_res_users_settings__calendar_default_privacy
msgid "Default privacy setting for whom the calendar events will be visible."
msgstr "캘린더 이벤트가 표시되는 기본 개인정보 보호 설정입니다."

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__delete
msgid "Delete"
msgstr "삭제"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Delete Event"
msgstr "행사 삭제"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__all
msgid "Delete all the events"
msgstr "전체 행사 삭제"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__next
msgid "Delete this and following events"
msgstr "현재 및 다음 행사 삭제"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__one
msgid "Delete this event"
msgstr "이 행사 삭제"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Describe your meeting"
msgstr "회의에 대해 설명해 주세요."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "설명"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "Details"
msgstr "세부 정보"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__discuss
msgid "Discuss"
msgstr "메일 및 채팅"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_channel_id
msgid "Discuss Channel"
msgstr "메일 및 채팅 채널"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "설명 표시"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "표시명"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "문서 ID"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "문서 모델"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "문서 모델명"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "시작"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "소요시간"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "분으로 표시한 기간"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "이메일"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
msgid "Edit Recurrent event"
msgstr "반복 일정 편집"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "반복 일정 편집"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "이메일"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "이메일 - 3 시간전"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "이메일 - 6 시간전"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "임직원"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "종료일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "종료 유형"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "종료일"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "일정 알림"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "일정 알림 관리자"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "회의 일정 유형"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "일정 반복 규칙"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "일정 시간"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days"
msgstr "%(interval)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days for %(count)s events"
msgstr "%(count)s개 행사당 %(interval)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days until %(until)s"
msgstr "%(until)s까지 %(interval)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s"
msgstr "%(interval)s개월 %(day)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s for %(count)s events"
msgstr "%(count)s개 행사당 %(interval)s개월 %(day)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s until %(until)s"
msgstr "%(until)s까지 %(interval)s개월 %(day)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months on the %(position)s %(weekday)s"
msgstr "%(interval)s개월 %(position)s%(weekday)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s for %(count)s "
"events"
msgstr "%(count)s개 행사에 대해 %(interval)s개월 %(position)s%(weekday)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s until %(until)s"
msgstr "%(until)s까지 %(interval)s개월 %(position)s%(weekday)s일마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s"
msgstr "매 %(interval)s 주의 %(days)s일"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s for %(count)s events"
msgstr "행사 %(count)s건을 매 %(interval)s 주의 %(days)s 일에 진행"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s until %(until)s"
msgstr "%(until)s까지 매 %(interval)s 주의 %(days)s 일"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years"
msgstr "%(interval)s년마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years for %(count)s events"
msgstr "행사 %(count)s건을 %(interval)s년마다 진행"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years until %(until)s"
msgstr "%(until)s까지 %(interval)s년마다"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/mail_activity.py:0
msgid "Feedback: %(feedback)s"
msgstr "피드백: %(feedback)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "첫 번째"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "First you have to specify the date of the invitation."
msgstr "먼저 초대할 날짜를 지정해야 합니다."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "팔로우 반복"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "영원히"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "네 번째"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "무료"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "금"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "금요일"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__google
msgid "Google"
msgstr "구글"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Google Calendar"
msgstr "구글 캘린더"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Google Calendar icon"
msgstr "구글 캘린더 아이콘"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_id
msgid "Google Client_id"
msgstr "구글 클라이언트_id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_secret
msgid "Google Client_key"
msgstr "구글 클라이언트_key"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "구글 동기화가 일시정지 되었습니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "그룹별"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "시간"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__id
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr "사용중인 필드를 아니오로 설정하면 일정 알람 정보를 제거하지 않고 숨길 수 있습니다."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"시간을 '바쁨'으로 표시하면, 해당 행사는 공개 여부에 따라서 다른 사람들에게 전체 정보를 보이거나 간단히 '바쁨'으로만 표시될 수도 있습니다. 이 옵션을 통하여 귀하가 해당 기간 동안 다른 일을 할 수 없음을 다른 사람에게 알려줍니다.\n"
"행사를 '한가함'으로 표시하면, 귀하가 해당 기간 동안 다른 일을 할 여유가 있다는 것을 다른 사람에게 알려줍니다."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "간격"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__invalid_email_partner_ids
msgid "Invalid Email Partner"
msgstr "잘못된 협력사 이메일"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "초대장"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
#: model:ir.model.fields,field_description:calendar.field_calendar_event__access_token
msgid "Invitation Token"
msgstr "초대 토큰"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "초대 세부사항"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_invitation
msgid "Invitation email to new attendees"
msgstr "신규 참석자에게 보내는 이메일 초대장"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "초대장"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "{{ object.event_id.name }} 행사 초대"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "초대장"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "일정 강조 표시 여부"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "단일 주최자입니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Join Video Call"
msgstr "화상 회의 참여"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "최근"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "기본 캘린더에서 읽음으로 표시된 최근 알림"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type_ui
msgid "Let the event automatically repeat at that interval"
msgstr "일정 간격으로 자동으로 반복되도록 일정을 설정합니다"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "위치"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "로고"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "메일 Tz"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
msgid "Maybe"
msgstr "고려 중"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "나"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "회의"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "회의 주제"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "회의 유형"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "회의 URL"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "링크된 회의"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "회의"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "메시지"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Microsoft Outlook icon"
msgstr "Microsoft Outlook 아이콘"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "분"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_name
msgid "Model Description"
msgstr "모델 설명"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "월"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "월요일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "월별"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__monthly
msgid "Monthly"
msgstr "월별"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "월"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/calendar_form/calendar_quick_create.xml:0
msgid "More Options"
msgstr "옵션 더 보기"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "내 회의"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "이름"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
msgid "Needs Action"
msgstr "작업 필요"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "New"
msgstr "신규"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
msgid "No"
msgstr "아니오"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "참가하지 않습니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "아직 피드백이 없습니다"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "회의가 없습니다. 지금 생성합니다!"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
msgid "No, keep it"
msgstr "아니요, 보관합니다."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "알림"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "알림 - 1일 전"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "알림 - 1시간 전"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "알림 - 15분 전"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "알림 - 2시간 전"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "알림 - 30분 전"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "모든 참석자들에게 회의에 대해 알림을 전송하였습니다."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Number of Repetitions"
msgstr "반복횟수"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "반복횟수"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "OK"
msgstr "확인"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Online Meeting"
msgstr "온라인 미팅"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "내부 사용자 전용"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__confidential
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__confidential
msgid "Only internal users"
msgstr "내부 사용자 전용"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "일정표 열기"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "선택 사항"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "주최자"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__microsoft
msgid "Outlook"
msgstr "아웃룩"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "아웃룩 달력"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "Outlook 클라이언트 아이디"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "Outlook 클라이언트 비밀번호"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_sync_paused
msgid "Outlook Synchronization Paused"
msgstr "아웃룩 동기화가 일시정지 되었습니다."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "참석자"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "사용자의 협력사 관련 데이터"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "이 행사 내용을 확인할 수 있는 사람"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "전화번호"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "비공개"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__private
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "비공개"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__public
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "공개"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "반복"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "반복 규칙"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "반복 종료"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "반복 갱신"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "반복"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "반복 규칙"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "미리 알림"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "미리 알림"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type_ui
msgid "Repeat"
msgstr "반복"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat On"
msgstr "반복"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "다음 지정일까지만 반복"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Repeat every"
msgstr "계속 반복"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "다음 주기마다 반복 (일/주/월/년)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "x회 반복"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_patch.xml:0
msgid "Reschedule"
msgstr "일정 변경"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "담당자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "R규칙"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "R규칙 유형"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "토"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "토요일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "예약자"

#. module: calendar
#. odoo-python
#: code:addons/calendar/wizard/mail_activity_schedule.py:0
msgid ""
"Scheduling an activity using the calendar is not possible on more than one "
"record."
msgstr "캘린더에서 여러 레코드에 대해 활동을 예약하는 것은 불가능합니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "회의 검색"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "보조"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "참가자 선택 ..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "참석자에게 이메일 전송"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "초대장 전송"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "이메일 전송"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_reminder
msgid "Sent to all attendees if a reminder is set"
msgstr "알림 설정 시 모든 참석자에게 전송"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_changedate
msgid "Sent to all attendees if the schedule change"
msgstr "일정 변경 시 모든 참석자에게 전송"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.calendar_settings_action
#: model:ir.ui.menu,name:calendar.menu_calendar_settings
msgid "Settings"
msgstr "설정"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__should_show_status
msgid "Should Show Status"
msgstr "상태가 표시되어야 함"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "다음으로 표시"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "Snooze"
msgstr "다시 알림"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "시작"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "시작일"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr "하루 종일 진행되는 일정의  시작일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "상태"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "상태:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "중지"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "하루 종일 진행되는 일정의 종료일"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Stop synchro"
msgstr "동기화 중지"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "제목"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Submit"
msgstr "제출"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "일"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "일요일"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Synchro is paused"
msgstr "동기화가 일시 중지됨"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Synchronize with"
msgstr "동기화 대상"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "캘린더를 구글 캘린더와 동기화"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "일정을 Outlook과 동기화"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists!"
msgstr "태그명이 이미 존재합니다!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "태그"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr "메일 알림 내용을 렌더링하는데 사용되는 서식입니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "시험"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tentative_count
msgid "Tentative Count"
msgstr "잠정적 수치"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "The"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"일정표는 직원간에 공유할 수 있습니다. \n"
"                다른 응용 프로그램(예 : 직원 휴가 또는 비즈니스 기회)에 완전히 통합되었습니다."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "날짜는 1에서 31 사이여야 합니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"The ending date and time cannot be earlier than the starting date and time.\n"
"Meeting “%(name)s” starts at %(start_time)s and ends at %(end_time)s"
msgstr ""
"종료 날짜와 시간은 시작 날짜와 시간보다 빠를 수 없습니다.\n"
"\"%(name)s\" 회의는 %(start_time)s에 시작하여 %(end_time)s에 종료됩니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"The ending date cannot be earlier than the starting date.\n"
"Meeting “%(name)s” starts on %(start_date)s and ends on %(end_date)s"
msgstr ""
"종료 날짜와 시간은 시작 날짜와 시간보다 빠를 수 없습니다.\n"
"\"%(name)s\" 회의는 %(start_date)s에 시작하여 %(end_date)s에 종료됩니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "The interval cannot be negative."
msgstr "간격은 음수가 될 수 없습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "The number of repetitions cannot be negative."
msgstr "반복횟수는 음의 값이 될 수 없습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "There are no attendees on these events"
msgstr "해당 행사에 참석자가 없습니다."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "세 번째"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
msgid "This and following events"
msgstr "현재와 다음 일정"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
msgid "This event"
msgstr "현재 일정"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "목"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "목요일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "시간대"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr "메일 서식에서 시간 표시에 사용하는 표준 시간대"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
msgid "Today's Meetings"
msgstr "오늘의 회의"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__trigger_id
msgid "Trigger"
msgstr "자동 실행"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "화"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "화요일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "유형"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "Unable to save the recurrence with \"This Event\""
msgstr "\"이 행사\"는 반복 저장할 수 없습니다."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Uncertain"
msgstr "불확실"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "단위"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "반복 종료일"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_update
msgid "Used to manually notifiy attendees"
msgstr "참석자에게 수기로 알리는데 사용"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "User"
msgstr "사용자"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_can_edit
msgid "User Can Edit"
msgstr "사용자가 편집할 수 있음"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users_settings
msgid "User Settings"
msgstr "사용자 설정"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "화상 회의 소스"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Videocall URL"
msgstr "화상 회의 URL"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
msgid "View"
msgstr "화면"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "수"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "수요일"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "평일"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__weekly
msgid "Weekly"
msgstr "매주"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "주"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__yearly
msgid "Yearly"
msgstr "매년"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "년"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
msgid "Yes"
msgstr "예"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "참가합니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
msgid ""
"You are not allowed to change the calendar default privacy of another user "
"due to privacy constraints."
msgstr "개인정보 보호 제한으로 인해 다른 사용자의 캘린더 기본 개인정보 보호 설정을 변경할 수 없습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "You can't update a recurrence without base event."
msgstr "기본 행사가 없는 경우에는 반복하도록 업데이트할 수 없습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "You cannot duplicate a calendar attendee."
msgstr "일정 참석자 항목은 복사할 수 없습니다."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "You have to choose at least one day in the week"
msgstr "일주일 중 적어도 하루를 선택해야 합니다"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.xml:0
msgid "You're alone in this meeting"
msgstr "이 회의에 아무도 없습니다."

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "accepted"
msgstr "수락함"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "attendees"
msgstr "참석자"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "awaiting"
msgstr "대기 중"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "declined"
msgstr "거절함"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "예 : 비즈니스 점심"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g: 12/31/2023"
msgstr "e.g: 12/31/2023"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "maybe,"
msgstr "고려 중"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/attendee_tags_list.xml:0
msgid "no email"
msgstr "이메일 없음"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "no,"
msgstr "아니오,"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee.xml:0
msgid "props.placeholder"
msgstr "props.placeholder"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "uncertain"
msgstr "불확실"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "yes,"
msgstr "예,"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - 알림"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: 변경된 날짜"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: 행사 업데이트"
