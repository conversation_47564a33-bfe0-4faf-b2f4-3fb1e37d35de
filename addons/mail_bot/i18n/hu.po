# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# krnkris, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: krnkris, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "Kikapcsolva"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_discuss_channel
msgid "Discussion Channel"
msgstr "Kommunikációs csatorna"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Good, you can customize canned responses in the Discuss "
"application.%(new_line)s%(new_line)s%(bold_start)sIt's the end of this "
"overview%(bold_end)s, you can now %(bold_start)sclose this "
"conversation%(bold_end)s or start the tour again with typing "
"%(command_start)sstart the tour%(command_end)s. Enjoy discovering Odoo!"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Great! 👍%(new_line)sTo access special commands, %(bold_start)sstart your "
"sentence with%(bold_end)s %(command_start)s/%(command_end)s. Try getting "
"help."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/res_users.py:0
msgid "Hello,"
msgstr "Üdv!"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "Hmmm..."
msgstr "Hmmm..."

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "I'm afraid I don't understand. Sorry!"
msgstr "Attól tartok, hogy nem értem. Bocsánat!"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"I'm not smart enough to answer your question.%(new_line)sTo follow my guide,"
" ask: %(command_start)sstart the tour%(command_end)s."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr "Üresjárat"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Not exactly. To continue the tour, send an emoji: "
"%(bold_start)stype%(bold_end)s%(command_start)s :)%(command_end)s and press "
"enter."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr "Nincs inicializálva"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Not sure what you are doing. Please, type %(command_start)s/%(command_end)s "
"and wait for the propositions. Select %(command_start)shelp%(command_end)s "
"and press enter."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Not sure what you are doing. Please, type %(command_start)s:%(command_end)s "
"and wait for the propositions. Select one of them and press enter."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/res_users.py:0
msgid ""
"Odoo's chat helps employees collaborate efficiently. I'm here to help you "
"discover its features."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "OdooBot állapot"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachment"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_canned
msgid "Onboarding canned"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...%(new_line)sI can show you features if you write: "
"%(command_start)sstart the tour%(command_end)s."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Sorry, I am not listening. To get someone's attention, %(bold_start)sping "
"him%(bold_end)s. Write %(command_start)s@OdooBot%(command_end)s and select "
"me."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "Thanks"
msgstr "Köszönettel"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "Thanks for your feedback. Goodbye!"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "This is a temporary canned response to see how canned responses work."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"To %(bold_start)ssend an attachment%(bold_end)s, click on the "
"%(paperclip_icon)s icon and select a file."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "To start, try to send me an emoji :)"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/res_users.py:0
msgid "Try to send me an emoji"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check %(document_link_start)sour "
"documentation%(document_link_end)s or %(slides_link_start)sour "
"videos%(slides_link_end)s."
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "User"
msgstr "Felhasználó"

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Wonderful! 😇%(new_line)sTry typing %(command_start)s:%(command_end)s to use "
"canned responses. I've created a temporary one for you."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Wow you are a natural!%(new_line)sPing someone with @username to grab their "
"attention. %(bold_start)sTry to ping me using%(bold_end)s "
"%(command_start)s@OdooBot%(command_end)s in a sentence."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid ""
"Yep, I am here! 🎉 %(new_line)sNow, try %(bold_start)ssending an "
"attachment%(bold_end)s, like a picture of your cute dog..."
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "fuck"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "help"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "i love you"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "love"
msgstr ""

#. module: mail_bot
#. odoo-python
#: code:addons/mail_bot/models/mail_bot.py:0
msgid "start the tour"
msgstr ""
