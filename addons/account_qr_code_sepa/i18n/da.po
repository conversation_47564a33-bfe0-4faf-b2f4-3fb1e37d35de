# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_sepa
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_qr_code_sepa
#: model:ir.model,name:account_qr_code_sepa.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankkonti"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR Code with the %s currency."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR code if the account type isn't IBAN."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR code with a non SEPA iban."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "SEPA Credit Transfer QR"
msgstr "SEPA Kredit Overførsel QR"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid ""
"The account receiving the payment must have an account holder name or "
"partner name set."
msgstr ""
"Kontoen der modtager betalingen skal have et kontoholder navn eller partner "
"navn angivet."
