# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# JanaAvalah, 2024
# <PERSON><PERSON><PERSON>, 2024
# Anna, 2024
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr "<span class=\"text-muted\">(aasta lõpu saldod)</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr "<span class=\"text-muted\">(täieliku ajaloo jaoks)</span>"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "Konto"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Account Winbooks Import module"
msgstr "Konto Winbooks Import moodul"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_import_summary
msgid "Account import summary view"
msgstr "Konto impordi koondvaade"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "Raamatupidamise import"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
msgid "Accounting Import Guide"
msgstr "Raamatupidamise impordi juhend"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Accounting Import Options"
msgstr "Raamatupidamise impordi valikud"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "Baasi import"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
msgid "Chart of Accounts"
msgstr "Kontoplaan"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr "Valige, kuidas soovite oma CoA seadistada"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_date
msgid "Created on"
msgstr "Loodud"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Customers"
msgstr "Kliendid"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Download"
msgstr "Laadi alla"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Excel Import"
msgstr "Excel import"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC"
msgstr "FEC"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC Import module"
msgstr "FEC impordi moodul"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__id
msgid "ID"
msgstr "ID"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_base_import_list
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_setup_base_import_list
msgid "Import"
msgstr "Impordi"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
msgid "Import Chart of Accounts"
msgstr "Impordi kontoplaan"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import CoA"
msgstr "Impordi CoA"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import Contacts"
msgstr "Impordi kontaktid"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
msgid "Import Journal Items"
msgstr "Impordi andmike kanderead"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "Impordi partnereid"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/wizard/account_import_summary.py:0
msgid "Import Summary"
msgstr "Impordi kokkuvõte"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_account_ids
msgid "Import Summary Account"
msgstr "Konto importimise kokkuvõte"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_journal_ids
msgid "Import Summary Journal"
msgstr "Andmike importimise kokkuvõte"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_move_ids
msgid "Import Summary Move"
msgstr "Kande importimise kokkuvõte"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_name
msgid "Import Summary Name"
msgstr "Nimede importimise kokkuvõte"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_partner_ids
msgid "Import Summary Partner"
msgstr "Impordi partnerid"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_tax_ids
msgid "Import Summary Tax"
msgstr "Impordi koondmakse"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import contacts"
msgstr "Impordi kontaktid"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr ""
"Importige kliente või tarnijaid (partnereid) ja nende kontakte, kasutades"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import the Chart of Accounts and initial balances using a"
msgstr "Importige kontoplaan ja algsaldod, kasutades"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "Imported Data"
msgstr "Imporditud andmed"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Initial Setup"
msgstr "Algseadistus"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
msgid "Install a module"
msgstr "Paigalda moodul"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Journal Items"
msgstr "Andmike kanderead"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in Europe support exporting SAF-T file for audit purposes.\n"
"                            Use the"
msgstr ""
"Enamus raamatupidamistarkvarad Euroopas toetavad SAF-T faili eksportimist auditi eesmärgil.\n"
"                            Kasuta"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr ""
"Enamik Prantsusmaa raamatupidamistarkvarasid toetab FEC-faili eksportimist auditi eesmärgil.\n"
"                             Kasuta"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "No data was imported."
msgstr "Andmeid ei imporditud. "

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Optional, but useful to import open receivables & payables using a"
msgstr ""
"Valikuline, kuid tuleb kasuks nõuete ja võlgnevuste importimiseks, kasutades"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Review Manually"
msgstr "Manuaalne ülevaade"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T"
msgstr "SAF-T"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T Import module"
msgstr "SAF-T Import moodul"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 5"
msgstr "SIE 5"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE Import module"
msgstr "SIE Import moodul"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"The SIE standard file format is very common in Sweden for several purposes "
"such as auditing, importing and exporting data from and to other accounting "
"softwares."
msgstr ""
"SIE standardfaili formaat on Rootsis väga levinud mitmel eesmärgil, näiteks "
"auditeerimisel, andmete importimisel ja eksportimisel teistesse "
"raamatupidamistarkvaradesse."

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
msgid "The import file is missing the following required columns: %s"
msgstr "Impordifailil puuduvad järgmised kohustuslikud veerud: %s"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use predefined format to import your data faster."
msgstr "Andmete kiiremaks importimiseks kasutage eelmääratletud formaati. "

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr ""
"Kasutage malle CSV või Exceli importimiseks oma raamatupidamise seadistuste "
"jaoks."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"We will setup your charts of accounts and the history of journal entries, "
"that will stay in draft."
msgstr ""
"Seadistame kontoplaani ja ajaloo andmike kannetest, mis jäävad mustandisse. "

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Winbooks"
msgstr "Winbooks"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr ""
"Winbooks on vanaaegne Belgia raamatupidamistarkvara, mille hankis Exact.\n"
"                             Kasuta"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
msgid ""
"You must provide both the `code_mapping_ids/company_id` and the "
"`code_mapping_ids/code` columns."
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "accounts imported"
msgstr "imporditud kontod"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"is required to import the SIE and SIE entry files.\n"
"                            For general SIE, we will setup your charts of accounts balances, journals, partners, and the history of journal entries (journals data must be present in the file).\n"
"                            For the SIE entry, only entries and partners will be created, the rest must already be present in the system."
msgstr ""
"on kohustuslik importida SIE ja SIE formaadis failid.\n"
"                          SIE jaoks seadistame kontoplaanid, bilansi, andmikud, partnerid ja pearaamatu kannete ajaloo  (Andmike andmed peavad olema failis).\n"
"                            SIE kande jaoks peavad olema loodud ainult kanded ja partnerid, ülejäänu peab olema juba süsteemis"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "journals imported"
msgstr "imporditud andmikud"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "moves imported"
msgstr "imporditud kanded"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "or"
msgstr "või"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "partners imported"
msgstr "imporditud partnerid"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "taxes imported"
msgstr "imporditud maksud"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "template."
msgstr "mall."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""
"Winbooksi täieliku varukoopia importimiseks (Hooldus > Varundamine), et hankida kontoplaan, kontaktid, maksud, andmikukirjete ajalugu ja dokumendid.\n"
"                             Toetavad versioonid: Winbooks Desktop 5.50, 6, 7, 8."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr ""
"FEC-faili importimiseks. Seadistame teie kontoplaanid ja andmiku kannete "
"ajaloo."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "to import the SAF-T file."
msgstr "importida SAF-T fail."
