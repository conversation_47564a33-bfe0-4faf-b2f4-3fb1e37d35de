<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_payroll_structure_cp200_employee_salary" model="hr.payroll.structure">
        <field name="name">CP200: Employees Monthly Pay</field>
        <field name="code">CP200MONTHLY</field>
        <field name="country_id" ref="base.be"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_payslip_be"/>
        <field name="unpaid_work_entry_type_ids" eval="[
            (4, ref('hr_work_entry_contract.work_entry_type_unpaid_leave')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_unpredictable')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_strike')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_long_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_partial_incapacity')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_part_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_maternity')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_breast_feeding')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_paternity_legal')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_unjustified_reason')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_medical_assistance')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_youth_time_off')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_credit_time')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_parental_time_off')),
        ]"/>
    </record>
    <record id="hr_contract.structure_type_employee_cp200" model="hr.payroll.structure.type">
        <field name="default_struct_id" ref="hr_payroll_structure_cp200_employee_salary"/>
    </record>

    <record id="hr_payroll_structure_cp200_reimbursement" model="hr.payroll.structure">
        <field name="name">CP200: Reimbursement</field>
        <field name="code">CP200REIMBURSEMENT</field>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="payslip_name">Employee Reimbursement</field>
        <field name="use_worked_day_lines" eval="False"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
    </record>

    <record id="hr_payroll_structure_cp200_double_holiday" model="hr.payroll.structure">
        <field name="name">CP200: Employees Double Holidays</field>
        <field name="code">CP200DOUBLE</field>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="payslip_name">Double Holidays Slip</field>
        <field name="use_worked_day_lines" eval="False"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
    </record>

    <record id="hr_payroll_structure_cp200_employee_termination_fees" model="hr.payroll.structure">
        <field name="name">CP200: Employees Termination Fees</field>
        <field name="code">CP200TERM</field>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_termination_fees"/>
        <field name="payslip_name">Termination Fees</field>
        <field name="rule_ids" eval="[]"/>
        <field name="use_worked_day_lines" eval="False"/>
    </record>

    <record id="hr_payroll_structure_cp200_pfi" model="hr.payroll.structure">
        <field name="name">CP200: PFI</field>
        <field name="code">CP200PFI</field>
        <field name="country_id" ref="base.be"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200_pfi"/>
        <field name="unpaid_work_entry_type_ids" eval="[
            (4, ref('hr_work_entry_contract.work_entry_type_unpaid_leave')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_unpredictable')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_long_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_part_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_maternity')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_breast_feeding')),
        ]"/>
    </record>
    <record id="hr_contract.structure_type_employee_cp200_pfi" model="hr.payroll.structure.type">
        <field name="default_struct_id" ref="hr_payroll_structure_cp200_pfi"/>
    </record>

    <record id="hr_payroll_structure_cp200_employee_departure_n_holidays" model="hr.payroll.structure">
        <field name="name">CP200: Employees Termination Holidays N</field>
        <field name="code">CP200HOLN</field>
        <field name="rule_ids" eval="[]"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="hide_basic_on_pdf" eval="True"/>
        <field name="payslip_name">Holiday Pay (N Year)</field>
        <field name="use_worked_day_lines" eval="False"/>
    </record>

    <record id="hr_payroll_structure_cp200_employee_departure_n1_holidays" model="hr.payroll.structure">
        <field name="name">CP200: Employees Termination Holidays N-1</field>
        <field name="code">CP200HOLN1</field>
        <field name="rule_ids" eval="[]"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="hide_basic_on_pdf" eval="True"/>
        <field name="payslip_name">Holiday Pay (N-1 Year)</field>
        <field name="use_worked_day_lines" eval="False"/>
    </record>

    <record id="hr_payroll_structure_cp200_thirteen_month" model="hr.payroll.structure">
        <field name="name">CP200: Employees 13th Month</field>
        <field name="code">CP200THIRTEEN</field>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="payslip_name">13th Month Slip</field>
        <field name="use_worked_day_lines" eval="False"/>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="unpaid_work_entry_type_ids" eval="[
            (4, ref('hr_work_entry_contract.work_entry_type_unpaid_leave')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_long_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_part_sick')),
            (4, ref('l10n_be_hr_payroll.work_entry_type_notice')),
        ]"/>
    </record>

    <record id="hr_payroll_structure_cp200_structure_warrant" model="hr.payroll.structure">
        <field name="name">CP200: Warrants</field>
        <field name="code">CP200WARRANT</field>
        <field name="type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_bonus_month"/>
        <field name="use_worked_day_lines" eval="False"/>
        <field name="payslip_name">Warrants</field>
    </record>

    <record id="hr_payroll_structure_student_regular_pay" model="hr.payroll.structure">
        <field name="name">Student: Regular Pay</field>
        <field name="code">BESTUDENT</field>
        <field name="country_id" ref="base.be"/>
        <field name="report_id" ref="l10n_be_hr_payroll.action_report_payslip_be"/>
        <field name="payslip_name">Student: Regular Pay</field>
        <field name="type_id" ref="l10n_be_hr_payroll.structure_type_student"/>
    </record>
    <record id="structure_type_student" model="hr.payroll.structure.type">
        <field name="default_struct_id" ref="hr_payroll_structure_student_regular_pay"/>
    </record>

</odoo>
