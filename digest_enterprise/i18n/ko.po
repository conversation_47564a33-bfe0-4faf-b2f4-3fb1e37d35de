# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest_enterprise
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_0
msgid "<b class=\"tip_title\">Tip: Navigate menus like a pro</b>"
msgstr "<b class=\"tip_title\">팁: 전문가처럼 메뉴를 활용해 보세요.</b>"

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_1
msgid ""
"Connect your WhatsApp account to Odoo to receive inbound messages directly "
"in your CRM."
msgstr "WhatsApp 계정을 Odoo에 연결하여 CRM 내에서 바로 인바운드 메시지를 수신하세요."

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_0
msgid ""
"From the Home page, start typing what you would like to access. Odoo will "
"suggest menus matching your search. Use arrow keys and press enter to jump "
"to your menu."
msgstr ""
"홈페이지에서 액세스할 항목 입력을 시작하세요. Odoo가 검색어와 일치하는 메뉴를 제안합니다. 화살표와 엔터키를 같이 누르면 메뉴로 "
"이동합니다."

#. module: digest_enterprise
#: model:digest.tip,name:digest_enterprise.digest_tip_web_enterprise_0
msgid "Tip: Navigate menus like a pro"
msgstr "팁: 전문가처럼 메뉴를 활용해 보세요."

#. module: digest_enterprise
#: model:digest.tip,name:digest_enterprise.digest_tip_web_enterprise_1
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_1
msgid "Tip: Your Business right in their pocket"
msgstr "팁: 고객의 주머니 속 비즈니스"
