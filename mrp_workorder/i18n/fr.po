# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 13:01+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to delete this instruction"
msgstr "%(user_name)s suggère de supprimer cette instruction"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to use this document as instruction"
msgstr "%(user_name)s suggère d'utiliser ce document comme instruction"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "+ New Operator"
msgstr "+ Nouvel opérateur"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "< Back"
msgstr "< Retour"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> Imprimer les commandes de code-barres"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_operation_form_view
msgid "<span class=\"o_stat_text\">Instructions</span>"
msgstr "<span class=\"o_stat_text\">Instructions</span>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "<span invisible=\"employee_name\">Log In </span>"
msgstr "<span invisible=\"employee_name\">Se connecter </span>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Date de début :</strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Date de fin : </strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>Poste de travail : </strong>"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add By-product"
msgstr "Ajouter sous-produit"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add Component"
msgstr "Ajouter composant"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/employees_panel.xml:0
msgid "Add Operator"
msgstr "Ajouter un opérateur"

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Add Work Order"
msgstr "Ajouter un ordre de travail"

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add a Step"
msgstr "Ajouter une étape"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid "Add log note"
msgstr "Ajouter une note"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production_additional_workorder
msgid "Additional Workorder"
msgstr "Ordre de travail additionnel"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid ""
"Additional instructions that can be created and visualised from both here "
"and the shop floor interface."
msgstr ""
"Instructions supplémentaires pouvant être créées et visualisées à partir "
"d'ici et de l'interface de l'atelier."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__all_employees_allowed
msgid "All Employees Allowed"
msgstr "Tous les employés autorisés"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "All MO"
msgstr "Tous les OF"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allow_producing_quantity_change
msgid "Allow Changes to Producing Quantity"
msgstr "Autoriser les modifications de la quantité produite"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__allow_registration
msgid "Allow Registration"
msgstr "Autoriser l'inscription"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "Allowed Employees"
msgstr "Employés autorisés"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Archived"
msgstr "Archivé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_assigned_ids
msgid "Assigned"
msgstr "Assigné"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Availability"
msgstr "Disponibilité"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Rapport de vue d'ensemble de la nomenclature"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Back"
msgstr "Retour"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Barcode Scanned"
msgstr "Code barre scanné"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_bom
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_id
msgid "Bill of Material"
msgstr "Nomenclature"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Binary file"
msgstr "Fichier binaire"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Block"
msgstr "Bloquer"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "BoM feedback %(step)s (%(production)s - %(operation)s)"
msgstr "Retour sur la nomenclature %(step)s (%(production)s - %(operation)s)"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "BoM feedback (%(production)s - %(operation)s)"
msgstr "Retour sur la nomenclature (%(production)s - %(operation)s)"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_product_ids
msgid "Bom Product"
msgstr "Produit de la nomenclature"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE"
msgstr "CONTINUER"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Cancel"
msgstr "Annuler"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_change_production_qty
msgid "Change Production Qty"
msgstr "Changer la quantité de fabrication"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__check_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__quality_check_ids
msgid "Check"
msgstr "Contrôle"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "Contrôles"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_log_note_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Close"
msgstr "Fermer"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Close Production"
msgstr "Fermer la production"

#. module: mrp_workorder
#: model:product.attribute,name:mrp_workorder.product_attribute_color_radio
msgid "Color"
msgstr "Couleur"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__comment
msgid "Comment"
msgstr "Commentaire"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__company_id
msgid "Company"
msgstr "Société"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_ids
msgid "Component"
msgstr "Composant"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_qty_to_do
msgid "Component Qty To Do"
msgstr "Qté de composants à faire"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Configuration"
msgstr "Configuration"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Confirm"
msgstr "Confirmer"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__connected_employee_ids
msgid "Connected Employee"
msgstr "Employé connecté"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__consumption
msgid "Consumption"
msgstr "Consommation"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Continue"
msgstr "Continuer"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Continue Consumption"
msgstr "Continuer la consommation"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Continue consumption"
msgstr "Continuer la consommation"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__total_cost
msgid "Cost"
msgstr "Coût"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.js:0
msgid "Could not display the selected %s"
msgstr "Impossible d'afficher le %s sélectionné"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid "Create a new operation type"
msgstr "Créer un nouveau type d'opération"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_date
msgid "Created on"
msgstr "Créé le"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Creates a new serial/lot number"
msgstr "Créez un nouveau numéro de lot/série"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__currency_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__currency_id
msgid "Currency"
msgstr "Devise"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__current_quality_check_id
msgid "Current Quality Check"
msgstr "Contrôle qualité actuel"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__step
msgid "Custom"
msgstr "Personnalisé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__product_description_variants
msgid "Custom Description"
msgstr "Description personnalisée"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Date"
msgstr "Date"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__date_start
msgid "Date Start"
msgstr "Date de début"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "Définit le type de point de contrôle qualité"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Delete a Step"
msgstr "Supprimer une étape"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_deleted
msgid "Deleted in production"
msgstr "Supprimé en production"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Discard"
msgstr "Ignorer"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Display Log Note"
msgstr "Afficher la note"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Document"
msgstr "Document"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__qty_done
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__qty_done
msgid "Done"
msgstr "Terminé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__done_check_ids
msgid "Done Check"
msgstr "Vérification réalisée"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: model:ir.model,name:mrp_workorder.model_hr_employee
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__employee_assigned_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__employee_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employee"
msgstr "Employé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Employee Capacity"
msgstr "Capacité d'employés"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_costs_hour
msgid "Employee Hourly Cost"
msgstr "Coût horaire employé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_name
msgid "Employee Name"
msgstr "Nom de l'employé"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employees"
msgstr "Employés"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__component_tracking
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Assurez la traçabilité d'un produit stockable dans votre entrepôt."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Error during log out!"
msgstr "Erreur lors de la déconnexion !"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__duration_expected
msgid "Expected Duration"
msgstr "Durée prévue"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fail"
msgstr "Échec"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fill in worksheet"
msgstr "Compléter la feuille de travail"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Finished"
msgstr "Terminé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_lot_id
msgid "Finished Lot/Serial"
msgstr "Lot/numéro de série fini"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Lot/Serial Number"
msgstr "Lot/numéro de série terminé"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__finished_product_check_ids
msgid "Finished Product Check"
msgstr "Contrôle de produit fini"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_product_sequence
msgid "Finished Product Sequence Number"
msgstr "Numéro de séquence du produit fini"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Google Doc"
msgstr "Google Doc"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Google Slide Link"
msgstr "Lien Google Slides"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_url
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_url
msgid "Google doc URL"
msgstr "URL du document Google"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__has_operation_note
msgid "Has Description"
msgstr "A une Description"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__id
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__id
msgid "ID"
msgstr "ID"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_document
msgid "Image/PDF"
msgstr "Image/PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Improvement Suggestion"
msgstr "Suggestion d'amélioration"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "In Progress"
msgstr "En cours"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__move_line_id
msgid ""
"In case of Quality Check by Quantity, Move Line on which the Quality Check "
"applies"
msgstr ""
"En cas de contrôle qualité par quantité, déplacer la ligne sur laquelle le "
"contrôle qualité s'applique"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Indicate after which step you would like to add this one"
msgstr "Indiquez après quelle étape vous souhaitez l'ajouter."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__blocked_by_workorder_id
msgid "Insert after operation"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Install App"
msgstr "Installer l'application"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Instruction"
msgstr "Instruction"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Instruction:"
msgstr "Instruction :"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_count
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Instructions"
msgstr "Instructions"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Instructions ("
msgstr "Instructions ("

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""
"Mouvements d'inventaire pour lesquels vous devez scanner un numéro de lot "
"dans cet ordre de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_tracking
msgid "Is Component Tracked"
msgstr "Est un composant tracké"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_unfinished_wo
msgid "Is Last Work Order To Process"
msgstr "Est le dernier ordre de travail à traiter"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_lot
msgid "Is Last lot"
msgstr "Est le dernier lot"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_started_wo
msgid "Is The first Work Order"
msgstr "Est le premier ordre de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__is_workorder_step
msgid "Is Workorder Step"
msgstr "Est une étape de production"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_user_working
msgid "Is the Current User Working"
msgstr "L'utilisateur actuel travaille-t-il ?"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Load Samples"
msgstr "Charger les exemples"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_form_log_note
msgid "Log Note"
msgstr "Note"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__log_note
msgid "Log note"
msgstr "Note"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged in!"
msgstr "Connecté !"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged out!"
msgstr "Déconnecté !"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Lot"
msgstr "Lot"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__lot_id
msgid "Lot/Serial"
msgstr "Lot/série"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Rapport de vue d'ensemble des OF"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_tablet_timer
msgid "Manage Work Order timer on Shop Floor"
msgstr "Gérer le minuteur des ordres de travail dans l'atelier"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Manage your manufacturing orders from the shop floor app"
msgstr "Gérer vos ordres de fabrication dans l'application Atelier"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_shop_floor
msgid "Manage your manufacturing orders from the shop floor display app"
msgstr ""
"Gérer vos ordres de fabrication à partir de l'application de visualisation "
"de l'atelier"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "Ordre de fabrication"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Manufacturing Orders"
msgstr "Ordres de fabrication"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done"
msgstr "Marquer comme terminé"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done and Close MO"
msgstr "Marquer comme terminé et fermer l'ordre de fabrication"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Mass Produce"
msgstr "Produire en masse"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Measure:"
msgstr "Mesure :"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Menu"
msgstr "Menu"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Move to work center"
msgstr "Déplacer au poste de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "Mouvements à suivre"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Department"
msgstr "Mon département"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Team"
msgstr "Mon équipe"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "My WO"
msgstr "Mes ordres de travail"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_filter_my_work_orders
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_search_my_work_orders
msgid "My Work Orders"
msgstr "Mes ordres de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__note
msgid "New Instruction"
msgstr "Nouvelle instruction"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Instruction suggested by %(user_name)s"
msgstr "Nouvelle instruction suggérée par %(user_name)s"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "New Step suggested by %(user_name)s"
msgstr "Nouvelle étape suggérée par %(user_name)s"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Title suggested:"
msgstr "Suggestion de nouveau titre :"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Newly Hired"
msgstr "Nouvelles recrues"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Next"
msgstr "Suivant"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__next_check_id
msgid "Next Check"
msgstr "Prochain contrôle"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Next Operation"
msgstr "Opération suivante"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "No manufacturing steps defined yet!"
msgstr "Aucune étape de fabrication n'est encore définie !"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid "No work orders to do!"
msgstr "Aucun ordre de fabrication à faire !"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid ""
"No workcenters are available, please create one first to add it to the shop "
"floor view"
msgstr ""
"Aucun poste de travail n'est disponible. Veuillez d'abord en créer un à "
"ajouter à la vue de l'atelier"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Notes"
msgstr "Notes"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Number of employees needed to complete operation."
msgstr "Nombre d'employés nécessaires pour compléter l'opération."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Open Manufacturing Order"
msgstr "Ouvrir l'ordre de fabrication"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Open Shop Floor"
msgstr "Ouvrir l'atelier"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workorder_id
msgid "Operation"
msgstr "Opération"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "Operations"
msgstr "Opérations"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Operator"
msgstr "Opérateur"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_stock_picking_type_action
#: model:ir.actions.server,name:mrp_workorder.action_view_mrp_overview
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_dashboard
msgid "Overview"
msgstr "Vue d'ensemble"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "PAUSE"
msgstr "PAUSE"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "PDF file"
msgstr "Fichier PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Pass"
msgstr "Réussite"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Password?"
msgstr "Mot de passe ?"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Pause"
msgstr "Mettre en attente"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Pending"
msgstr "En attente"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_picking_type
msgid "Picking Type"
msgstr "Type de transfert"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__picture
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__picture
msgid "Picture"
msgstr "Image"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_production
msgid "Planning by Production"
msgstr "Planning par production"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_workcenter
msgid "Planning by Workcenter"
msgstr "Planning par poste de travail"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a Lot/SN."
msgstr "Saisissez un numéro de lot/série."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a positive quantity."
msgstr "Saisissez une quantité positive."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"Please set the quantity you are currently producing. It should be different "
"from zero."
msgstr ""
"Définissez la quantité que vous produisez actuellement. Celle-ci doit être "
"différente de zéro."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order"
msgstr "Débloquez le poste de travail pour lancer l'ordre de travail"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please upload a picture."
msgstr "Veuillez charger une image."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__previous_check_id
msgid "Previous Check"
msgstr "Contrôle précédent"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Print Labels"
msgstr "Imprimer les étiquettes"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_print_label
msgid "Print label"
msgstr "Imprimer l'étiquette"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_id
msgid "Product To Register"
msgstr "Produit à enregistrer"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__production_id
msgid "Production"
msgstr "Production"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__production_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__production_id
msgid "Production Order"
msgstr "Ordre de production"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Production Workcenter"
msgstr "Poste de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__product_ids
msgid "Products"
msgstr "Produits"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Propose Change"
msgstr "Proposer un changement"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_propose_change
msgid "Propose a change in the production"
msgstr "Proposer un changement dans la production"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_alert
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_ids
msgid "Quality Alert"
msgstr "Alerte qualité"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_count
msgid "Quality Alert Count"
msgstr "Nombre d'alertes qualité"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "Contrôle qualité"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_fail
msgid "Quality Check Fail"
msgstr "Échec du contrôle qualité"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_todo
msgid "Quality Check Todo"
msgstr "Contrôle qualité à faire"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "Point de contrôle qualité"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "Type de test de contrôle qualité"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_ids
msgid "Quality Point"
msgstr "Point qualité"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Quality Point Steps"
msgstr "Etapes de points qualité"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "Le point de qualité s'appliquera à tous les produits sélectionnes. "

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_state
msgid "Quality State"
msgstr "Étape de qualité"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Quantity"
msgstr "Quantité"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
msgid "Quantity Produced"
msgstr "Quantité produite"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Ready"
msgstr "Prêt"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Reason:"
msgstr "Motif :"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Record production"
msgstr "Enregistrer la production"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register %s"
msgstr "Enregistrer %s"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_byproducts
msgid "Register By-products"
msgstr "Enregistrer les sous-produits"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_consumed_materials
msgid "Register Consumed Materials"
msgstr "Enregistrer les matières consommées"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_production
msgid "Register Production"
msgstr "Enregistrer la production"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register Production: %s"
msgstr "Enregistrer la production : %s"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__additional
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__additional
msgid "Register additional product"
msgstr "Enregistrer un produit supplémentaire"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_active
msgid "Related Bill of Material Active"
msgstr "Nomenclature correspondante active"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_remaining_qty
msgid "Remaining Quantity for Component"
msgstr "Quantité restante pour le composant"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__remove_step
msgid "Remove Current Step"
msgstr "Supprimer l'étape actuelle"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_report_type
msgid "Report Type"
msgstr "Type de rapport"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__result
msgid "Result"
msgstr "Résultat"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Scrap"
msgstr "Rebut"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_open_employee_list
msgid "Select Employee"
msgstr "Sélectionner un employé"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.js:0
msgid "Select Work Centers for this station"
msgstr "Sélectionnez des postes de travail pour cette station"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select a new work center"
msgstr "Sélectionner un nouveau poste de travail"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select the step you want to modify"
msgstr "Sélectionnez l'étape que vous souhaitez modifier"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Serial"
msgstr "En série"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__set_picture
msgid "Set Picture"
msgstr "Définir la photo"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Set a New picture"
msgstr "Définir une nouvelle photo"

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.action_mrp_display
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_shop_floor
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_root
#: model:ir.ui.menu,name:mrp_workorder.menu_shop_floor
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Shop Floor"
msgstr "Atelier"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Shop Floor Control Panel"
msgstr "Panneau de contrôle de l'atelier"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_routing_workcenter_tree_view_inherited
msgid "Show Instructions"
msgstr "Afficher les instructions"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Show the timer on the work order screen"
msgstr "Montrer le minuteur sur l'écran des ordres de travail"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Skip >"
msgstr "Passer >"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Slides viewer"
msgstr "Slides viewer"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool
msgid "Small wooden stool"
msgstr "Petit tabouret en bois"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__operation
msgid "Specific Page of Operation Worksheet"
msgstr "Page précise d'une feuille de travail d'opération"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__operation_id
msgid "Step"
msgstr "Étape"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__source_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__source_document
msgid "Step Document"
msgstr "Document d'étape"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__step_id
msgid "Step to change"
msgstr "Étape à changer"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_mrp_workorder_show_steps
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_count
msgid "Steps"
msgstr "Étapes"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_id
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr "Ligne de mouvement de stock"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool
msgid "Stool"
msgstr "Tabouret"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_foot
msgid "Stool Foot"
msgstr "Pied du tabouret"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_top
msgid "Stool Top"
msgstr "Assise du tabouret"

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.tablet_client_action
msgid "Tablet Client Action"
msgstr "Action de tablette client"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type
msgid "Technical name"
msgstr "Nom technique"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Type de test"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"Le système des types d'opérations vous permet d'assigner à chaque opération\n"
"                de stock un type spécifique qui altérera ses vues en fonction.\n"
"                Sur le type d'opération, vous pourriez par exemple spécifier si le colis est requis par défaut,\n"
"                si le client doit être affiché."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "There is no session chief. Please log in."
msgstr "Il n'y a pas de chef de session. Veuillez vous connecter."

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_search_inherit_planning
msgid "This Station"
msgstr "Cette station de travail"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"This workcenter isn't expected to have open workorders during this period. "
"Work hours :"
msgstr ""
"Ce poste de travail ne devrait pas avoir d'ordres de travail ouverts pendant"
" cette période. Heures de travail :"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "Suivi du temps : %(user)s"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_tablet_timer
msgid "Timer"
msgstr "Minuteur"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__title
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__title
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Title"
msgstr "Titre"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Title:"
msgstr "Titre :"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "To Process"
msgstr "À traiter"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Total Qty"
msgstr "Qté totale"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid ""
"Track work orders, show instructions and record manufacturing operations from here:\n"
"                                    quality control, consumed quantities, lot/serial numbers, etc."
msgstr ""
"Suivez les ordres de travail, affichez les instructions et enregistrez les opérations de fabrication à partir d'ici :\n"
"                                    contrôle qualité, quantités consommées, lots/numéros de série, etc."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__product_tracking
msgid "Tracking"
msgstr "Suivi"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_workorder_form
msgid "Type"
msgstr "Type"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__change_type
msgid "Type of Change"
msgstr "Type de changement"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid ""
"Unable to load samples when you already have existing manufacturing orders"
msgstr ""
"Impossible de charger les exemples lorsque vous avez déjà des ordres de "
"fabrication existants"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Unblock"
msgstr "Débloquer"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Undo"
msgstr "Annuler"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Unit"
msgstr "Unité"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Units"
msgstr "Unités"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.production_order_unplan_server_action
msgid "Unplan orders"
msgstr "Déplanifier les ordres"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_uom_id
msgid "UoM"
msgstr "UdM"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__update_step
msgid "Update Current Step"
msgstr "Mettre à jour l'étape actuelle"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Update Instructions"
msgstr "Mettre à jour les instructions"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Upload your PDF file."
msgstr "Téléchargez votre fichier PDF."

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid ""
"Use steps to show instructions on a worksheet to operators, or trigger "
"quality checks at specific steps of the work order."
msgstr ""
"Utilisez des étapes pour afficher des instructions sur une feuille de "
"travail aux opérateurs ou déclencher des contrôles de qualité à des étapes "
"spécifiques de l'ordre de travail."

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Use the table work center control panel to register operations in the shop floor directly.\n"
"            The tablet provides worksheets for your workers and allow them to scrap products, track time,\n"
"            launch a maintenance request, perform quality tests, etc."
msgstr ""
"Utilisez le panneau de commande du poste de travail pour enregistrer les opérations directement à l'atelier.\n"
"            La tablette fournit des feuilles de travail à vos opérateurs et vous permet de créer des produits de rebut, de suivre leur temps de travail,\n"
"            de lancer une demande de maintenance, d'effectuer des contrôles qualité, etc."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Validate"
msgstr "Valider"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valeur du dernier code-barres scanné."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_bar.xml:0
msgid "WO"
msgstr "OT"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "WO Filters"
msgstr "Filtres OT"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Waiting"
msgstr "En attente"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "What do you want to do?"
msgstr "Que voulez-vous faire ?"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Will be placed at the beginning if emtpy"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_alert_view_search_inherit_mrp_workorder
msgid "Work Center"
msgstr "Poste de travail"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Utilisation du poste de travail"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "Ordre de travail"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_tree
msgid "Work Order Operation"
msgstr "Opération d'ordre de travail"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_workorder_action_tablet
#: model:ir.ui.menu,name:mrp_workorder.mrp_workorder_menu_planning
msgid "Work Orders"
msgstr "Ordres de travail"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_production
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_workcenter
msgid "Work Orders Planning"
msgstr "Planification des ordres de travail"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_view_kanban_inherit_workorder
msgid "Work orders"
msgstr "Ordres de travail"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Les ordres de travail sont des opérations à effectuer dans le cadre d'un ordre de fabrication.\n"
"Les opérations sont définies dans la nomenclature ou ajoutées directement dans l'ordre de fabrication."

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Workcenter Control Panel"
msgstr "Panneau de contrôle du poste de travail"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Journal de productivité du poste de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__working_state
msgid "Workcenter Status"
msgstr "Statut du poste de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_ids
msgid "Working employees"
msgstr "Employés actifs"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__workorder_id
msgid "Workorder"
msgstr "Ordre de travail"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_worksheet.xml:0
msgid "Worksheet"
msgstr "Feuille de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_page
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_page
msgid "Worksheet Page"
msgstr "Page de feuille de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__worksheet_page
msgid "Worksheet page"
msgstr "Page de feuille de travail"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Wrong password!"
msgstr "Mauvais mot de passe !"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allow to work on some of these work orders."
msgstr ""
"Vous n'êtes pas autorisé à travailler sur certains de ces ordres de travail."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allowed to work on the workorder"
msgstr "Vous n'êtes pas autorisé à travailler sur l'ordre de travail"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/change_production_qty.py:0
msgid ""
"You cannot update the quantity to do of an ongoing manufacturing order for "
"which quality checks have been performed."
msgstr ""
"Vous ne pouvez pas mettre la quantité à faire à jour sur un ordre de "
"fabrication en cours pour lequel des contrôles qualités ont été réalisés."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "You did not set a lot/serial number for the final product"
msgstr "Vous n'avez pas indiqué de lot/numéro de série pour le produit fini"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You must be logged in to process some of these work orders."
msgstr ""
"Vous devez être connecté pour traiter certains de ces ordres de travail."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to complete Quality Checks using the Shop Floor before marking Work"
" Order as Done."
msgstr ""
"Vous devez compléter les contrôles qualité dans l'application Atelier avant "
"de marquer l'ordre de travail comme terminé."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productive'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Vous devez définir au moins une perte de productivité dans la catégorie "
"'Productivité'. Créez-en une depuis l'application Fabrication, menu : "
"Configuration / Pertes de productivité."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to link this user to an employee of this company to process the "
"work order"
msgstr ""
"Vous devez relier cet utilisateur à un employé de cette entreprise pour "
"traiter l'ordre de travail."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You need to log in to process this work order."
msgstr "Vous devez être connecté pour traiter cet ordre de travail."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You still need to do the quality checks!"
msgstr "Vous devez toujours effectuer les contrôles qualité !"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Your suggestion to delete the %s step was succesfully created."
msgstr ""
"Votre suggestion de supprimer l'étape %s a été mise en œuvre avec succès. "

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "back"
msgstr "retour"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter_productivity__employee_id
msgid "employee that record this working time"
msgstr "employé qui enregistre ce temps de travail"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_cost
msgid "employee_cost"
msgstr "employee_cost"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "employees with access"
msgstr "employés avec accès"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "if left empty, all employees can log in to the workcenter"
msgstr ""
"si laissé vide, tous les employés peuvent se connecter au poste de travail"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "menu"
msgstr "menu"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "per employee"
msgstr "par employé"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_foot
msgid "wooden stool foot"
msgstr "pied du tabouret en bois"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_top
msgid "wooden stool top"
msgstr "assise du tabouret en bois"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__employee_ids
msgid "working employees"
msgstr "employés actifs"
