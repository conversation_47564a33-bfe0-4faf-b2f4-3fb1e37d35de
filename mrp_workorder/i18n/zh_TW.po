# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>doo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 13:01+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to delete this instruction"
msgstr "%(user_name)s 建議刪除此指示"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to use this document as instruction"
msgstr "%(user_name)s 建議使用此文件作為指示"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "+ New Operator"
msgstr "+ 新操作員"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "< Back"
msgstr "< 返回"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "列印條碼命令"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_operation_form_view
msgid "<span class=\"o_stat_text\">Instructions</span>"
msgstr "<span class=\"o_stat_text\">指示</span>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "<span invisible=\"employee_name\">Log In </span>"
msgstr "<span invisible=\"employee_name\">登入</span>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>開始日期: </strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>停止日期: </strong>"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>工作中心: </strong>"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_ids
msgid "Activities"
msgstr "活動"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add By-product"
msgstr "加入副產品"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add Component"
msgstr "加入組件"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/employees_panel.xml:0
msgid "Add Operator"
msgstr "加入操作員"

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Add Work Order"
msgstr "加入工作單"

#. module: mrp_workorder
#. odoo-javascript
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Add a Step"
msgstr "加入步驟"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid "Add log note"
msgstr "加入日誌備註"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production_additional_workorder
msgid "Additional Workorder"
msgstr "附加工作單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid ""
"Additional instructions that can be created and visualised from both here "
"and the shop floor interface."
msgstr "可在此處及工場程式介面，建立及視覺化顯示附加指示。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__all_employees_allowed
msgid "All Employees Allowed"
msgstr "允許所有員工"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "All MO"
msgstr "所有製造單"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allow_producing_quantity_change
msgid "Allow Changes to Producing Quantity"
msgstr "允許更改生產數量"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__allow_registration
msgid "Allow Registration"
msgstr "允許註冊"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "Allowed Employees"
msgstr "允許的員工"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Archived"
msgstr "已封存"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_assigned_ids
msgid "Assigned"
msgstr "已分派"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Availability"
msgstr "可用"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "BOM結構清單報表"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Back"
msgstr "返回"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Barcode Scanned"
msgstr "掃瞄到條碼"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_bom
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_id
msgid "Bill of Material"
msgstr "物料清單"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Binary file"
msgstr "二進位文件"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Block"
msgstr "區塊"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "BoM feedback %(step)s (%(production)s - %(operation)s)"
msgstr "用料清單回應 %(step)s (%(production)s - %(operation)s)"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "BoM feedback (%(production)s - %(operation)s)"
msgstr "用料清單回應 (%(production)s - %(operation)s)"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_product_ids
msgid "Bom Product"
msgstr "BoM產品"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE"
msgstr "繼續"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Cancel"
msgstr "取消"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_change_production_qty
msgid "Change Production Qty"
msgstr "更改生產數量"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__check_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__quality_check_ids
msgid "Check"
msgstr "檢查"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "檢查"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_log_note_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Close"
msgstr "關閉"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Close Production"
msgstr "關閉生產"

#. module: mrp_workorder
#: model:product.attribute,name:mrp_workorder.product_attribute_color_radio
msgid "Color"
msgstr "顏色"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__comment
msgid "Comment"
msgstr "評論"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__company_id
msgid "Company"
msgstr "公司"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_ids
msgid "Component"
msgstr "組件"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_qty_to_do
msgid "Component Qty To Do"
msgstr "待處理組件數量"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Configuration"
msgstr "配置"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Confirm"
msgstr "確認"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__connected_employee_ids
msgid "Connected Employee"
msgstr "已連接的員工"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__consumption
msgid "Consumption"
msgstr "消耗"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Continue"
msgstr "繼續"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Continue Consumption"
msgstr "繼續消耗"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Continue consumption"
msgstr "繼續消耗"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__total_cost
msgid "Cost"
msgstr "成本"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.js:0
msgid "Could not display the selected %s"
msgstr "未能顯示已選取的 %s"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid "Create a new operation type"
msgstr "建立新操作類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__create_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_date
msgid "Created on"
msgstr "建立於"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Creates a new serial/lot number"
msgstr "建立新的序號/批號"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__currency_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__currency_id
msgid "Currency"
msgstr "貨幣"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__current_quality_check_id
msgid "Current Quality Check"
msgstr "目前的品質檢查"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__step
msgid "Custom"
msgstr "自訂"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__product_description_variants
msgid "Custom Description"
msgstr "自訂描述"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Date"
msgstr "日期"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__date_start
msgid "Date Start"
msgstr "開始日期"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "定義品質控制點類型"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Delete a Step"
msgstr "刪除步驟"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_deleted
msgid "Deleted in production"
msgstr "在生產中刪除"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Discard"
msgstr "捨棄"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Display Log Note"
msgstr "顯示日誌備註"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Document"
msgstr "文件"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__qty_done
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__qty_done
msgid "Done"
msgstr "完成"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__done_check_ids
msgid "Done Check"
msgstr "完成檢查"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
#: model:ir.model,name:mrp_workorder.model_hr_employee
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__employee_assigned_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__employee_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employee"
msgstr "員工"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Employee Capacity"
msgstr "員工生產能力"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_costs_hour
msgid "Employee Hourly Cost"
msgstr "員工每小時成本"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_name
msgid "Employee Name"
msgstr "員工姓名"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Employees"
msgstr "員工"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__component_tracking
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "確保倉庫內可儲存產品的可追蹤性。"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Error during log out!"
msgstr "登出時出錯！"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__duration_expected
msgid "Expected Duration"
msgstr "預計時長"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fail"
msgstr "失敗"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Fill in worksheet"
msgstr "填寫工作表"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Finished"
msgstr "已完成"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_lot_id
msgid "Finished Lot/Serial"
msgstr "已完成批次/序列"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Lot/Serial Number"
msgstr "完成的批次/序號"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__finished_product_check_ids
msgid "Finished Product Check"
msgstr "成品檢查"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_product_sequence
msgid "Finished Product Sequence Number"
msgstr "成品序列號"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Google Doc"
msgstr "Google 文件"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Google Slide Link"
msgstr "Google 幻燈片連結"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_url
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_url
msgid "Google doc URL"
msgstr "Google 文件網址"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__has_operation_note
msgid "Has Description"
msgstr "有描述"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__id
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__id
msgid "ID"
msgstr "識別號"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_document
msgid "Image/PDF"
msgstr "圖像/PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Improvement Suggestion"
msgstr "改進建議"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "In Progress"
msgstr "進行中"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__move_line_id
msgid ""
"In case of Quality Check by Quantity, Move Line on which the Quality Check "
"applies"
msgstr "按數量檢查品質時，套用品質檢查的移動資料行"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Indicate after which step you would like to add this one"
msgstr "表示你想在哪一個步驟之後加入此步驟"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__blocked_by_workorder_id
msgid "Insert after operation"
msgstr "插入至操作之後"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.xml:0
msgid "Install App"
msgstr "安裝應用程式"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Instruction"
msgstr "指示"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Instruction:"
msgstr "指示："

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_count
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Instructions"
msgstr "指示"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Instructions ("
msgstr "指示（"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "您必須在此工單掃瞄批次號碼的庫存移動"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_tracking
msgid "Is Component Tracked"
msgstr "部件已追溯"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_unfinished_wo
msgid "Is Last Work Order To Process"
msgstr "是最後作業工單"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_lot
msgid "Is Last lot"
msgstr "是最後一批"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_started_wo
msgid "Is The first Work Order"
msgstr "是首張工作單"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__is_workorder_step
msgid "Is Workorder Step"
msgstr "是工作單步驟"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_user_working
msgid "Is the Current User Working"
msgstr "目前使用者正在工作嗎"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__write_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Load Samples"
msgstr "載入範例"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_form_log_note
msgid "Log Note"
msgstr "記錄備註"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__log_note
msgid "Log note"
msgstr "記錄備註"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged in!"
msgstr "已登入！"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Logged out!"
msgstr "已登出！"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Lot"
msgstr "批次"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__lot_id
msgid "Lot/Serial"
msgstr "批次/序列號"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "生產訂單（MO）概覽報告"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_tablet_timer
msgid "Manage Work Order timer on Shop Floor"
msgstr "在工場應用程式管理工作單計時器"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Manage your manufacturing orders from the shop floor app"
msgstr "在工場應用程式管理你的製造訂單"

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_shop_floor
msgid "Manage your manufacturing orders from the shop floor display app"
msgstr "在工場顯示應用程式管理你的製造訂單"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "製造訂單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Manufacturing Orders"
msgstr "製造訂單"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done"
msgstr "標記為完成"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done and Close MO"
msgstr "標記為完成並關閉MO"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Mass Produce"
msgstr "量產"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Measure:"
msgstr "測量："

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Menu"
msgstr "功能表"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Move to work center"
msgstr "移動至工作中心"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "待追蹤的移動"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Department"
msgstr "我的部門"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "My Team"
msgstr "我的團隊"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid "My WO"
msgstr "我的工作單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_filter_my_work_orders
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_workorder_form_view_search_my_work_orders
msgid "My Work Orders"
msgstr "我的工作單"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__note
msgid "New Instruction"
msgstr "新指示"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Instruction suggested by %(user_name)s"
msgstr "%(user_name)s 建議的新指示"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "New Step suggested by %(user_name)s"
msgstr "%(user_name)s 建議的新步驟"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Title suggested:"
msgstr "建議的新標題："

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_view_employee_filter
msgid "Newly Hired"
msgstr "新聘人員"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Next"
msgstr "下一頁"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__next_check_id
msgid "Next Check"
msgstr "下一檢查"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Next Operation"
msgstr "下一操作"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "No manufacturing steps defined yet!"
msgstr "尚未定義生產步驟！"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid "No work orders to do!"
msgstr "沒有工單可作!"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_workcenter_dialog.js:0
msgid ""
"No workcenters are available, please create one first to add it to the shop "
"floor view"
msgstr "沒有可用的工作中心。請先建立一個，以將其新增至工場檢視畫面中"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Notes"
msgstr "備註"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_routing_workcenter__employee_ratio
msgid "Number of employees needed to complete operation."
msgstr "完成操作所需的員工人數。"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Open Manufacturing Order"
msgstr "開啟製造訂單"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Open Shop Floor"
msgstr "開啟工場"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workorder_id
msgid "Operation"
msgstr "操作"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp_workorder.workcenter_line_gantt_production
msgid "Operations"
msgstr "製程"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Operator"
msgstr "客服人員"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_stock_picking_type_action
#: model:ir.actions.server,name:mrp_workorder.action_view_mrp_overview
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_dashboard
msgid "Overview"
msgstr "概覽"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "PAUSE"
msgstr "暫停"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "PDF file"
msgstr "PDF"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Pass"
msgstr "通過"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/pin_popup.xml:0
msgid "Password?"
msgstr "密碼？"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/working_employee_popup.xml:0
msgid "Pause"
msgstr "暫停"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Pending"
msgstr "暫停"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_picking_type
msgid "Picking Type"
msgstr "揀貨類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__picture
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__picture
msgid "Picture"
msgstr "圖片"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_production
msgid "Planning by Production"
msgstr "按生產規劃"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_workcenter
msgid "Planning by Workcenter"
msgstr "按工作中心規劃"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a Lot/SN."
msgstr "請輸入批號/序號。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a positive quantity."
msgstr "請輸入正數。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"Please set the quantity you are currently producing. It should be different "
"from zero."
msgstr "請設定您正在生產的數量。 它不能為零。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order"
msgstr "請解鎖工作中心,以啟始工作單"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please upload a picture."
msgstr "請上傳圖片。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__previous_check_id
msgid "Previous Check"
msgstr "之前檢查"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Print Labels"
msgstr "標籤列印"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_print_label
msgid "Print label"
msgstr "列印標籤"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "產品移動(移庫明細)"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_id
msgid "Product To Register"
msgstr "待登記產品"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__production_id
msgid "Production"
msgstr "生產"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__production_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__production_id
msgid "Production Order"
msgstr "製造訂單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Production Workcenter"
msgstr "生產工作中心"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__product_ids
msgid "Products"
msgstr "產品"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Propose Change"
msgstr "提出變更"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_propose_change
msgid "Propose a change in the production"
msgstr "提出生產變更建議"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_alert
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_ids
msgid "Quality Alert"
msgstr "品質警示"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_count
msgid "Quality Alert Count"
msgstr "品質警示數量"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "品質檢查"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_fail
msgid "Quality Check Fail"
msgstr "質檢失敗"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_todo
msgid "Quality Check Todo"
msgstr "品質檢查待辦事項"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "品質控制點"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "品質控制測試類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_ids
msgid "Quality Point"
msgstr "品質點"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Quality Point Steps"
msgstr "質檢步驟"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "品質點將應用於所有選定產品。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_state
msgid "Quality State"
msgstr "品質狀態"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Quantity"
msgstr "數量"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_register_production_dialog.xml:0
msgid "Quantity Produced"
msgstr "已生產數量"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Ready"
msgstr "準備好"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Reason:"
msgstr "原因："

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Record production"
msgstr "生產記錄"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register %s"
msgstr "註冊 %s"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_byproducts
msgid "Register By-products"
msgstr "登記副產品"

#. module: mrp_workorder
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_consumed_materials
msgid "Register Consumed Materials"
msgstr "登記已消耗物料"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:quality.point.test_type,name:mrp_workorder.test_type_register_production
msgid "Register Production"
msgstr "註冊生產"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "Register Production: %s"
msgstr "登記生產：%s"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__additional
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__additional
msgid "Register additional product"
msgstr "登記附加產品"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_active
msgid "Related Bill of Material Active"
msgstr "相關用料清單生效"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_remaining_qty
msgid "Remaining Quantity for Component"
msgstr "組件的剩餘數量"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__remove_step
msgid "Remove Current Step"
msgstr "移除目前步驟"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_report_type
msgid "Report Type"
msgstr "報表類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__user_id
msgid "Responsible"
msgstr "負責人"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__result
msgid "Result"
msgstr "結果"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Scrap"
msgstr "報廢"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_open_employee_list
msgid "Select Employee"
msgstr "選擇員工"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.js:0
msgid "Select Work Centers for this station"
msgstr "為此工作站選擇工作中心"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select a new work center"
msgstr "選擇新的工作中心"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Select the step you want to modify"
msgstr "選擇想要修改的步驟"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Serial"
msgstr "序列"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__set_picture
msgid "Set Picture"
msgstr "設置圖片"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Set a New picture"
msgstr "設置新圖片"

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.action_mrp_display
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_shop_floor
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_root
#: model:ir.ui.menu,name:mrp_workorder.menu_shop_floor
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_form_view
msgid "Shop Floor"
msgstr "工場"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "Shop Floor Control Panel"
msgstr "工場控制面板"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_routing_workcenter_tree_view_inherited
msgid "Show Instructions"
msgstr "顯示指示"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Show the timer on the work order screen"
msgstr "在工作單螢幕上顯示計時器"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Skip >"
msgstr "跳過 >"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/viewer.xml:0
msgid "Slides viewer"
msgstr "幻燈片顯示器"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool
msgid "Small wooden stool"
msgstr "小木凳"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__operation
msgid "Specific Page of Operation Worksheet"
msgstr "操作工作表的特定頁面"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__operation_id
msgid "Step"
msgstr "步驟"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__source_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__source_document
msgid "Step Document"
msgstr "步驟文件"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__step_id
msgid "Step to change"
msgstr "待更改步驟"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_mrp_workorder_show_steps
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_count
msgid "Steps"
msgstr "步驟"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_id
msgid "Stock Move"
msgstr "庫存移動"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr "庫存移動資料行"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool
msgid "Stool"
msgstr "凳子"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_foot
msgid "Stool Foot"
msgstr "凳腳"

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_top
msgid "Stool Top"
msgstr "凳面"

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.tablet_client_action
msgid "Tablet Client Action"
msgstr "平板電腦客戶端動作"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type
msgid "Technical name"
msgstr "技術名稱"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_type_id
msgid "Test Type"
msgstr "測試類型"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"操作類型系統允許您為每個庫存操作分配一個特定類型，該類型將相應地改變其檢視.\n"
"            在操作類型上，例如: 您可以指定是否預設需要打包，\n"
"            如果他應該向客戶顯示."

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "There is no session chief. Please log in."
msgstr "沒有操作時段負責人。請登入。"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_view_search_inherit_planning
msgid "This Station"
msgstr "此工作站"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"This workcenter isn't expected to have open workorders during this period. "
"Work hours :"
msgstr "在此期間，此工作中心不應有未完成工作單。工作小時數："

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "工時追蹤： %(user)s"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_tablet_timer
msgid "Timer"
msgstr "計時器"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__title
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__title
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Title"
msgstr "標題"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Title:"
msgstr "標題："

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "To Process"
msgstr "待處理"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Total Qty"
msgstr "總數量"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid ""
"Track work orders, show instructions and record manufacturing operations from here:\n"
"                                    quality control, consumed quantities, lot/serial numbers, etc."
msgstr ""
"此處可追蹤工作單、顯示說明指示，及記錄製造操作：\n"
"                                    品質控制、已消耗數量、批次/序號等。"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__product_tracking
msgid "Tracking"
msgstr "追蹤"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_workorder_form
msgid "Type"
msgstr "類型"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__change_type
msgid "Type of Change"
msgstr "變更類型"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_production.py:0
msgid ""
"Unable to load samples when you already have existing manufacturing orders"
msgstr "若已有現存的製造訂單，便不能載入範例"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Unblock"
msgstr "解除阻塞"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
msgid "Undo"
msgstr "復原"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Unit"
msgstr "單位"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Unit of Measure"
msgstr "量度單位"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/quality_check.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/stock_move.js:0
msgid "Units"
msgstr "單位"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.production_order_unplan_server_action
msgid "Unplan orders"
msgstr "取消訂單排期"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_uom_id
msgid "UoM"
msgstr "計量單位"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__update_step
msgid "Update Current Step"
msgstr "更新目前步驟"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.xml:0
msgid "Update Instructions"
msgstr "更新指示"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Upload your PDF file."
msgstr "上傳您的 PDF 檔。"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid ""
"Use steps to show instructions on a worksheet to operators, or trigger "
"quality checks at specific steps of the work order."
msgstr "利用步驟，向操作員顯示工作表中的說明，或在工作單特定步驟觸發品質檢查。"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Use the table work center control panel to register operations in the shop floor directly.\n"
"            The tablet provides worksheets for your workers and allow them to scrap products, track time,\n"
"            launch a maintenance request, perform quality tests, etc."
msgstr ""
"使用桌面工作中心控制面板，直接在車間登記操作。\n"
"            平板電腦為工人提供工作表，用於報廢產品、追蹤日期、\n"
"            發送維護保養請求、進行品質測試等。"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_quality_check_confirmation_dialog.js:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_workorder.js:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Validate"
msgstr "核實"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "上次掃瞄到的條碼的值."

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_bar.xml:0
msgid "WO"
msgstr "工作單"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display.xml:0
msgid "WO Filters"
msgstr "工作單篩選器"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/search_model.js:0
msgid "Waiting"
msgstr "正在等待"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_display_record.js:0
msgid "What do you want to do?"
msgstr "您想做什麼？"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_production_additional_workorder_wizard
msgid "Will be placed at the beginning if emtpy"
msgstr "若留空，將會放在起首"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production_additional_workorder__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_alert_view_search_inherit_mrp_workorder
msgid "Work Center"
msgstr "工作中心"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "工作中心使用情況"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "工作單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_tree
msgid "Work Order Operation"
msgstr "工作單操作"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_workorder_action_tablet
#: model:ir.ui.menu,name:mrp_workorder.mrp_workorder_menu_planning
msgid "Work Orders"
msgstr "工作單"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_production
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_workcenter
msgid "Work Orders Planning"
msgstr "工作單規劃"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_view_kanban_inherit_workorder
msgid "Work orders"
msgstr "工作單"

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"工作訂單是作為製造訂單的一部分執行的操作。\n"
"             操作在物料清單中定義或直接添加到製造訂單中."

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.stock_picking_type_view_kanban
msgid "Workcenter Control Panel"
msgstr "工作中心控制面板"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心產能日誌"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__working_state
msgid "Workcenter Status"
msgstr "工作中心狀態"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__employee_ids
msgid "Working employees"
msgstr "正在工作的員工"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__workorder_id
msgid "Workorder"
msgstr "工作單"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_worksheet_dialog.xml:0
#: code:addons/mrp_workorder/static/src/mrp_display/mrp_record_line/mrp_worksheet.xml:0
msgid "Worksheet"
msgstr "工作記錄表"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_page
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_page
msgid "Worksheet Page"
msgstr "工作記錄表頁面"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__worksheet_page
msgid "Worksheet page"
msgstr "工作記錄表頁面"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/hooks/employee_hooks.js:0
msgid "Wrong password!"
msgstr "密碼錯誤"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allow to work on some of these work orders."
msgstr "你未被允許處理部份工作單。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You are not allowed to work on the workorder"
msgstr "你未被允許處理該工作單"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/change_production_qty.py:0
msgid ""
"You cannot update the quantity to do of an ongoing manufacturing order for "
"which quality checks have been performed."
msgstr "不可更新正在進行、已完成品質檢查的生產訂單的待生產數量。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "You did not set a lot/serial number for the final product"
msgstr "您尚未為成品建立批/序號。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You must be logged in to process some of these work orders."
msgstr "若要處理部份這些工作單，必須登入。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to complete Quality Checks using the Shop Floor before marking Work"
" Order as Done."
msgstr "將工作單標記為完成前，你需要使用「工場」功能，完成品質檢查。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productive'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr "需要在「生產」類別中定義至少一個生產力損失。請在製造應用程式中建立，然後轉到選單：配置 / 生產力損失。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid ""
"You need to link this user to an employee of this company to process the "
"work order"
msgstr "此使用者須連結至此公司的員工，才可處理工作單"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You need to log in to process this work order."
msgstr "若要處理此工作單，必須登錄。"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You still need to do the quality checks!"
msgstr "您還是需要做品質檢查!"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/mrp_display/dialog/mrp_menu_dialog.js:0
msgid "Your suggestion to delete the %s step was succesfully created."
msgstr "你提議刪除 %s 步驟的建議，已成功建立。"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "back"
msgstr "返回"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter_productivity__employee_id
msgid "employee that record this working time"
msgstr "記錄此工作時間的員工"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter_productivity__employee_cost
msgid "employee_cost"
msgstr "employee_cost"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "employees with access"
msgstr "有權存取的員工"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workcenter__employee_ids
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__allowed_employees
msgid "if left empty, all employees can log in to the workcenter"
msgstr "若留空，所有員工均可登入至工作中心"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "menu"
msgstr "選單"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_form_view_inherit
msgid "per employee"
msgstr "每位員工"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_foot
msgid "wooden stool foot"
msgstr "木凳腳"

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_top
msgid "wooden stool top"
msgstr "木凳面"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__employee_ids
msgid "working employees"
msgstr "正在工作員工"
